"use strict";
import { Request, ResponseToolkit } from "@hapi/hapi";
import * as Jo<PERSON> from "joi";
import { failActionFunction } from "@utils/appUtils";
import { blessingControllerV1, blessingDaoV1 } from "@modules/blessings/index";
import {
	SWAGGER_DEFAULT_RESPONSE_MESSAGES,
	SERVER,
	REGEX,
	BLESSING_PERFORM_TYPE,
	BLESSING_TYPE,
	BLESSING_VOICEOVER,
	STATUS,
	FLAGGED_BLESSING_SORT_CRITERIA,
	FLAG_TYPE,
	WISH_INTESION
} from "@config/index";
import { responseHandler } from "@utils/ResponseHandler";
import { authorizationHeaderObj } from "@utils/validator";
export const blessingRoute = [
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/blessing/guided-blessing`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const payload: BlessingRequest.Add = request.payload;
				const result = await blessingControllerV1.blessingPerform(payload, tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "user/blessings"],
			description: "User perform blessing on wish",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
					wishId: Joi.string().trim().regex(REGEX.MONGO_ID).optional(),
					listenBlessingId: Joi.string().trim().regex(REGEX.MONGO_ID).required(),
					listenAudio: Joi.string().required(),
					listenDuration: Joi.number().required().description("in minutes").greater(0),
					audio: Joi.string().optional().description("user bless audio"),
					audioLength:Joi.number().optional(),
					notes: Joi.string().optional().description("user bless text"),
					type: Joi.string().valid(BLESSING_PERFORM_TYPE.AUDIO, BLESSING_PERFORM_TYPE.TEXT,BLESSING_PERFORM_TYPE.NONE).required(),
					location:Joi.object({
						address: Joi.string().trim().allow("").optional(),
						coordinates: Joi.array().required(),
						city:Joi.string().required(),
						country:Joi.string().required(),
						state:Joi.string().required()
					}).optional(), 
					atSignup:Joi.boolean().optional()

				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/admin/blessings/{id}`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const params: BlessingRequest.GuidedBlessingDetail = request.params;
				const result = await blessingControllerV1.guidedBlessingDetail(params);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin blessings"],
			description: "Guided blessing detail",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				params: Joi.object({
					id: Joi.string().required()
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "PUT",
		path: `${SERVER.API_BASE_URL}/v1/admin/blessings/{id}`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const params: BlessingRequest.GuidedBlessingDetail = request.params;
				const payload: BlessingRequest.EditGuidedBlessing = request.payload;
				const result = await blessingControllerV1.editGuidedBlessing({ ...params, ...payload });
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin blessings"],
			description: "Edit Guided blessing",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				params: Joi.object({
					id: Joi.string().required()
				}),
				payload: Joi.object({
					image: Joi.string().required(),
					name: Joi.string().required(),
					blessingType: Joi.string().required().valid(BLESSING_TYPE.MEDITATION, BLESSING_TYPE.PRAYER),
					language: Joi.string().required(),
					audioFile: Joi.string().required(),
					voiceover: Joi.string().required().valid(BLESSING_VOICEOVER.MASCULINE, BLESSING_VOICEOVER.FEMININE),
					authorName: Joi.string().required(),
					intension: Joi.string().required().valid(WISH_INTESION.ABUNDANCE, WISH_INTESION.HEALTH, WISH_INTESION.LOVE, WISH_INTESION.PEACE)
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/blessing/wishes-bless`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const query: BlessingRequest.WishBlessings = request.query;
				const result = await blessingControllerV1.wishesBlessings(query,tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "user/blessings"],
			description: "All Guided blessing of wish",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				query: Joi.object({
					wishId: Joi.string().required().description("wishId"),
					pageNo: Joi.number().optional().description("Page no"),
					limit: Joi.number().optional().description("limit"),
				
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/admin/flagged-blessings`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const query: BlessingRequest.FlaggedBlessing = request.query;
				const result = await blessingControllerV1.flaggedBlessings(query);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin flagged blessing"],
			description: "Flagged blessings",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				query: Joi.object({
					pageNo: Joi.number().required(),
					limit: Joi.number().required(),
					sortCriteria: Joi.string().trim().valid(
						FLAGGED_BLESSING_SORT_CRITERIA.BLESSED_BY,
						FLAGGED_BLESSING_SORT_CRITERIA.BLESSING_NUMBER,
						FLAGGED_BLESSING_SORT_CRITERIA.CREATED_AT,
						FLAGGED_BLESSING_SORT_CRITERIA.REPORT_COUNT,
						FLAGGED_BLESSING_SORT_CRITERIA.STATUS,
						FLAGGED_BLESSING_SORT_CRITERIA.WISH_NUMBER
					).optional().default(FLAGGED_BLESSING_SORT_CRITERIA.CREATED_AT),
					sortBy: Joi.number().valid(1, 0).optional(),
					searchKey: Joi.string().optional(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "DELETE",
		path: `${SERVER.API_BASE_URL}/v1/admin/blessings/{id}`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const params: BlessingRequest.EditGuidedBlessing = request.params;
				const query: BlessingRequest.EditGuidedBlessing = request.query;
				const result = await blessingControllerV1.editGuidedBlessingStatus({ ...query, ...params });
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin blessings"],
			description: "Edit Guided blessing status",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				params: Joi.object({
					id: Joi.string().required()
				}),
				query: Joi.object({
					status: Joi.string().required().valid(STATUS.ACTIVE, STATUS.IN_ACTIVE, STATUS.DELETED),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/admin/perform-blessings/{id}`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const params: BlessingRequest.GuidedBlessingDetail = request.params;
				const result = await blessingControllerV1.performBlessingDetail(params);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin flagged blessing"],
			description: "Perform blessing detail",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				params: Joi.object({
					id: Joi.string().required()
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},

    {
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/admin/flagged-blessing-reports`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const query: BlessingRequest.FlaggedBlessing = request.query;
				const result = await blessingControllerV1.flaggedBlessingReports(query);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin flagged blessing"],
			description: "Flagged blessing reports",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				query: Joi.object({
					blessingId: Joi.string().required(),
					pageNo: Joi.number().required(),
					limit: Joi.number().required(),
					sortCriteria: Joi.string().trim().valid("createdAt").optional(),
					sortBy: Joi.number().valid(1, 0).optional(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "PUT",
		path: `${SERVER.API_BASE_URL}/v1/admin/perform-blessings/{id}`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const params: BlessingRequest.UnflagDeletePerformBlessing = request.params;
				const payload: BlessingRequest.UnflagDeletePerformBlessing = request.payload;
				const result = await blessingControllerV1.unflagDeletePerformBlessing({ ...params, ...payload }, tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin flagged blessing"],
			description: "Unflag/Delete perform blessing",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				params: Joi.object({
					id: Joi.string().required()
				}),
				payload: Joi.object({
					status: Joi.string().required().valid(FLAG_TYPE.UN_FLAGGED, FLAG_TYPE.DELETED),
					deleteReason: Joi.string().optional().allow("")
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/blessing/well`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const query:BlessingRequest.WellLog = request.query;
				const result = await blessingControllerV1.wellBlessLogs(query,tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "user/blessings"],
			description: "list of well for well now",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				query: Joi.object({
					pageNo: Joi.number().required(),
					limit: Joi.number().required(),
					phoneNumber: Joi.string().optional()
					
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
]