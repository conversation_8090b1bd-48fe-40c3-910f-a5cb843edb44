"use strict";
import * as _ from "lodash";
import { BaseDao, baseDao } from "@modules/baseDao/BaseDao";
import { DB_MODEL_REF, LOGIN_TYPE, STATUS, UPDATE_TYPE, USER_SORT_CRITERIA,USER_TYPE,WISH_TAG_TYPE } from "@config/constant";
import { escapeSpecialCharacter, toObjectId } from "@utils/appUtils";
import { Types } from "mongoose";
export class UserDao extends BaseDao {
   /**
	 * @function isEmailExists
	 */
	async isEmailExists(params, userId?: string) {
		try {
			const query: any = {};
			query.email = params.email;
			if (userId) query._id = { "$not": { "$eq": userId } };
			query.status = { "$ne": STATUS.DELETED };
			const projection = { updatedAt: 0 };
			return await this.findOne("users", query, projection);
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function isMobileExists
	 */
	async isMobileExists(params, userId?: string) {
		try {
			const query: any = {};
			query.countryCode = params.countryCode;
			query.mobileNo = params.phoneNumber;
			if (userId) query._id = { "$not": { "$eq": userId } };
			query.status = { "$ne": STATUS.DELETED };
			const projection = { _id: 1 };
			return await this.findOne("users", query, projection);
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function signUp
	 */
	async signUp(params: UserRequest.SignUp, session?) {
		try {
			return await this.save("users", params, { session });
		} catch (error) {
			throw error;
		}
	}
	/**    
	 * @function findUserById
	 */
	async findUserById(userId: string, project = {}) {
		try {
			const query: any = {};
			query._id = toObjectId(userId);
			query.status = { "$ne": STATUS.DELETED };
			const projection = (Object.values(project).length) ? project : { createdAt: 0, updatedAt: 0 };
			return await this.findOne("users", query, projection);
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function changePassword   
	 */
	async changePassword(params: UserRequest.ChangeForgotPassword, tokenData?: TokenData) {
		try {
			const query: any = {};
			if(params?.resetToken) {
				query.passwordResetToken = params.resetToken;
			}
			if(tokenData?.userId) {
				query._id = tokenData.userId;
			}
			let updateData = {};
			updateData = {
				hash: params.hash
			};
			if(params.salt){
				updateData = {
					...updateData,
					salt: params.salt
				};
			}
			const update = {
				$set: updateData
			};
			return await this.updateOne("users", query, update, {});
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function userList
	 */
	async userList(params: AdminRequest.UserListing) {
		try {
			const aggPipe = [];
			aggPipe.push({
				"$project": {
					_id: 1, userType: 1, name: 1, email: 1, profilePicture: 1, onboardingProgress: 1, isApproved: 1, status: 1,
					created: 1, ndisNumber: { "$convert": { input: "$ndisPlanDetails.number", to: "string" } }, createdAt: 1
				}
			});
			const match: any = {};
			if (params.userType) match.userType = params.userType;
			if (params.searchKey) {
				params.searchKey = escapeSpecialCharacter(params.searchKey);
				match["$or"] = [
					{ name: { "$regex": params.searchKey, "$options": "i" } },
					{ email: { "$regex": params.searchKey, "$options": "i" } },
					{ ndisNumber: { "$regex": params.searchKey, "$options": "i" } }
				];
			}
			if (params.status)
				match.status = params.status;
			else
				match.status = { "$ne": STATUS.DELETED };
			if (params.fromDate && !params.toDate) match.created = { "$gte": params.fromDate };
			if (params.toDate && !params.fromDate) match.created = { "$lte": params.toDate };
			if (params.fromDate && params.toDate) match.created = { "$gte": params.fromDate, "$lte": params.toDate };
			if (params.latestUsers) match.createdAt = { "$gte": new Date(new Date().setHours(0, 0, 0, 0)), "$lt": new Date(new Date().setHours(23, 59, 59, 999)) };
			aggPipe.push({ "$match": match });
			let sort = {};
			(params.sortBy && params.sortOrder) ? sort = { [params.sortBy]: params.sortOrder } : sort = { created: -1 };
			aggPipe.push({ "$sort": sort });
			const options = { collation: true };
			aggPipe.push({
				"$project": {
					_id: 1, userType: 1, name: 1, email: 1, profilePicture: 1, onboardingProgress: 1, isApproved: 1, status: 1,
					created: 1, ndisNumber: 1
				}
			});
			let pageCount = true;
			if (params.latestUsers) pageCount = false;
			return await this.paginate("users", aggPipe, params.limit, params.pageNo, options, pageCount);
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function blockUnblock
	 */
	async blockUnblock(params: BlockRequest) {
		try {
			const query: any = {};
			query._id = params.userId;
			const update = {};
			update["$set"] = {
				status: params.status,
				updateType: UPDATE_TYPE.BLOCK_UNBLOCK
			};
			const options = { new: true };
			return await this.findOneAndUpdate("users", query, update, options);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function verifyUser
	 */
	async verifyUser(params: UserRequest.VerifyUser) {
		try {
			const query: any = {};
			query._id = params.userId;
			const update = {};
			update["$set"] = params;
			const options = { new: true };
			return await this.findOneAndUpdate("users", query, update, options);
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function editProfile
	 */
	async editProfile(params, userId: string, profileSteps?: string[]) {
		try {
			const query: any = {};
			query._id = userId;
			const update = {};
			if (Object.values(params).length) update["$set"] = params;
			if (!params.updateType) update["$unset"] = { updateType: "" };
			if (profileSteps && profileSteps.length) {
				update["$addToSet"] = { profileSteps: { "$each": profileSteps } };
				update["$pullAll"] = { skipSteps: profileSteps };
			}
			const options = { new: true };
			return await this.findOneAndUpdate("users", query, update, options);
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function notificationList
	 */
	async notificationList(params: UserRequest.NotificationList, tokenData) {
		try {
			const aggPipe = [];

			const match: any = {};
			match.status = { "$ne": STATUS.DELETED };
			match.receiverId = toObjectId(tokenData.userId);
			aggPipe.push({

				"$lookup": {
					"from": "users",
					"let": { "senderId": "$senderId" },
					"pipeline": [
						{ "$match": { "$expr": { "$eq": ["$_id", "$$senderId"] } } },
						{ "$project": { "firstName": 1, "lastName": 1, "profilePicture": 1, "userType": 1 } }
					],
					"as": "user_detail"
				}

				//"$project": { firstName: 1, lastName: 1, profilePicture: 1 }
			});
			aggPipe.push({

				"$lookup": {
					"from": "friends",
					"let": { "requestId": "$requestId" },
					"pipeline": [
						{ "$match": { "$expr": { "$eq": ["$_id", "$$requestId"] } } },
						{ "$project": { "status": 1 } }
					],
					"as": "friendStatus"
				}

				//"$project": { firstName: 1, lastName: 1, profilePicture: 1 }
			});
			aggPipe.push(
				{ "$unwind": { path: '$friendStatus', preserveNullAndEmptyArrays: true } },
			)

			aggPipe.push({ "$match": match });
			aggPipe.push({ "$sort": { createdAt: -1 } });

			const options = { collation: true };

			let response = await this.paginate("notifications", aggPipe, params.limit, params.pageNo, options, false);
			let counData = await this.aggregate("notifications", aggPipe, {});

			response.total = counData.length;
			return response;
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function notificationList
	 */
	async adminNotificationList(params: UserRequest.NotificationList, tokenData) {
		try {
			const aggPipe = [];

			const match: any = {};
			match.status = { "$ne": STATUS.DELETED };
			match.receiverId = toObjectId(tokenData.userId);
			aggPipe.push({

				"$lookup": {
					"from": "users",
					"let": { "senderId": "$senderId" },
					"pipeline": [
						{ "$match": { "$expr": { "$eq": ["$_id", "$$senderId"] } } },
						{ "$project": { "firstName": 1, "lastName": 1, "profilePicture": 1, "userType": 1 } }
					],
					"as": "user_detail"
				}

				//"$project": { firstName: 1, lastName: 1, profilePicture: 1 }
			});
			aggPipe.push({

				"$lookup": {
					"from": "activities",
					"let": { "activityId": "$activityId" },
					"pipeline": [
						{ "$match": { "$expr": { "$eq": ["$_id", "$$activityId"] } } },
						{ "$project": { "details": 1, "activityType": 1, "startTime": 1, "photo": 1 } }
					],
					"as": "activity_detail"
				}

				//"$project": { firstName: 1, lastName: 1, profilePicture: 1 }
			});
			aggPipe.push(
				{ "$unwind": { path: '$friendStatus', preserveNullAndEmptyArrays: true } },
			)

			aggPipe.push({ "$match": match });
			aggPipe.push({ "$sort": { createdAt: -1 } });

			const options = { collation: true };

			let response = await this.paginate("notifications", aggPipe, params.limit, params.pageNo, options, false);
			let counData = await this.aggregate("notifications", aggPipe, {});

			response.total = counData.length;
			return response;
		} catch (error) {
			throw error;
		}
	}
	
	/**    
	 * @function updateSocialData
	 * update the user data if user re-login with some social accounts
	 */
	async updateSocialData(params, existingData) {
		try {
			const query: any = {};
			query['_id'] = existingData._id;
			if (params.name) params['socialData.name'] = params.name;
			if (params.profilePicture) params['socialData.profilePic'] = params.profilePicture;
			params['socialData.socialId'] = params.socialId;
			if(params.email)params['socialData.email'] = params.email;
			if(params.loginType==LOGIN_TYPE.APPLE) params['appleSocialId'] = params.socialId;
			if(params.loginType==LOGIN_TYPE.GOOGLE) params['googleSocialId'] = params.socialId;
		return await this.findOneAndUpdate("users", query, params, {new: true});
		} catch (error) {
			throw error;
		}
	}

	/**
	* @function isUserExists
	* @description checks if the user is already exists
	*/
	async isSocialIdExists(params, userId?: string) {
		try {
			const query: any = {};
			if(params.loginType==LOGIN_TYPE.APPLE) query['appleSocialId'] = params.socialId;
			if(params.loginType==LOGIN_TYPE.GOOGLE) query['googleSocialId'] = params.socialId;
			query['socialData.socialId'] = params.socialId;
			if (userId) query._id = { "$not": { "$eq": userId } };
			query.status = { "$ne": STATUS.DELETED };
			const projection = { updatedAt: 0 };
			return await this.findOne("users", query, projection);
		} catch (error) {
			throw error;
		}

	}

	/**
	 * @function registration
	 */
	async registration(params, userId: string) {
		try {
			const query: any = {};
			query['_id'] = toObjectId(userId);
			query['status'] = { "$ne": STATUS.DELETED };
			params['name'] = params.firstName+" "+params.lastName;
			return await this.findOneAndUpdate("users", query, params, {new: true});
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function getUserMatchContacts
	 */
	async getUserMatchContacts(resultrepo) {
		try {
			const query: any = {};

			query["$or"] = [
				{
					'phoneNumber':  { '$in': resultrepo }
				},
				{ 
					'fullPhoneNumber' : { '$in': resultrepo } 
				}
			];
			//query['phoneNumber'] =  { '$in': resultrepo }
			query['status'] = { '$ne': STATUS.DELETED}
			const projection = { _id:1, email:1, status:1, name:1,profilePicture:1,firstName:1,lastName:1,phoneNumber:1,fullPhoneNumber:1};
			return await this.find("users", query, projection);
		}catch (error) {
			throw error;
		}
	}
	
	/**
	 * @function userSearch
	 */
	async userSearch(params: ListingRequest, tokenData: TokenData) {
		try {
			const aggPipe = [];
			const match: any = {};
			match.status = { "$ne": STATUS.DELETED };
			match.firstName = { $exists: true, $ne: null };
			match._id = { "$ne": toObjectId(tokenData.userId) };
			aggPipe.push({ "$match": match });
			if (params.searchKey) {
				params.searchKey = escapeSpecialCharacter(params.searchKey);
				match["$or"] = [
					{ firstName: { "$regex": params.searchKey, "$options": "i" } },
					{ lastName: { "$regex": params.searchKey, "$options": "i" } },
				];
			}
			aggPipe.push({ "$sort": { createdAt: -1 } });
			aggPipe.push({"$project":{_id:1,name:1,firstName:1,lastName:1,profilePicture:1}})
			const options = { collation: true };
			let response = await this.paginate("users", aggPipe, params.limit, params.pageNo, options, false);
			let counData = await baseDao.countDocuments("users",match)
			response.total = counData.length;
			return response;
		} catch (error) {
			throw error;
		}
	}

	/**    
	 * @function findContactById
	 */
	async findContactById(userId: string, project = {}) {
		try {
			const query: any = {};
			query._id = toObjectId(userId);
			query.status = { "$ne": STATUS.DELETED };
			const projection = (Object.values(project).length) ? project : { createdAt: 0, updatedAt: 0 };
			return await this.findOne("contacts_v2", query, projection);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function userContacts
	 */
	async userContacts(params: ListingRequest, tokenData, users?) {
		try {
			console.log(tokenData,'tokenData')
			const aggPipe = [];
			const match: any = {};
			match.status = { "$ne": STATUS.DELETED };
			match.userId = toObjectId(tokenData.userId);
			match.contactUserId = { "$nin": users };
			match.name  = {$exists:true};
			if(params.deviceId)match.deviceId = params.deviceId;
			if(params.type=="2")match.isAppUser=true;			
			aggPipe.push({
    
				"$lookup": {
					"from": "users",
					"let": { "userId": "$contactUserId" },
					"pipeline": [
						{ "$match": { "$expr": 
										   {'$and':[
												{ "$eq": ["$_id", "$$userId"] },											
											]} } },
						{ "$project": { "firstName": 1,"lastName":1, "profilePicture":1,name:1 } }
					],
					"as": "userDetail"
				}

			});
			aggPipe.push({
                '$unwind': {
                    "path": "$userDetail",
                    "preserveNullAndEmptyArrays": true
                }
            });
			if (params.searchKey) {
				params.searchKey = escapeSpecialCharacter(params.searchKey);
				match["$or"] = [
					{ name: { "$regex": params.searchKey, "$options": "i" } },
					{ 'userDetail.firstName': { "$regex": params.searchKey, "$options": "i" } },
					{ 'userDetail.lastName': { "$regex": params.searchKey, "$options": "i" } },
					{ 'userDetail.name': { "$regex": params.searchKey, "$options": "i" } },


				];
			}
			aggPipe.push({ "$match": match });

			aggPipe.push({
				"$group": {
					"_id": "$phoneNumber", // Grouping by phoneNumber
					"firstContact": { "$first": "$$ROOT" } // Selecting the first document within each group
				}
			});
	
			aggPipe.push({ "$replaceRoot": { "newRoot": "$firstContact" } }); // Replace root with the selected documents

			aggPipe.push({ "$sort": { isAppUser: -1, "userDetail.firstName": 1, name: 1 } });
			aggPipe.push({"$project":{_id:1,name:1,firstName:1,lastName:1,profilePicture:1,isAppUser:1,countryCode:1,phoneNumber:1,contactUserId:1,userDetail:1}})
			const options = {};
			//let counData = await baseDao.countDocuments("contacts",{})
			// let counData = await this.aggregate("contacts", aggPipe);
            // let total = counData.length;
			let response = await this.paginate("contacts", aggPipe, params.limit, params.pageNo, options, true);
		    // response.total = total;
			return response;
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function userContactsV2
	 */
	async userContactsV2(params: ListingRequest, tokenData, users?) {
		try {
			console.log(tokenData, 'tokenData');
			const aggPipe = [];
			const match: any = {};
			match.status = { "$ne": STATUS.DELETED };
			match.userId = toObjectId(tokenData.userId);

			if (params.deviceId) match.deviceId = params.deviceId;
			if (params.type == "2") match.isAppUser = true;

			aggPipe.push({
				"$match": match
			});

			aggPipe.push({
				"$unwind": "$phoneNumbers" // Unwind the phoneNumbers array
			});

			aggPipe.push({
				"$lookup": {
					"from": "users",
					"let": { "userId": "$phoneNumbers.contactUserId" },
					"pipeline": [
						{ "$match": { "$expr": { "$eq": ["$_id", "$$userId"] } } },
						{ "$project": { "firstName": 1, "lastName": 1, "profilePicture": 1, "name": 1 } }
					],
					"as": "phoneNumbers.userDetail"
				}
			});

			aggPipe.push({
				"$unwind": {
					"path": "$phoneNumbers.userDetail",
					"preserveNullAndEmptyArrays": true
				}
			});

			if (params.searchKey) {
				params.searchKey = escapeSpecialCharacter(params.searchKey);
				aggPipe.push({
					"$match": {
						"$or": [
							{ name: { "$regex": params.searchKey, "$options": "i" } },
							{ 'phoneNumbers.userDetail.firstName': { "$regex": params.searchKey, "$options": "i" } },
							{ 'phoneNumbers.userDetail.lastName': { "$regex": params.searchKey, "$options": "i" } },
							{ 'phoneNumbers.userDetail.name': { "$regex": params.searchKey, "$options": "i" } },
						]
					}
				});
			}

			aggPipe.push({
				"$match": {
					"$or": [
						{ "isAppUser": false, "phoneNumbers.default": true }, // If isAppUser is false, match default phone number
						{ "isAppUser": true, "phoneNumbers.connectedToAccount": true } // If isAppUser is true, match connectedToAccount phone numbers
					]
				}
			});

			aggPipe.push({
				"$group": {
					"_id": "$_id", // Group by the contact ID
					"contact": { "$first": "$$ROOT" }, // Take the first document in each group
					"phoneNumbers": { "$push": "$phoneNumbers" } // Push matched phone numbers into an array
				}
			});

			aggPipe.push({
				"$replaceRoot": {
					"newRoot": {
						"$mergeObjects": [
							"$contact",
							{ "phoneNumbers": "$phoneNumbers" } // Replace the phoneNumbers array with the filtered phone numbers
						]
					}
				}
			});

			aggPipe.push({ "$sort": { isAppUser: -1, "phoneNumbers.userDetail.firstName": 1, name: 1 } });

			aggPipe.push({
				"$project": {
					_id: 1,
					name: 1,
					firstName: 1,
					lastName: 1,
					profilePicture: 1,
					isAppUser: 1,
					phoneNumbers: 1,
					contactUserId: 1,
				}
			});

			const options = {};
			let response = await this.paginate("contacts_v2", aggPipe, params.limit, params.pageNo, options, true);

			return response;
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function addContactSync
	 */
	async addContactSync(params) {
		try {
			return await this.insertMany("contacts", params, {});
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function addContactSyncV2
	 */
	async addContactSyncV2(params) {
		try {
			return await this.insertMany("contacts_v2", params, {});
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function createContactSyncV2
	 */
	async createContactV2(params) {
		try {
			return await this.insert("contacts_v2", params, {});
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function updateContactV2
	 */
	async updateContactV2(params) {
		try {
		const { _id, ...updateParams } = params;
		return await this.findOneAndUpdate("contacts_v2", { _id }, updateParams, { new: true });
		} catch (error) {
		throw error;
		}
	}

	/**
	 * @function findContactV2
	 */
	async findContactV2(query) {
		try {
			return await this.findOne("contacts_v2", query, {});
		} catch (error) {
			throw error;
		}
	}

	async deleteContactByIdV2(contactId) {
		try {
			return await this.deleteOne("contacts_v2", { _id: contactId });
		} catch (error) {
			throw error;
		}
	}
		/**
	 * @function getContacts
	 */
		async getContacts(resultrepo,userId, params) {
			try {
				const query: any = {};
				query['userId'] =  toObjectId(userId);
				query['phoneNumber'] =  { '$in': resultrepo }
				query['status'] = { '$ne': STATUS.DELETED}
				if(params)query["deviceId"] = params.deviceId;
				const projection = { _id:1};
	
				return await this.find("contacts", query, projection);
			}catch (error) {
				throw error;
			}
		}

	/**
	 * @function getContactsV2
	 * @description Retrieves contacts based on user ID, phone numbers, and optional device ID
	 */
	async getContactsV2(resultrepo: string[], userId: string, params?: { deviceId?: string }) {
		try {
			const query: any = {
				userId: toObjectId(userId),
				'phoneNumbers.phoneNumber': { '$in': resultrepo },
				status: { '$ne': STATUS.DELETED }
			};

			if (params && params.deviceId) {
				query.deviceId = params.deviceId;
			}

			const projection = {
				_id: 1,
				userId: 1,
				name: 1,
				profilePicture: 1,
				'phoneNumbers.$': 1, // Include matched phoneNumbers only
				status: 1,
				deviceId: 1
			};

			return await this.find("contacts_v2", query, projection);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function findCountriesWithPhoneCode
	 */
	async getPhoneCodes() {
		try {
			const query = {
				$and: [
				{ phone_code: { $ne: null } },
				{ phone_code: { $ne: "" } }
				]
			};
			const projection = {
				_id: 0,
				phone_code: 1
			};
			return await this.find("countries", query, projection);
		} catch (error) {
			throw error;
		}
	}  

	/**
	 * @function addFavourites
	 */
	async addFavourites(params) {
		try {
			return await this.save("favourites", params);
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function removeFavourites
	 */
	async removeFavourites(params) {
		try {
			const query: any = {};
			query['userId'] =  toObjectId(params.userId);
			query['wishId'] =  toObjectId(params.wishId);
			query['status'] = { '$ne': STATUS.DELETED}
			return await this.findOneAndUpdate("favourites", query,{status:STATUS.DELETED}, {returnDocument: 'after'});  //returnDocument: 'after'
		}catch (error) {
			throw error;
		}
	}

	/**
	 * @function getFavourite
	 */
	async getFavourite(params,userId,reportList?) {
		try {
			const aggPipe = [];
			const match: any = {};
			match['userId'] = toObjectId(userId)
			match['status'] = { '$eq': STATUS.ACTIVE}
			if(reportList && reportList.length) match['wishId'] ={ '$nin': reportList}
			if(params.communityId){
				let community = params.communityId.split(',')
				params.communityId = community
				for(let i = 0; params.communityId.length > i; i++){
					params.communityId[i]=toObjectId(params.communityId[i])
				}
				aggPipe.push({
					"$lookup": {
						"from": "tagwishes",
						"let": { "wishId": "$wishId" },
	
						"pipeline": [
							{
								"$match": {
	
									"$expr":
									{
										$and: [
											{
												"$eq": ["$wishId", "$$wishId"]
											},
											{
												"$eq": ["$userId", toObjectId(userId)]
											},
											{
												'$in':  ["$status", [STATUS.ACTIVE]]
											},
											{
												'$in':  ["$communityId", params.communityId]
											},
											// {
											// 	'$eq': ['$isDuplicate', false]
											// }	
										]
									}
	
							 },
							},
							{ "$project": {_id:1,status:1,communityId:1,userId:1, lastBlessTime:1
							} }
						],
						"as": "taggedWishDetail"
					}
				});
				aggPipe.push({
					'$unwind': {
						"path": "$taggedWishDetail",
						"preserveNullAndEmptyArrays": false     
					}
				});
			}else{
				aggPipe.push({
					"$lookup": {
						"from": "tagwishes",
						"let": { "wishId": "$wishId" },
	
						"pipeline": [
							{
								"$match": {
	
									"$expr":
									{
										$and: [
											{
												"$eq": ["$wishId", "$$wishId"]
											},
											{
												"$eq": ["$userId", toObjectId(userId)]
											},
											{
												'$in':  ["$status", [STATUS.ACTIVE]]
											}
										]
									}
	
							 },
							},
							{ "$project": {_id:1,status:1,communityId:1,userId:1, lastBlessTime:1
							} }
						],
						"as": "taggedWishDetail"
					}
				});
				aggPipe.push({
					'$unwind': {
						"path": "$taggedWishDetail",
						"preserveNullAndEmptyArrays": true     // for global wish to be seen
					}
				});

			}
			aggPipe.push({
				"$lookup": {
					"from": "wishes",
					"let": { "wishId": "$wishId" },

					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$_id", "$$wishId"]
										},
										{
											'$in':  ["$status", [STATUS.ACTIVE,STATUS.PENDING]]
										}
									]
								}

						 },
						},
						{ "$project": {_id:1, title:1,description:1,created:1,userId:1,hostId:1,image:1,userDetail:1,isGlobalWish:1,status:1,totalBlessings:1,userLastBlessTime:1,hostLastBlessTime:1,
						} }
					],
					"as": "wishDetail"
				}
			});
			aggPipe.push({
				'$unwind': {
					"path": "$wishDetail",
					"preserveNullAndEmptyArrays": false
				}
			});
			aggPipe.push({
				"$lookup": {
					"from": "perform_blessings",
					"let": { "wishId": "$wishId" },
					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$wishId", "$$wishId"]
										},
										{
											"$eq": ["$userId", toObjectId(userId)]
										},
										{
											"$eq": ["$status", STATUS.ACTIVE]
										}
									]
								}

						 },
						},
						{ "$project": {_id:1,createdAt:1 }}
					],
					"as": "blessingData"
				}
			});
			// aggPipe.push({
			// 	'$unwind': {
			// 		"path": "$blessingData",
			// 		"preserveNullAndEmptyArrays": true
			// 	}
			// });
			aggPipe.push({
				"$lookup": {
					"from": "users",
					"let": { "hostId": "$wishDetail.hostId" },

					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$_id", "$$hostId"]
										}
									]
								}
						 },
						},
						{ "$project": {_id:0,  "profilePicture": 1,"firstName": 1,"lastName": 1
						} }
					],
					"as": "wishDetail.hostDetail"
				}
			});
			aggPipe.push({
				'$unwind': {
					"path": "$wishDetail.hostDetail",
					"preserveNullAndEmptyArrays": true
				}
			});
			aggPipe.push({
				"$lookup": {
					"from": "pinned_wishes",
					"let": { "subjectId": "$wishId" },
					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$subjectId", "$$subjectId"]
										},
										{
											"$eq": ["$userId", toObjectId(userId)]
										},
										{
											"$eq": ["$status", STATUS.ACTIVE]
										}
									]
								}

						 },
						},
						{ "$project": {_id:1,subjectId:1} }
					],
					"as": "pinnedWishes"
				}
			});
			aggPipe.push({
				'$unwind': {
					"path": "$pinnedWishes",
					"preserveNullAndEmptyArrays": true
				}
			});
			aggPipe.push({
				"$lookup": {
					"from": "tagwishes",
					"let": { "wishId": "$wishId" },
					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$wishId", "$$wishId"]
										},
										{
											"$eq": ["$status", STATUS.ACTIVE]
										},
										{
											"$eq": ["$type", WISH_TAG_TYPE.COHOST]
										}
									]
								}

						 },
						},
						{ "$project": {_id:1,userId:1} }
					],
					"as": "wishDetail.cohosts"
				}
			});
			if (params.searchKey) {
				params.searchKey = escapeSpecialCharacter(params.searchKey);
				match["$or"] = [
					{ 'wishDetail.description': { "$regex": params.searchKey, "$options": "i" } },
					{ "wishDetail.userDetail.firstName": { "$regex": params.searchKey, "$options": "i" } },
					{ "wishDetail.hostDetail.firstName": { "$regex": params.searchKey, "$options": "i" } }
				];
			}
			aggPipe.push({ "$match": match });
			aggPipe.push({
				"$project": {
					_id:1,wishDetail:1,userId:1,wishId:1,created:1,pinnedWishes:1,status:1,taggedWishDetail:1,lastBlessTime:{ $arrayElemAt: ["$blessingData.createdAt", -1] },
					pinned: {
						$cond: [
						  {$ifNull: ["$pinnedWishes", false]},
						  1,
						  0
						]
					  }
				
				}
			});
			aggPipe.push({ "$sort": { pinned: -1,created: -1} });
			const options = {};
			// let counData = await this.aggregate("favourites", aggPipe);
            // let total = counData.length;
			let response = await this.paginate("favourites", aggPipe, params.limit, params.pageNo, options, true);
		    // response.total = total;
			return response;
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function userListing
	 */
	async userListing(params: AdminRequest.UserList) {
		try {
			let match: any = {};
			let aggPipe: any = [];
			let sort: any = {};

			match["isApproved"] = true;

			if(params.searchKey) {
				match["$expr"] = {
					$or: [{
						$regexMatch: {
							input: { $concat: ['$firstName', ' ', '$lastName'] },
							regex: params.searchKey,
							options: 'i'
						}
					}]
				}
			}

			// aggPipe.push({ "$match": match });

			aggPipe.push({ "$match": {
				"$and": [
					match,	// Existing match conditions
					{ "firstName": { "$ne": null } },
					{ "firstName": { "$ne": "" } }
				]
			} });

			const thankWishwellLookup = {
				from: DB_MODEL_REF.WISHWELL_THANKS,
				localField: "_id",
				foreignField: "userId",
				as: "thankWishwell",
				pipeline: [
					{
						$project: {
							_id: 0,
							amount: 1
						}
					},
					{
						$match: {
							amount: {
								$exists: true,
								$gt: 0
							}
						}
					}
				]
			}

			aggPipe.push({ $lookup: thankWishwellLookup });

			const project = {
				_id: 1,
				status: 1,
				firstName: 1,
				lastName: 1,
				donationCount: 1,
				streakCount: 1,
				wishCount: 1,
				blessingCount: 1,
				gratitudeCount: 1,
				communityCount: 1,
				communityReportCount: 1,
				wishReportCount: 1,
				blessingReportCount: 1,
				gratitudeReportCount: 1,
				createdAt: 1,
				fullName: {
					$concat: [
						{ $ifNull: ['$firstName', ''] },
						' ',
						{ $ifNull: ['$lastName', ''] }
					]
				}
			}

			aggPipe.push({ "$project": project });

			if (params.sortBy == 1) {
				params.sortBy = 1;
			} else {
				params.sortBy = -1;
			}

			if (params.sortCriteria == USER_SORT_CRITERIA.NAME) {
				sort = { "fullName": params.sortBy }
			} else if (params.sortCriteria == USER_SORT_CRITERIA.STATUS) {
				sort = { "status": params.sortBy }
			} else {
				sort = { "createdAt": params.sortBy }
			}

			aggPipe.push({ "$sort": sort });
			
			const options = { collation: true };
			return this.paginate("users", aggPipe, params.limit, params.pageNo, options, true);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function userDetails
	 */
	async userDetails(params: AdminRequest.UserDetail) {
		try {
			const query: any = {};
			query["_id"] = Types.ObjectId(params.id);

			const project = {
				_id: 1, firstName: 1, lastName: 1, profilePicture: 1, countryCode: 1, phoneNumber: 1, fullPhoneNumber: 1, email: 1, oldEmail: 1, status: 1, createdAt: 1, donationCount: 1, streakCount: 1, wishCount: 1, blessingCount: 1, gratitudeCount: 1, communityCount: 1, communityReportCount: 1, wishReportCount: 1, blessingReportCount: 1, gratitudeReportCount: 1, "actionSummary.reason": 1, "actionSummary.status": 1, "actionSummary.createdAt": 1, deletedAt: 1, adminDeleteReason: 1, deleteReason: 1, lastSeen: 1
			};
			const userDetail = await this.findOne("users", query, project);
			userDetail?.actionSummary?.sort((a, b) => b.createdAt - a.createdAt);
			const lastSeen = await baseDao.findOne("login_histories", { "userId._id": Types.ObjectId(params.id), isLogin: false }, { lastLogin: 1 }, {}, { createdAt: -1 });
			const communityJoinedCount = await this.countDocuments("members", { userId: Types.ObjectId(params.id), status: STATUS.ACTIVE });
			userDetail.communityJoinedCount = communityJoinedCount;
			const donationCount = await this.countDocuments("wishwell_thanks", { amount: { $exists: true, $gt: 0 }, userId: Types.ObjectId(params.id) });
			const noteCount = await this.countDocuments("wishwell_thanks", { userId: Types.ObjectId(params.id) });
			const deletedWish = await this.countDocuments("wishes", { flagStatus: STATUS.DELETED, userId: Types.ObjectId(params.id) });
			const deletedBlessing = await this.countDocuments("perform_blessings", { flagStatus: STATUS.DELETED, userId: Types.ObjectId(params.id) });
			const deletedGratitude = await this.countDocuments("gratitudes", { flagStatus: STATUS.DELETED, userId: Types.ObjectId(params.id) });;
			userDetail.deletedPostCount = deletedWish + deletedBlessing + deletedGratitude;
			userDetail.donationCount = donationCount;
			userDetail.noteCount = noteCount;
			// userDetail.lastSeen = lastSeen?.lastLogin ? new Date(lastSeen?.lastLogin) : "";
			// userDetail.testKey = "TEST";
			return userDetail;
		} catch (error) {
			throw error
		}
	}

	/**
	 * @function editUser
	 */
	async editUser(params: AdminRequest.EditUser, tokenData: TokenData) {
		try {
			const query: any = {};
			const update: any = {};
			query["_id"] = Types.ObjectId(params.id);
			if(params.status !== STATUS.DELETED) {
				update["$set"] = { status: params.status };
				update["$push"] = {
					actionSummary: {
						adminId: tokenData.userId,
						status: params.status,
						reason: params.reason
					}
				}
			} else {
				update["$set"] = { 
					status: params.status, 
					deletedAt: Date.now(),
					adminDeleteReason: params.reason
				};
			}

			return await this.update("users", query, update, {});
		} catch (error) {
			throw error;
		}
	}

	/**
	 *@detail 
	 */

	 /**
	 * @function profileDetail
	 */
	async profileDetail(userId) {
		try {
			const query: any = {};
			query["_id"] = userId;
			const project = {
				_id: 1, firstName: 1, lastName: 1, profilePicture: 1, 
				countryCode: 1, phoneNumber: 1, fullPhoneNumber: 1, 
				email: 1, status: 1, createdAt: 1,isApproved:1,
				location:1,isContactSynced:1,country:1,state:1,city:1,
				socialData:1,pushNotificationStatus:1,communityNotificationStatus:1,
				wishesNotificationStatus:1,gratitudeNotificationStatus:1,
				perferMedidation: 1,preferPrayer: 1,locationSharing:1, 
				wishCount:1,blessingCreatedCount: 1,blessingReceivedCount: 1,
				gratitudeSharedCount: 1,gratitudeReceivedCount:1,offerBlessing:1,
				blessingCount:1,gratitudeCount:1,countryFlagCode:1,intension:1,completeTooltip:1,
				isProfileComplete:1,isPasswordSet:1,isEmailVerified:1,globalGratRecieveCount:1
			};
			const userDetail = await this.findOne("users", query, project);
			return userDetail;
		} catch (error) {
			throw error
		}
	}
	/**
	 * @function favourite
	 */
	async favourite(userId,params) {
		try {
			const aggPipe = [];
			const match: any = {};
			match['userId'] = toObjectId(userId)
			match['status'] = { '$eq': STATUS.ACTIVE}
			aggPipe.push({
				"$lookup": {
					"from": "wishes",
					"let": { "wishId": "$wishId" },

					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$_id", "$$wishId"]
										},
										{
											'$eq':  ["$status", STATUS.ACTIVE]
										}
									]
								}

						 },
						},
						{ "$project": {_id:1,image:1,totalBlessings:1,description:1
						} }
					],
					"as": "wishDetail"
				}
			});
			aggPipe.push({  
				'$unwind': {
					"path": "$wishDetail",
					"preserveNullAndEmptyArrays": false
				}
			});
			aggPipe.push({ "$match": match });
			aggPipe.push({
				"$project": {
					_id:0, wishDetail:1
				}
			});
			aggPipe.push({ '$limit': 8 });
			aggPipe.push({ $sample: { size: 8 }});
			const options = {};
			// let counData = await this.aggregate("favourites", aggPipe);
            // let total = counData.length;
			let response = await this.paginate("favourites", aggPipe,params.limit, params.pageNo, options, true);
			// response['total'] = total;
			return response;
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function suggestedRequested
	 */
		async suggestedRequested(userId,params) {
			try {
				const aggPipe = [];
				const match: any = {};
				match['userId'] = toObjectId(userId)
				match['status'] = { '$eq': STATUS.ACTIVE}
				if(params.userList){
					let favouriteUser = params.userList
					let users = params.userList.split(',')
					params.userList = users
					for(let i = 0; params.userList.length > i; i++){
						params.userList[i]=toObjectId(params.userList[i])
					}
					match['_id'] = {'$nin':params.userList}
					params.userList=favouriteUser;
				}
				aggPipe.push({ "$match": match });
				aggPipe.push({
					"$project": {
						'wishDetail._id':'$_id','wishDetail.image':'$image','wishDetail.totalBlessings':'$totalBlessings',"wishDetail.description":"$description"
					}
				});
				//aggPipe.push({ '$limit': params.limit });
				//aggPipe.push({ $sample: { size: params.limit }});
				
				const options = {};
				// let counData = await this.aggregate("wishes", aggPipe);
                // let total = counData.length;
			    let response = await this.paginate("wishes", aggPipe,params.limit, params.pageNo, options, true);
			    // response['total'] = total;
				return response;
			} catch (error) {
				throw error;
			}
		}
		/**
	 * @function suggestedReceived
	 */
		async suggestedReceived(userId,params) {
			try {
				const aggPipe = [];
				const match: any = {};
				match['userId'] = toObjectId(userId)
				match['status'] = { '$eq': STATUS.ACTIVE}
				match['isDuplicate'] = false;
				if(params.userList){
					let favouriteUser = params.userList
					let users = params.userList.split(',')
					params.userList = users
					for(let i = 0; params.userList.length > i; i++){
						params.userList[i]=toObjectId(params.userList[i])
					}
					match['wishId'] = {'$nin':params.userList}
					params.userList=favouriteUser;
				}
				aggPipe.push({

					"$lookup": {
						"from": "wishes",
						"let": { "wishId": "$wishId" },
						"pipeline": [
							{ "$match": { "$expr": 
							{$and: [
								{ "$eq": ["$_id", "$$wishId"] },
								{'$in':  ["$status", [STATUS.ACTIVE]]}
							]
							}
						} },
							{"$project": { image: 1, totalBlessings: 1, description: 1 }}
						],
						"as": "wishDetail"
					}
				});
				aggPipe.push(
					{ "$unwind": { path: '$wishDetail', preserveNullAndEmptyArrays: false } },
				)
				aggPipe.push({ "$match": match });
				aggPipe.push({
					"$project": {
						wishDetail:1
					}
				});
				//aggPipe.push({ '$limit': params.limit });
				//aggPipe.push({ $sample: { size: params.limit }});
				
				const options = {};
				// let counData = await this.aggregate("tagwishes", aggPipe);
                // let total = counData.length;
			    let response = await this.paginate("tagwishes", aggPipe,params.limit, params.pageNo, options, true);
			    // response['total'] = total;
				return response;
			} catch (error) {
				throw error;
			}
		}
	/**
	 * @function leastBlessedRequested
	 */
		async leastBlessedRequested(userId,params) {
			try {
				const aggPipe = [];
				const match: any = {};
				// match['userId'] = toObjectId(userId)
				match["$or"] = [
					{ userId:toObjectId(userId)},
					{ hostId:toObjectId(userId)}
				];

				match['status'] = { '$eq': STATUS.ACTIVE}
				if(params.userList){
					let favouriteUser = params.userList
					let users = params.userList.split(',')
					params.userList = users
					for(let i = 0; params.userList.length > i; i++){
						params.userList[i]=toObjectId(params.userList[i])
					}
					match['_id'] = {'$nin':params.userList}
					params.userList=favouriteUser;
				}
				aggPipe.push({ "$match": match });
				aggPipe.push({
					"$project": {
						'wishDetail._id':'$_id','wishDetail.image':'$image','wishDetail.totalBlessings':'$totalBlessings',"wishDetail.description":"$description"
					}
				});
			//	aggPipe.push({ '$limit': params.limit });
				aggPipe.push({ "$sort": { totalBlessings:1} });
				const options = {};
				// let counData = await this.aggregate("wishes", aggPipe);
                // let total = counData.length;
			    let response = await this.paginate("wishes", aggPipe,params.limit, params.pageNo, options, true);
			    // response['total'] = total;				
				return response;
			} catch (error) {
				throw error;
			}
		}
	
		/**
	 * @function leastBlessedReceived
	 */
		async leastBlessedReceived(userId,params) {
			try {
				const aggPipe = [];
				const match: any = {};
				match['userId'] = toObjectId(userId)
				match['status'] = { '$eq': STATUS.ACTIVE}
				match['isDuplicate'] = false;
				if(params.userList){
					let favouriteUser = params.userList
					let users = params.userList.split(',')
					params.userList = users
					for(let i = 0; params.userList.length > i; i++){
						params.userList[i]=toObjectId(params.userList[i])
					}
					match['wishId'] = {'$nin':params.userList}
					params.userList=favouriteUser;
				}
				aggPipe.push({

					"$lookup": {
						"from": "wishes",
						"let": { "wishId": "$wishId" },
						"pipeline": [
							{ "$match": { "$expr": 
							{$and: [
								{ "$eq": ["$_id", "$$wishId"] },
								{'$in':  ["$status", [STATUS.ACTIVE]]}
							]
							}
						} },
							{"$project": { image: 1, totalBlessings: 1, description: 1 }}
						],
						"as": "wishDetail"
					}
				});
				aggPipe.push(
					{ "$unwind": { path: '$wishDetail', preserveNullAndEmptyArrays: false } },
				)
				aggPipe.push({ "$match": match });
				aggPipe.push({
					"$project": {
						wishDetail:1
					}
				});
				//aggPipe.push({ '$limit': params.limit });
				aggPipe.push({ "$sort": { 'wishDetail.totalBlessings':1} });
				const options = {};
				// let counData = await this.aggregate("tagwishes", aggPipe);
                // let total = counData.length;
			    let response = await this.paginate("tagwishes", aggPipe,params.limit, params.pageNo, options, true);
			    // response['total'] = total;				
				return response;
			} catch (error) {
				throw error;
			}
		}
	/**
	 * @function editProfile
	 */
		async editUserProfile(params, userId: string) {
			try {
				const query: any = {};
				query._id = userId;
				const update = {};
				if (Object.values(params).length) 
				if(params.phoneNumber) params['fullPhoneNumber'] = params.countryCode+params.phoneNumber;
				if(params.firstName) params['name'] = params.firstName+' '+params.lastName;
				params.updateType=UPDATE_TYPE.EDIT_PROFILE;
				update["$set"] = params;
				const options = { new: true };
				return await this.findOneAndUpdate("users", query, update, options);
			} catch (error) {
				throw error;
			}
		}
	/**
	 * @function deleteEmailCheck
	 */
	async deleteEmailCheck(params) {
		try {
			const query: any = {};
			query['$or'] = [
				{ 'email':params.email },
				{'$and': [{ 'oldEmail':params.email}, {isDeletedBy:USER_TYPE.ADMIN }]}
			]
			query.status = { "$in": [STATUS.DELETED,STATUS.BLOCKED] };
			const projection = { updatedAt: 0 };
			return await this.findOne("users", query, projection);
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function deleteEmailCheckSocial
	 */
	async deleteEmailCheckSocial(params) {
		try {
			const query: any = {};
			const socialCheck: any = [];
			if(params.loginType==LOGIN_TYPE.APPLE) socialCheck.push({appleSocialId:params.socialId });
			if(params.loginType==LOGIN_TYPE.GOOGLE) socialCheck.push({googleSocialId:params.socialId });
			socialCheck.push({isDeletedBy:USER_TYPE.ADMIN });
			query['$or'] = [
				{'$and': socialCheck}
			]
			query.status = { "$in": [STATUS.DELETED,STATUS.BLOCKED] };
			const projection = { updatedAt: 0 };
			return await this.findOne("users", query, projection);
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function mySyncedContacts
	 */
	async mySyncedContacts(params: UserRequest.Registration) {
		try {
			return await baseDao.aggregate("contacts", [
				{
					$match: {
						$or: [
							{
								phoneNumber:params.phoneNumber
							},
							{
								phoneNumber:params.countryCode+params.phoneNumber
							},
							{
								phoneNumber: { $regex: `${params.phoneNumber}$` }
							}
						]
					}
				},
				{
					$project: { _id: 0, userId: 1, name: 1 }
				},
				{
					$group: {
						_id: "$userId",
						name: { $first: "$name" }
					}
				}
			]);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function mySyncedContactsV2
	 */
	async mySyncedContactsV2(params: UserRequest.Registration) {
		try {
			return await baseDao.aggregate("contacts_v2", [
				{
					$match: {
						$or: [
							{
								"phoneNumbers.phoneNumber":params.phoneNumber
							},
							{
								"phoneNumbers.phoneNumber":params.countryCode+params.phoneNumber
							},
							{
								"phoneNumbers.phoneNumber": { $regex: `${params.phoneNumber}$` }
							}
						]
					}
				},
				{
					$project: { _id: 0, userId: 1, name: 1 }
				},
				{
					$group: {
						_id: "$userId",
						name: { $first: "$name" }
					}
				}
			]);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function setPassword
	 */
	async setPassword(params: UserRequest.SetPassword, tokenData: TokenData) {
		try {
			const query: any = {};
			query["_id"] = tokenData.userId;
			
			const update = {};
			update["$set"] = { "hash": params.hash, "salt": params.salt, "isPasswordSet": true };
			return await this.updateOne("users", query, update, {});
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function findAdminByToken
	 */
	async findUserByHash(token: string, project = {}) {
		try {
			const query: any = {};
			query.passwordResetToken = token;

			const projection = (Object.values(project).length) ? project : { createdAt: 0, updatedAt: 0 };
			return await this.findOne("users", query, projection);
		} catch (error) {
			throw error;
		}
	}
}

export const userDao = new UserDao();