"use strict";
const mongoose = require("mongoose");
import { Document, model, Model, Schema } from "mongoose";
import { DB_MODEL_REF, STATUS } from "@config/index";

export interface IPhoneNumber {
  phoneNumber: string;
  prefixPhoneNumber: string;
  type: string;
  default?: boolean;
  connectedToAccount?: boolean;
  profilePicture? : string;
}

export interface IContactV2 extends Document {
  name?: string;
  firstName?: string;
  lastName?: string;
  email: string;
  isAppUser: boolean;
  phoneNumbers?: IPhoneNumber[];
  deviceId?: string;
  status: string;
  created: number;
  platform: string;
}

// Define the sub-schema for phone numbers
const phoneNumberSchema = new mongoose.Schema(
  {
    phoneNumber: { type: String, required: true },
    originalPhoneNumber: { type: String, required: false },
    type: { type: String, required: true },
    default: { type: Boolean, required: false },
    connectedToAccount: { type: Boolean, required: false },
    contactUserId: { type: Schema.Types.ObjectId, required: false },
  },
  { _id: false } // _id: false to prevent creating an _id for each phone number sub-document
); 

const contactSchemaV2: Schema = new mongoose.Schema(
  {
    _id: { type: Schema.Types.ObjectId, required: true, auto: true },
    userId: { type: Schema.Types.ObjectId, required: true },
    name: { type: String, trim: true, required: false },
    firstName: { type: String, trim: true, required: false },
    lastName: { type: String, trim: true, required: false },
    email: { type: String, trim: true, required: false },
    isAppUser: { type: Boolean, default: true },
    phoneNumbers: {
      type: [phoneNumberSchema],
      required: true,
    },
    deviceId: { type: String, required: false },
    status: {
      type: String,
      enum: [STATUS.BLOCKED, STATUS.UN_BLOCKED, STATUS.DELETED],
      default: STATUS.UN_BLOCKED,
      required: true,
    },

    platform: { type: String, required: false },
    created: { type: Number, default: Date.now },
  },
  {
    versionKey: false,
    timestamps: true,
  }
);

contactSchemaV2.index({ name: 1 });
// contactSchemaV2.index({ email: 1 });
// contactSchemaV2.index({ phoneNumber: 1, deviceId: 1 }, { unique: true });
// Create a compound unique index on userId and phoneNumbers.phoneNumber to ensure uniqueness across documents
// contactSchemaV2.index({ deviceId: 1, 'phoneNumbers.phoneNumber': 1 }, { unique: true });
contactSchemaV2.index(
  { userId: 1, deviceId: 1, 'phoneNumbers.phoneNumber': 1 },
  {
    unique: true,
    partialFilterExpression: {
      'phoneNumbers.phoneNumber': { $exists: true }
    }
  }
);
// contactSchemaV2.index({ phoneNumbers: 1, deviceId: 1 }, { unique: true });
contactSchemaV2.index({ status: 1 });
contactSchemaV2.index({ created: -1 });

// Export contactsV2 schema
export const contacts_v2: Model<IContactV2> = model<IContactV2>(DB_MODEL_REF.CONTACTV2, contactSchemaV2);
