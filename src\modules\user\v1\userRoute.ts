"use strict";
import { Request, ResponseToolkit } from "@hapi/hapi";
import * as <PERSON><PERSON> from "joi";
import { failActionFunction } from "@utils/appUtils";
import {
	authorizationHeaderObj,
	headerObject
} from "@utils/validator";
import {
	REGEX,
	SWAGGER_DEFAULT_RESPONSE_MESSAGES,
	SERVER,
	LOGIN_TYPE,
	ACTION_TYPE,
	HOME_LIST,
	BLESSING_PERFORM_TYPE,
	THANK_WISHWELL_LISTING_TYPE,
	THANK_WISHWELL_SORT_CRITERIA,
	VALIDATION_CRITERIA,
	VALIDATION_MESSAGE
} from "@config/index";
import { responseHandler } from "@utils/ResponseHandler";   
import { userControllerV1 } from "@modules/user/index";
export const userRoute = [
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v3/user/signup`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const headers = request.headers;
				const payload = request.payload;
				payload.remoteAddress = request["headers"]["x-forwarded-for"] || request.info.remoteAddress;
				
				const result = await userControllerV1.signUpWithoutVerification({ ...headers, ...payload });
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "user"],
			description: "User SignUp without email verification",
			auth: {
				strategies: ["BasicAuth"]
			},
			validate: {
				headers: headerObject["required"],
				payload: Joi.object({
					email: Joi.string()
						.trim()
						.lowercase()
						.email({ minDomainSegments: 2 })
						.regex(REGEX.EMAIL)
						.default(SERVER.DEFAULT_EMAIL)
						.required(),
					password: Joi.string().min(6).required(),
					deviceId: Joi.string().trim().required(),
					deviceToken: Joi.string().trim().required()
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v2/user/signup`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const headers = request.headers;
				const payload: UserRequest.SignUp = request.payload;
				payload.remoteAddress = request["headers"]["x-forwarded-for"] || request.info.remoteAddress;
				const result = await userControllerV1.signUpV2({ ...headers, ...payload },);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "user"],
			description: "User SignUp ",
			auth: {
				strategies: ["BasicAuth"]
			},
			validate: {
				headers: headerObject["required"],
				payload: Joi.object({
					email:Joi.string()
						.trim()
						.lowercase()
						.email({ minDomainSegments: 2 })
						.regex(REGEX.EMAIL)
						.default(SERVER.DEFAULT_EMAIL)
						.required(),
					deviceId: Joi.string().trim().required(),
					deviceToken: Joi.string().trim().required(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/user/signup`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const headers = request.headers;
				const payload: UserRequest.SignUp = request.payload;
				payload.remoteAddress = request["headers"]["x-forwarded-for"] || request.info.remoteAddress;
				const result = await userControllerV1.signUpV1({ ...headers, ...payload },);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "user"],
			description: "User SignUp ",
			auth: {
				strategies: ["BasicAuth"]
			},
			validate: {
				headers: headerObject["required"],
				payload: Joi.object({
					email:Joi.string()
					.trim()
					.lowercase()
					.email({ minDomainSegments: 2 })
					.regex(REGEX.EMAIL)
					.default(SERVER.DEFAULT_EMAIL)
					.required(),
					deviceId: Joi.string().trim().required(),
					deviceToken: Joi.string().trim().required(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},  
	{
		method: "PUT",
		path: `${SERVER.API_BASE_URL}/v1/user/set-password`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const payload: UserRequest.SetPassword = request.payload;
				const result = await userControllerV1.setPassword(payload, tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "user"],
			description: "User set password after signup & verify email",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
					password: Joi.string()
						.trim()
						.regex(REGEX.PASSWORD_V3)
						.min(VALIDATION_CRITERIA.PASSWORD_MIN_LENGTH)
						.max(VALIDATION_CRITERIA.PASSWORD_MAX_LENGTH)
						.default(SERVER.DEFAULT_PASSWORD)
						.required()
						.messages({
							"string.pattern.base": VALIDATION_MESSAGE.password.pattern,
							"string.min": VALIDATION_MESSAGE.password.minlength,
							"string.max": VALIDATION_MESSAGE.password.maxlength,
							"string.empty": VALIDATION_MESSAGE.password.required,
							"any.required": VALIDATION_MESSAGE.password.required
						}),
					deviceId: Joi.string().trim().optional(),
					// deviceToken: Joi.string().trim().optional()
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/user/verify-login-email`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const payload: UserRequest.Login = request.payload;
				const result = await userControllerV1.verifyLoginEmail(payload);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "user"],
			description: "Verify login email",
			auth: {
				strategies: ["BasicAuth"]
			},
			validate: {
				headers: headerObject["required"],
				payload: Joi.object({
					email:Joi.string()
						.trim()
						.lowercase()
						.email({ minDomainSegments: 2 })
						.regex(REGEX.EMAIL)
						.default(SERVER.DEFAULT_EMAIL)
						.required(),
					deviceId: Joi.string().trim().required(),
					deviceToken: Joi.string().trim().required()
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/user/login`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const headers = request.headers;
				const payload: UserRequest.Login = request.payload;
				payload.remoteAddress = request["headers"]["x-forwarded-for"] || request.info.remoteAddress;
				const result = await userControllerV1.login({ ...headers, ...payload });
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "user"],
			description: "User login",
			auth: {
				strategies: ["BasicAuth"]
			},
			validate: {
				headers: headerObject["required"],
				payload: Joi.object({
					email:Joi.string()
						.trim()
						.lowercase()
						.email({ minDomainSegments: 2 })
						.regex(REGEX.EMAIL)
						.default(SERVER.DEFAULT_EMAIL)
						.required(),
					password: Joi.string()
						.trim()
						.min(VALIDATION_CRITERIA.PASSWORD_MIN_LENGTH)
						.max(VALIDATION_CRITERIA.PASSWORD_MAX_LENGTH)
						.default(SERVER.DEFAULT_PASSWORD)
						.required(),
					deviceId: Joi.string().trim().optional(),
					deviceToken: Joi.string().trim().optional()
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/user/social-signup`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const headers = request.headers;
				const payload: UserRequest.SignUp = request.payload;
				payload.remoteAddress = request["headers"]["x-forwarded-for"] || request.info.remoteAddress;
				const result = await userControllerV1.socialSignup({ ...headers, ...payload });
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "user"],
			description: "User Social-Signup",
			auth: {
				strategies: ["BasicAuth"]
			},
			validate: {
				headers: headerObject["required"],
				payload: Joi.object({
					name: Joi.string().trim().optional(),
					profilePicture: Joi.string().trim().allow("").optional(),
					email: Joi.string()
						.trim()
						.lowercase()
						.email({ minDomainSegments: 2 })
						.regex(REGEX.EMAIL)
						.optional(),
					socialId: Joi.string().trim().optional(),
					loginType: Joi.string().valid(
						LOGIN_TYPE.APPLE,
						LOGIN_TYPE.FACEBOOK,
						LOGIN_TYPE.GOOGLE
					).optional(),
					deviceId: Joi.string().trim().optional(),
					deviceToken: Joi.string().trim().optional(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},    
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/user/registration`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const payload: UserRequest.Registration = request.payload;
				const result = await userControllerV1.registration(payload,tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "user"],
			description: "User registration",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
					firstName: Joi.string().trim().required(),
					lastName: Joi.string().trim().required(),
					country: Joi.string().trim().required(),
					state: Joi.string().trim().required(),
					city: Joi.string().trim().required(),
					countryCode: Joi.string().trim().required(),
					phoneNumber: Joi.string().trim().required(),
					countryFlagCode:Joi.string().trim().required(),
					deviceId: Joi.string().trim().optional(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/user/contact-sync`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const payload: UserRequest.ContactSync = request.payload;
				const result = await userControllerV1.contactSync(payload,tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "user"],
			description: "User Contact-Syncing",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
					userId: Joi.string().trim().regex(REGEX.MONGO_ID).required(),
					contacts: Joi.array().items(
						Joi.object({
						    name: Joi.string().trim().optional().allow(""),
						    phoneNumber: Joi.string().trim().required()
					  }).required(),).required(),  
					deviceId: Joi.string().trim().optional(),  
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v2/user/contact-sync`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const payload: UserRequest.ContactSyncV2 = request.payload;
				const result = await userControllerV1.contactSyncV2(payload,tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "user"],
			description: "User Contact-Syncing",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
				  userId: Joi.string().trim().regex(REGEX.MONGO_ID).required(),
				  contacts: Joi.array().items(
					Joi.object({
					  name: Joi.string().trim().optional().allow(""),
					  phoneNumbers: Joi.array().items(
						Joi.object({
						  phoneNumber: Joi.string().trim().required(),
						  type: Joi.string().trim().required(),
						}).required()
					  ).required()
					}).required()
				  ).required(),
				  deviceId: Joi.string().trim().optional()
				}),
				failAction: failActionFunction
			  },
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/user/delete-sync-contact`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const payload: UserRequest.DeleteSyncContact = request.payload;
				const result = await userControllerV1.deleteSyncContact(payload,tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "user"],
			description: "User contact sync",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
					contacts: Joi.array().items(
						Joi.object({
						    name: Joi.string().trim().required(),
						    phoneNumber: Joi.string().trim().required()
					  }).required(),).required(), 
					deviceId: Joi.string().trim().optional(),     
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v2/user/delete-sync-contact`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const payload: UserRequest.DeleteSyncContactV2 = request.payload;
				const result = await userControllerV1.deleteSyncContactV2(payload,tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "user"],
			description: "User contact sync",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
					contacts: Joi.array().items(
						Joi.object({
						    name: Joi.string().trim().required(),
						    phoneNumber: Joi.string().trim().required()
					  }).required(),).required(), 
					deviceId: Joi.string().trim().optional(),     
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/user/favourites`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const payload: UserRequest.Favourites = request.payload;
				const result = await userControllerV1.favourites(payload,tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "favourites"],
			description: "Add/Remove favourites",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
					wishId: Joi.string().trim().regex(REGEX.MONGO_ID).required(),
					type:Joi.string().valid(ACTION_TYPE.ADD,ACTION_TYPE.REMOVE).required()
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/user/favourites`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const query: UserRequest.FavouritesList = request.query;
				const result = await userControllerV1.getFavourites(query,tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
	
			}
		},
		options: {
			tags: ["api", "favourites"],
			description: "Favourite List of Wishes",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				query: Joi.object({
					pageNo: Joi.number().optional().description("Page no"),
					limit: Joi.number().optional().description("limit"),
					searchKey: Joi.string().optional().description("Search"),
					communityId: Joi.string().optional()
					// Joi.alternatives()
					// .try(Joi.array().items(Joi.string().trim()), Joi.string())
					// .optional(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/user/logout`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const result = await userControllerV1.logout(tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "user"],
			description: "User Logout",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	 {
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/user/profile`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const headers = request.headers;
				const query: UserId = request.query;
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const result = await userControllerV1.profile({ ...query, ...headers }, tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "user"],
			description: "User Profile",
			notes: "for Admin/User",
			auth: {
				strategies: ["CommonAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				query: Joi.object({
					userId: Joi.string().trim().regex(REGEX.MONGO_ID).optional(),
					deviceId: Joi.string().trim().optional(),
					deviceToken: Joi.string().trim().optional(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/user/home`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const query: UserRequest.Home = request.query;
				const result = await userControllerV1.getListV2(query,tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "user"],
			description: "Home page",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				query: Joi.object({
					type: Joi.string().valid(HOME_LIST.FAVOURITE,HOME_LIST.SUGGESTED,HOME_LIST.LEAST_BLESSED).optional(),
					isInclude:Joi.boolean().required(),
					userList: Joi.string().optional()

				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	
	{
		method: "PUT",
		path: `${SERVER.API_BASE_URL}/v1/user/setting`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const payload: UserRequest.Settings = request.payload;
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const result = await userControllerV1.settings(payload, tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "user"],
			description: "Manage User Settings",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object().keys({
					offerBlessing:Joi.string().trim().optional().allow(""),
					intension:Joi.string().trim().optional(),
					pushNotificationStatus: Joi.boolean().optional(),
					communityNotificationStatus: Joi.boolean().optional(),
					wishesNotificationStatus:Joi.boolean().optional(),
					gratitudeNotificationStatus:Joi.boolean().optional(),
					perferMedidation:Joi.boolean().optional(),
					preferPrayer:Joi.boolean().optional(),
					locationSharing:Joi.boolean().optional(),
					location:Joi.object({
						address: Joi.string().trim().allow("").optional(),
						coordinates: Joi.array().optional(),
						city:Joi.string().required(),
						state:Joi.string().required(),
						country:Joi.string().required()
					}).optional(), 
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	// {
	// 	method: "POST",
	// 	path: `${SERVER.API_BASE_URL}/v1/user/notification-status`,
	// 	handler: async (request: Request | any, h: ResponseToolkit) => {
	// 		try {
	// 			const payload: UserRequest.NotificationStatus = request.payload;
	// 			const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
	// 			const result = await userControllerV1.notificationStatus(payload, tokenData);
	// 			return responseHandler.sendSuccess(h, result);
	// 		} catch (error) {
	// 			return responseHandler.sendError(request, error);
	// 		}
	// 	},
	// 	options: {
	// 		tags: ["api", "user"],
	// 		description: "Manage Notifications",
	// 		auth: {
	// 			strategies: ["UserAuth"]
	// 		},
	// 		validate: {
	// 			headers: authorizationHeaderObj,
	// 			payload: Joi.object({
	// 				isRead: Joi.boolean().required(),
	// 				notificationId: Joi.string().trim().regex(REGEX.MONGO_ID).required()
	// 			}),
	// 			failAction: failActionFunction
	// 		},
	// 		plugins: {
	// 			"hapi-swagger": {
	// 				responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
	// 			}
	// 		}
	// 	}
	// },
	// {
	// 	method: "GET",
	// 	path: `${SERVER.API_BASE_URL}/v1/user/notification`,
	// 	handler: async (request: Request | any, h: ResponseToolkit) => {
	// 		try {
	// 			const query: UserRequest.NotificationList = request.query;
	// 			const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
	// 			const result = await userControllerV1.NotificationList(query, tokenData);
	// 			return responseHandler.sendSuccess(h, result);
	// 		} catch (error) {
	// 			return responseHandler.sendError(request, error);
	// 		}
	// 	},
	// 	config: {
	// 		tags: ["api", "user"],
	// 		description: " User Notification List",
	// 		notes: "for Participant/Supporter",
	// 		auth: {
	// 			strategies: ["UserAuth"]
	// 		},
	// 		validate: {
	// 			headers: authorizationHeaderObj,
	// 			query: Joi.object({
	// 				pageNo: Joi.number().required().description("Page no"),
	// 				limit: Joi.number().required().description("limit"),
	// 			}),
	// 			failAction: failActionFunction
	// 		},
	// 		plugins: {
	// 			"hapi-swagger": {
	// 				responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
	// 			}
	// 		}
	// 	}
	// },

	{
		method: "PUT",
		path: `${SERVER.API_BASE_URL}/v1/user/edit-profile`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const headers = request.headers;
				const payload: UserRequest.EditProfile = request.payload;
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const result = await userControllerV1.editUserProfile({ ...payload, ...headers }, tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "user"],
			description: "edit user profile",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
					profilePicture: Joi.string().trim().allow("").optional(),
					firstName: Joi.string().trim().optional(),
					lastName: Joi.string().trim().optional(),
					countryCode: Joi.string().trim().optional(),
					phoneNumber: Joi.string().trim().optional(),
					countryFlagCode:Joi.string().trim().optional(),
					deviceId: Joi.string().trim().optional(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},

	{
		method: "PUT",
		path: `${SERVER.API_BASE_URL}/v1/user/delete-account`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const payload = request.payload;
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const result = await userControllerV1.deleteAccount({...tokenData,...payload});
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "user"],
			description: "dlete account user ",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
					deleteReason: Joi.string().trim().optional(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},

	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/user/wishwell-thanks`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const payload: UserRequest.WishwellThanks = request.payload;
				const result = await userControllerV1.wishwellThanks(payload,tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "user"],
			description: "thanks wishwell",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
					note: Joi.string().trim().optional(),
					audio: Joi.string().trim().optional(),
					type:Joi.string().valid(BLESSING_PERFORM_TYPE.AUDIO, BLESSING_PERFORM_TYPE.TEXT).required(),
					isAllowShare:Joi.boolean().optional().default(false),
					amount:Joi.number().optional(),
					//paymentIntentid:Joi.string().optional(),
					//intentId:Joi.string().regex(REGEX.MONGO_ID).optional(),
					intentId: Joi.when("amount", {
						is: Joi.exist(),
						then: Joi.string().trim().regex(REGEX.MONGO_ID).required(),
						otherwise: Joi.string().trim().optional()
					}),
					paymentIntentid: Joi.when("amount", {
						is: Joi.exist(),
						then: Joi.string().trim().required(),
						otherwise: Joi.string().trim().optional()
					}),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},

	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/admin/thank-wishwell`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const query: UserRequest.ThankWishwellListing = request.query;
				const result = await userControllerV1.thankWishwellListing(query);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin thank wishwell"],
			description: "Thank Wishwell",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				query: Joi.object({
					userId: Joi.string().optional(),
					type: Joi.string().required().valid(THANK_WISHWELL_LISTING_TYPE.ALL, THANK_WISHWELL_LISTING_TYPE.USER),
					pageNo: Joi.number().required(),
					limit: Joi.number().required(),
					sortCriteria: Joi.string().trim().valid(THANK_WISHWELL_SORT_CRITERIA.SERIAL_NUMBER, THANK_WISHWELL_SORT_CRITERIA.NOTE_OWNER, THANK_WISHWELL_SORT_CRITERIA.DATE, THANK_WISHWELL_SORT_CRITERIA.LOCATION, THANK_WISHWELL_SORT_CRITERIA.DONATION, THANK_WISHWELL_SORT_CRITERIA.CONSENT).optional().default(THANK_WISHWELL_SORT_CRITERIA.DATE),
					sortBy: Joi.number().valid(1, 0).optional(),
					searchKey: Joi.string().optional(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},


	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/user/forgot-password`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const headers = request.headers;
				const payload: UserRequest.ForgotPassword = request.payload;
				const result = await userControllerV1.forgotPassword({ ...headers, ...payload });
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "user"],
			description: "Forgot Password",
			auth: {
				strategies: ["BasicAuth"]
			},
			validate: {
				headers: headerObject["required"],
				payload: Joi.object({
					email: Joi.string()
						.trim()
						.lowercase()
						.default(SERVER.DEFAULT_EMAIL)
						.email({ minDomainSegments: 2 })
						// .email({ minDomainSegments: 2, tlds: { allow: ["com", "net"] } })
						.regex(REGEX.EMAIL)
						.required()
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/user/reset-password`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const payload: UserRequest.ChangeForgotPassword = request.payload;
				const result = await userControllerV1.resetPassword(payload);
				console.log(result, 'resultsststs')
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "user"],
			description: "Reset Password After Forgot Password",
			auth: {
				strategies: ["BasicAuth"]
			},
			validate: {
				headers: headerObject["required"],
				payload: Joi.object({
					resetToken: Joi.string().trim().required(),
					newPassword: Joi.string()
						.trim()
						.regex(REGEX.PASSWORD_V3)
						.min(VALIDATION_CRITERIA.PASSWORD_MIN_LENGTH)
						.max(VALIDATION_CRITERIA.PASSWORD_MAX_LENGTH)
						.default(SERVER.DEFAULT_PASSWORD)
						.required()
						.messages({
							"string.pattern.base": VALIDATION_MESSAGE.password.pattern,
							"string.min": VALIDATION_MESSAGE.password.minlength,
							"string.max": VALIDATION_MESSAGE.password.maxlength,
							"string.empty": VALIDATION_MESSAGE.password.required,
							"any.required": VALIDATION_MESSAGE.password.required
						}),
					confirmPassword: Joi.string()
						.trim()
						.regex(REGEX.PASSWORD_V3)
						.min(VALIDATION_CRITERIA.PASSWORD_MIN_LENGTH)
						.max(VALIDATION_CRITERIA.PASSWORD_MAX_LENGTH)
						.default(SERVER.DEFAULT_PASSWORD)
						.required()
						.messages({
							"string.pattern.base": VALIDATION_MESSAGE.password.pattern,
							"string.min": VALIDATION_MESSAGE.password.minlength,
							"string.max": VALIDATION_MESSAGE.password.maxlength,
							"string.empty": VALIDATION_MESSAGE.password.required,
							"any.required": VALIDATION_MESSAGE.password.required
						})
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/user/match-password`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const payload: UserRequest.SetPassword = request.payload;
				const result = await userControllerV1.matchPassword(payload, tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "user"],
			description: "check curret password",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
					password: Joi.string()
						.trim()
						// .regex(REGEX.PASSWORD_V3)
						.min(VALIDATION_CRITERIA.PASSWORD_MIN_LENGTH)
						.max(VALIDATION_CRITERIA.PASSWORD_MAX_LENGTH)
						.default(SERVER.DEFAULT_PASSWORD)
						.required()
						// .messages({
						// 	"string.pattern.base": VALIDATION_MESSAGE.password.pattern,
						// 	"string.min": VALIDATION_MESSAGE.password.minlength,
						// 	"string.max": VALIDATION_MESSAGE.password.maxlength,
						// 	"string.empty": VALIDATION_MESSAGE.password.required,
						// 	"any.required": VALIDATION_MESSAGE.password.required
						// })
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/user/change-password`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const payload: UserRequest.ChangeForgotPassword = request.payload;
				const result = await userControllerV1.changePassword(payload, tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "user"],
			description: "Change Password",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
					newPassword: Joi.string()
						.trim()
						.regex(REGEX.PASSWORD_V3)
						.min(VALIDATION_CRITERIA.PASSWORD_MIN_LENGTH)
						.max(VALIDATION_CRITERIA.PASSWORD_MAX_LENGTH)
						.default(SERVER.DEFAULT_PASSWORD)
						.required()
						.messages({
							"string.pattern.base": VALIDATION_MESSAGE.password.pattern,
							"string.min": VALIDATION_MESSAGE.password.minlength,
							"string.max": VALIDATION_MESSAGE.password.maxlength,
							"string.empty": VALIDATION_MESSAGE.password.required,
							"any.required": VALIDATION_MESSAGE.password.required
						}),
					confirmPassword: Joi.string()
						.trim()
						.regex(REGEX.PASSWORD_V3)
						.min(VALIDATION_CRITERIA.PASSWORD_MIN_LENGTH)
						.max(VALIDATION_CRITERIA.PASSWORD_MAX_LENGTH)
						.default(SERVER.DEFAULT_PASSWORD)
						.required()
						.messages({
							"string.pattern.base": VALIDATION_MESSAGE.password.pattern,
							"string.min": VALIDATION_MESSAGE.password.minlength,
							"string.max": VALIDATION_MESSAGE.password.maxlength,
							"string.empty": VALIDATION_MESSAGE.password.required,
							"any.required": VALIDATION_MESSAGE.password.required
						})
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	}
];