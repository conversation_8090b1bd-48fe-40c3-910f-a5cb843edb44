"use strict";

import { BaseDao, baseDao } from "@modules/baseDao/BaseDao";
import { Types } from "mongoose";
import { STATUS, DEVICE_TYPE, BLESSING_SORT_CRITERIA, WISH_CREATED_BY, WISH_SORT_CRITERIA, REPORT_TYPE, FLAG_TYPE, DB_MODEL_REF, INVESTOR_SORT_CRITERIA, USER_TYPE, FLAGGED_WISH_SORT_CRITERIA, WISH_TAG_TYPE, BLESSING_PERFORM_TYPE, BLESSING_TYPE } from "@config/constant";
import { escapeSpecial<PERSON>haracter, toObjectId, passwordGenrator } from "@utils/appUtils";
export class AdminDao extends BaseDao {

	/**
	 * @function isDeletedEmail
	 */
	async isDeletedEmail(params) {
		try {
			const query: any = {};
			query.email = params.email;
			query.status = { "$eq": STATUS.DELETED };
			const projection = { status: 1 };

			return await this.findOne("admins", query, projection);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function isEmailExists
	 */
	async isEmailExists(params, userId?: string) {
		try {
			const query: any = {};
			query.email = params.email;
			if (userId) query._id = { "$not": { "$eq": userId } };
			query.status = { "$ne": STATUS.DELETED };

			const projection = { updatedAt: 0 };

			return await this.findOne("admins", query, projection);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function findAdminById
	 */
	async findAdminById(userId: string, project = {}) {
		try {
			const query: any = {};
			query._id = userId;
			query.status = { "$ne": STATUS.DELETED };

			const projection = (Object.values(project).length) ? project : { createdAt: 0, updatedAt: 0 };

			return await this.findOne("admins", query, projection);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function findAdminByToken
	 */
	async findAdminByToken(token: string, project = {}) {
		try {
			const query: any = {};
			query.passwordResetToken = token;

			const projection = (Object.values(project).length) ? project : { createdAt: 0, updatedAt: 0 };
			return await this.findOne("admins", query, projection);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function createAdmin
	 */
	async createAdmin(params: AdminRequest.Create) {
		try {
			return await this.save("admins", params);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function emptyForgotToken
	 */
	async emptyForgotToken(params) {
		try {
			const query: any = {};
			if (params.token) query.forgotToken = params.token;
			if (params.userId) query._id = params.userId;

			const update = {};
			update["$unset"] = {
				"forgotToken": ""
			};

			return await this.updateOne("admins", query, update, {});
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function setNewPassword   
	 */
	async setNewPassword(params) {
		try {
			const query: any = {};
			query["_id"] = params.userId;
			
			const update = {};
			update["$set"] = { "hash": params.hash };
			return await this.updateOne("admins", query, update, {});
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function updateSetPasswordStatus   
	 */
	async updateSetPasswordStatus(params, userType) {
		try {
			const query: any = {};
			query["_id"] = params.userId;
			
			const update = {};
			let updateData: any = { "isPasswordUpdated": true };
			if(userType == USER_TYPE.INVESTOR) updateData = { ...updateData, "status": STATUS.UN_BLOCKED }
			update["$set"] = updateData;
			return await this.updateOne("admins", query, update, {});
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function updateAdminDetails   
	 */
	async updateAdminDetails(params) {
		try {
			const query: any = {};
			query["_id"] = params.userId;
			
			const update: any = {};
			update["$set"] = { firstName: params.firstName, lastName: params.lastName, contactNo: params.contactNo };
			return await this.updateOne("admins", query, update, {});
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function updateAdminDetailsStatus   
	 */
	async updateAdminDetailsStatus(params) {
		try {
			const query: any = {};
			query["_id"] = params.userId;
			
			const update = {};
			update["$set"] = { "isProfileUpdated": true, "status": STATUS.UN_BLOCKED };
			return await this.updateOne("admins", query, update, {});
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function resetPasswordToken   
	 */
	async resetPasswordToken(params, hash) {
		try {
			const query: any = {};
			query["email"] = params.email;
			
			const update = {};
			update["$set"] = { "passwordResetToken": hash };
			return await this.updateOne("admins", query, update, {});
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function changePassword   
	 */
	async changePassword(params, userId?: string) {
		try {
			const query: any = {};
			if (userId) query._id = userId;
			if (params.email) query.email = params.email;

			const update = {};
			update["$set"] = {
				"hash": params.hash
			};

			if(params.token) {
				await this.updateOne("admins", { passwordResetToken: params.token }, { passwordResetToken: '' }, {});
			}

			return await this.updateOne("admins", query, update, {});
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function editProfile
	 */
	async editProfile(params: AdminRequest.EditProfile, userId: string) {
		try {
			const query: any = {};
			query._id = userId;

			const update = {};
			update["$set"] = params;
			const options = { new: true };

			return await this.findOneAndUpdate("admins", query, update, options);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function dashboardExpansionMonth
	 */
	async dashboardExpansionMonthOld(params: AdminRequest.DashboardExpansion) {
		try {
			let match: any = {};
			let aggPipe: any = [];
			let sort: any = {};
			
			const userLog = {
				from: "login_histories",
				localField: "_id",
				foreignField: "userId._id",
				as: "userLog",
				pipeline: [
					{
						$project: { _id: 1, createdAt: 1 }
					}
				]
			};

			const wishLookup = {
				from: "wishes",
				localField: "_id",
				foreignField: "userId",
				as: "wishes",
				pipeline: [
					{
						$project: { _id: 1, createdAt: 1 }
					}
				]
			}

			const blessingLookup = {
				from: "blessing_libraries",
				localField: "_id",
				foreignField: "userId",
				as: "blessings",
				pipeline: [
					{
						$project: { _id: 1, createdAt: 1 }
					}
				]
			}

			const project = {
				year: { $year: "$createdAt" },
				month: { $month: "$createdAt" },
				activeCount: { $size: "$userLog" },
				wishCount: { $size: "$wishes" },
				blessingCount: { $size: "$blessings" }
			};

			const group = {
				_id: { year: "$year", month: "$month" },
				userCount: { $sum: 1 },
				activeCount: { $sum: "$activeCount" },
				wishCount: { $sum: "$wishCount" },
				blessingCount: { $sum: "$blessingCount" }
			};

			if(params.startDate && params.endDate) {
				match["createdAt"] = {
					$gte: new Date(params.startDate),
        			$lte: new Date(params.endDate)
				}
			}
			
			aggPipe.push({ "$match": match });
			aggPipe.push({ "$lookup": userLog });
			aggPipe.push({ "$lookup": wishLookup });
			aggPipe.push({ "$lookup": blessingLookup });
			aggPipe.push({ "$project": project });
			aggPipe.push({ "$group": group });
			aggPipe.push({ "$addFields": { communityCount: 0 } }); // static for now
			
			return this.paginate("users", aggPipe, params.limit, params.pageNo, {}, true);
		} catch (error) {
			throw error;
		}
	}

	async dashboardExpansionMonth(params: AdminRequest.DashboardExpansion) {
		try {
			let startDate;
			let endDate;
			const now = new Date();
			const nowUTC = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), now.getUTCHours(), now.getUTCMinutes(), now.getUTCSeconds(), now.getUTCMilliseconds()));


			if(params.startDate) {
				startDate = await this.transformDate(params.startDate, "START");
			} else {
				startDate = await this.latestCreatedAt();
			}

			if(params.endDate) {
				endDate = await this.transformDate(params.endDate, "END");;
			} else {
				endDate = nowUTC
			}

			const STEP1 = await this.step1_Month(startDate, endDate);

			const findUsersAll = await this.aggregate("users", [
				{
					$project: {
						__id: 1,
						createdAt: 1,
						status: 1
					}
				},
				{
					$match: {
						createdAt: {
							$lte: endDate,
							$gte: startDate
						},
						status: {
							$ne: STATUS.DELETED
						}
					},
				},
				{
					$group: {
						_id: {
							year: { $year: "$createdAt" }, 
							month: { $month: "$createdAt" }, 
						},
						count: { $sum: 1 }
					}
				}
			]);
			
			const ALL_USERS_GROUP = await this.step2_Month(findUsersAll);
			const ALL_USERS = await this.step3_Month(STEP1, ALL_USERS_GROUP);

			const findUsersActive = await this.aggregate("users", [
				{
					$project: {
						__id: 1,
						createdAt: 1,
						status: 1
					}
				},
				{
					$match: {
						createdAt: {
							$lte: endDate,
							$gte: startDate
						},
						status: STATUS.UN_BLOCKED
					},
				},
				{
					$group: {
						_id: {
							year: { $year: "$createdAt" }, 
							month: { $month: "$createdAt" }, 
						},
						count: { $sum: 1 }
					}
				}
			]);

			const ACTIVE_USERS_GROUP = await this.step2_Month(findUsersActive);
			const ACTIVE_USERS = await this.step3_Month(STEP1, ACTIVE_USERS_GROUP);

			const findWishes = await this.aggregate("wishes", [
				{
					$project: {
						__id: 1,
						createdAt: 1,
						status: 1,
						isGlobalWish: 1
					}
				},
				{
					$match: {
						createdAt: {
							$lte: endDate,
							$gte: startDate
						},
						status: {
							$ne: STATUS.DELETED
						},
						isGlobalWish: false
					},
				},
				{
					$group: {
						_id: {
							year: { $year: "$createdAt" }, 
							month: { $month: "$createdAt" }, 
						},
						count: { $sum: 1 }
					}
				}
			]);

			const ALL_WISHES_GROUP = await this.step2_Month(findWishes);
			const ALL_WISHES = await this.step3_Month(STEP1, ALL_WISHES_GROUP);

			const findBlessings = await this.aggregate("perform_blessings", [
				{
					$project: {
						__id: 1,
						createdAt: 1,
						status: 1,
						isGratitude: 1
					}
				},
				{
					$match: {
						createdAt: {
							$lte: endDate,
							$gte: startDate
						},
						status: {
							$ne: STATUS.DELETED
						},
						isGratitude: false
					},
				},
				{
					$group: {
						_id: {
							year: { $year: "$createdAt" }, 
							month: { $month: "$createdAt" }, 
						},
						count: { $sum: 1 }
					}
				}
			]);

			const ALL_BLESSINGS_GROUP = await this.step2_Month(findBlessings);
			const ALL_BLESSINGS = await this.step3_Month(STEP1, ALL_BLESSINGS_GROUP);

			const findCommunities = await this.aggregate("communities", [
				{
					$project: {
						__id: 1,
						createdAt: 1,
						status: 1
					}
				},
				{
					$match: {
						createdAt: {
							$lte: endDate,
							$gte: startDate
						},
						status: {
							$ne: STATUS.DELETED
						}
					},
				},
				{
					$group: {
						_id: {
							year: { $year: "$createdAt" }, 
							month: { $month: "$createdAt" }, 
						},
						count: { $sum: 1 }
					}
				}
			]);

			const ALL_COMMUNITIES_GROUP = await this.step2_Month(findCommunities);
			const ALL_COMMUNITIES = await this.step3_Month(STEP1, ALL_COMMUNITIES_GROUP);

			const combined = await this.step4_Combined2(ALL_USERS, ACTIVE_USERS, ALL_WISHES, ALL_BLESSINGS, ALL_COMMUNITIES);

			// HANDLE MANUAL PAGINATION
			const pageNumber = params.pageNo;
			const pageSize = params.limit;
			const startIndex = (pageNumber - 1) * pageSize;
			const endIndex = pageNumber * pageSize;
			const paginatedItems = combined.slice(startIndex, endIndex);

			return {
				data: paginatedItems,
				total: combined.length,
				pageNo: params.pageNo,
				totalPage: Math.ceil(combined.length / params.limit),
				nextHit: params.pageNo + 1,
				limit: params.limit
			};
			
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function dashboardExpansionDateOld
	 */
	async dashboardExpansionDateOld(params: AdminRequest.DashboardExpansion) {
		try {
			let match: any = {};
			let aggPipe: any = [];
			let sort: any = {};

			const userLog = {
				from: "login_histories",
				localField: "_id",
				foreignField: "userId._id",
				as: "userLog",
				pipeline: [
					{
						$project: { _id: 1, createdAt: 1 }
					}
				]
			};

			const wishLookup = {
				from: "wishes",
				localField: "_id",
				foreignField: "userId",
				as: "wishes",
				pipeline: [
					{
						$project: { _id: 1, createdAt: 1 }
					}
				]
			}

			const blessingLookup = {
				from: "blessing_libraries",
				localField: "_id",
				foreignField: "userId",
				as: "blessings",
				pipeline: [
					{
						$project: { _id: 1, createdAt: 1 }
					}
				]
			}

			const project = {
				year: { $year: "$createdAt" },
				month: { $month: "$createdAt" },
				day: { $dayOfMonth: "$createdAt" },
				activeCount: { $size: "$userLog" },
				wishCount: { $size: "$wishes" },
				blessingCount: { $size: "$blessings" }
			};

			const group = {
				_id: { year: "$year", month: "$month", day: "$day" },
				userCount: { $sum: 1 },
				activeCount: { $sum: "$activeCount" },
				wishCount: { $sum: "$wishCount" },
				blessingCount: { $sum: "$blessingCount" }
			};

			if(params.startDate && params.endDate) {
				match["createdAt"] = {
					$gte: new Date(params.startDate),
        			$lte: new Date(params.endDate)
				}
			}

			aggPipe.push({ "$match": match });
			aggPipe.push({ "$lookup": userLog });
			aggPipe.push({ "$lookup": wishLookup });
			aggPipe.push({ "$lookup": blessingLookup });
			aggPipe.push({ "$project": project });
			aggPipe.push({ "$group": group });
			aggPipe.push({ "$addFields": { communityCount: 0 } }); // static for now

			return this.paginate("users", aggPipe, params.limit, params.pageNo, {}, true);
		} catch (error) {
			throw error;
		}
	}

	async step1(start, end) {
		let dates = [];
		const startDate = start;
		const endDate = end;
		for(let date = new Date(startDate); date <= endDate; date.setDate(date.getDate() + 1)) {
			dates.push(new Date(date));
		}
		return dates;
	}

	async step1_Month(start, end) {
		let dates = [];
		const startDate = start;
		const endDate = end;
		for(let date = new Date(startDate); date <= endDate; date.setDate(date.getDate() + 1)) {
			let year = date.getFullYear();
			let month = date.getMonth() + 1;
			let yearMonth = `${year}-${month}`;
			if(!dates.includes(yearMonth)) {
				dates.push(yearMonth);
			}
		}
		return dates;
	}

	async step2(users) {
		let groups = {};
		users.forEach(group => {
			let date = new Date(Date.UTC(group._id.year, group._id.month - 1, group._id.day));
			groups[date.toISOString().split('T')[0]] = group.count;
		});
		return groups;
	}

	async step2_Month(users) {
		let groups = {};
		users.forEach(group => {
			let date = `${group._id.year}-${group._id.month}`;
			groups[date] = group.count;
		});
		return groups;
	}

	async step3(dates, groups) {
		for(let date of dates) {
			let key = date.toISOString().split('T')[0];
			if(!groups[key]) {
				groups[key] = 0;
			}
		}
		return groups;
	}

	async step3_Month(dates, groups) {
		for(let date of dates) {
			if(!groups[date]) {
				groups[date] = 0;
			}
		}
		return groups;
	}

	async step4_Combined1(users, activeUsers, wishes, blessings, communities) {
		const allKeys = new Set([
			...Object.keys(users),
			...Object.keys(activeUsers),
			...Object.keys(wishes),
			...Object.keys(blessings),
			...Object.keys(communities)
		]);
		
		const combined = Array.from(allKeys).reduce((acc, date) => {
		acc[date] = {
			userCount: users[date] || 0,
			activeUserCount: activeUsers[date] || 0,
			wishCount: wishes[date] || 0,
			blessingCount: blessings[date] || 0,
			communityCount: communities[date] || 0
		};
		return acc;
		}, {});

		return combined;
	}

	async step4_Combined2(users, activeUsers, wishes, blessings, communities) {
		const allKeys = new Set([
			...Object.keys(users),
			...Object.keys(activeUsers),
			...Object.keys(wishes),
			...Object.keys(blessings),
			...Object.keys(communities)
		]);
		
		const combined = Array.from(allKeys).map(date => ({
			date,
			userCount: users[date] || 0,
			activeUserCount: activeUsers[date] || 0,
			wishCount: wishes[date] || 0,
			blessingCount: blessings[date] || 0,
			communityCount: communities[date] || 0
		}));
		
		const sortedCombined = combined.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
		return sortedCombined;
	}

	async latestCreatedAt() {
		try {
			const now = new Date();
			const nowUTC = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), now.getUTCHours(), now.getUTCMinutes(), now.getUTCSeconds(), now.getUTCMilliseconds()));

			const latestUser = await baseDao.findOne("users", { status: { $ne: STATUS.DELETED } }, { _id: 0, createdAt: 1 }, {}, { createdAt: 1 });
			const latestWish = await baseDao.findOne("wishes", { status: { $ne: STATUS.DELETED } }, { _id: 0, createdAt: 1 }, {}, { createdAt: 1 });
			const latestBlessing = await baseDao.findOne("perform_blessings", { status: { $ne: STATUS.DELETED }, isGratitude: false }, { _id: 0, createdAt: 1 }, {}, { createdAt: 1 });
			const latestCommunity = await baseDao.findOne("communities", { status: { $ne: STATUS.DELETED } }, { _id: 0, createdAt: 1 }, {}, { createdAt: 1 });

			if(!latestUser && !latestWish && !latestBlessing && !latestCommunity) {
				return nowUTC;
			} else {
				const latest = [];
				latestUser ? latest.push(latestUser.createdAt) : false;
				latestWish ? latest.push(latestWish.createdAt) : false
				latestBlessing ? latest.push(latestBlessing.createdAt) : false;
				latestCommunity ? latest.push(latestCommunity.createdAt) : false;
				return latest.sort((a, b) => new Date(a).getTime() - new Date(b).getTime())[0];
			}
		} catch (error) {
			throw error;
		}
	}

	async transformDate(dateString, type) {
		const year = dateString.slice(11, 15);
		const date = dateString.slice(8, 10);
		const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
		let month: any = months.indexOf(dateString.slice(4, 7)) + 1;
		month = month < 10 ? '0'+month : month;
		if(type == "START") {
			return new Date(Date.UTC(year, month - 1, date, 0, 0, 0, 0));
		} else {
			return new Date(Date.UTC(year, month - 1, date, 23, 59, 59, 999));
		}
	}
	
	/**
	 * @function dashboardExpansionDate
	 */
	async dashboardExpansionDate(params: AdminRequest.DashboardExpansion) {
		try {
			let startDate;
			let endDate;
			const now = new Date();
			const nowUTC = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), now.getUTCHours(), now.getUTCMinutes(), now.getUTCSeconds(), now.getUTCMilliseconds()));

			if(params.startDate) {
				startDate = await this.transformDate(params.startDate, "START");
			} else {
				startDate = await this.latestCreatedAt();
			}

			if(params.endDate) {
				endDate = await this.transformDate(params.endDate, "END");
			} else {
				endDate = nowUTC
			}
			
			const STEP1 = await this.step1(startDate, endDate);
			// console.log(STEP1)

			const findUsersAll = await this.aggregate("users", [
				{
					$project: {
						__id: 1,
						createdAt: 1,
						status: 1
					}
				},
				{
					$match: {
						createdAt: {
							$lte: endDate,
							$gte: startDate
						},
						status: {
							$ne: STATUS.DELETED
						}
					},
				},
				{
					$group: {
						_id: {
							year: { $year: "$createdAt" }, 
							month: { $month: "$createdAt" }, 
							day: { $dayOfMonth: "$createdAt" }
						},
						count: { $sum: 1 }
					}
				}
			]);

			const ALL_USERS_GROUP = await this.step2(findUsersAll);
			const ALL_USERS = await this.step3(STEP1, ALL_USERS_GROUP);
			// console.log(ALL_USERS)

			const findUsersActive = await this.aggregate("users", [
				{
					$project: {
						__id: 1,
						createdAt: 1,
						status: 1
					}
				},
				{
					$match: {
						createdAt: {
							$lte: endDate,
							$gte: startDate
						},
						status: STATUS.UN_BLOCKED
					},
				},
				{
					$group: {
						_id: {
							year: { $year: "$createdAt" }, 
							month: { $month: "$createdAt" }, 
							day: { $dayOfMonth: "$createdAt" }
						},
						count: { $sum: 1 }
					}
				}
			]);

			const ACTIVE_USERS_GROUP = await this.step2(findUsersActive);
			const ACTIVE_USERS = await this.step3(STEP1, ACTIVE_USERS_GROUP);
			// console.log(ACTIVE_USERS)

			const findWishes = await this.aggregate("wishes", [
				{
					$project: {
						__id: 1,
						createdAt: 1,
						status: 1,
						isGlobalWish: 1
					}
				},
				{
					$match: {
						createdAt: {
							$lte: endDate,
							$gte: startDate
						},
						status: {
							$ne: STATUS.DELETED
						},
						isGlobalWish: false
					},
				},
				{
					$group: {
						_id: {
							year: { $year: "$createdAt" }, 
							month: { $month: "$createdAt" }, 
							day: { $dayOfMonth: "$createdAt" }
						},
						count: { $sum: 1 }
					}
				}
			]);

			const ALL_WISHES_GROUP = await this.step2(findWishes);
			const ALL_WISHES = await this.step3(STEP1, ALL_WISHES_GROUP);
			// console.log(ALL_WISHES)

			const findBlessings = await this.aggregate("perform_blessings", [
				{
					$project: {
						__id: 1,
						createdAt: 1,
						status: 1,
						isGratitude: 1
					}
				},
				{
					$match: {
						createdAt: {
							$lte: endDate,
							$gte: startDate
						},
						status: {
							$ne: STATUS.DELETED
						},
						isGratitude: false
					},
				},
				{
					$group: {
						_id: {
							year: { $year: "$createdAt" }, 
							month: { $month: "$createdAt" }, 
							day: { $dayOfMonth: "$createdAt" }
						},
						count: { $sum: 1 }
					}
				}
			]);

			const ALL_BLESSINGS_GROUP = await this.step2(findBlessings);
			const ALL_BLESSINGS = await this.step3(STEP1, ALL_BLESSINGS_GROUP);
			// console.log(ALL_BLESSINGS)

			const findCommunities = await this.aggregate("communities", [
				{
					$project: {
						__id: 1,
						createdAt: 1,
						status: 1
					}
				},
				{
					$match: {
						createdAt: {
							$lte: endDate,
							$gte: startDate
						},
						status: {
							$ne: STATUS.DELETED
						}
					},
				},
				{
					$group: {
						_id: {
							year: { $year: "$createdAt" }, 
							month: { $month: "$createdAt" }, 
							day: { $dayOfMonth: "$createdAt" }
						},
						count: { $sum: 1 }
					}
				}
			]);

			const ALL_COMMUNITIES_GROUP = await this.step2(findCommunities);
			const ALL_COMMUNITIES = await this.step3(STEP1, ALL_COMMUNITIES_GROUP);
			// console.log(ALL_COMMUNITIES)

			const combined = await this.step4_Combined2(ALL_USERS, ACTIVE_USERS, ALL_WISHES, ALL_BLESSINGS, ALL_COMMUNITIES);
			// console.log(combined)

			// HANDLE MANUAL PAGINATION
			const pageNumber = params.pageNo;
			const pageSize = params.limit;
			const startIndex = (pageNumber - 1) * pageSize;
			const endIndex = pageNumber * pageSize;
			const paginatedItems = combined.slice(startIndex, endIndex);

			return {
				data: paginatedItems,
				total: combined.length,
				pageNo: params.pageNo,
				totalPage: Math.ceil(combined.length / params.limit),
				nextHit: params.pageNo + 1,
				limit: params.limit
			};
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function sheet1_WishData
	 */
	async sheet1_WishData(params: AdminRequest.DashboardExpansionExport) {
		try {
			let startDate;
			let endDate;
			const now = new Date();
			const nowUTC = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), now.getUTCHours(), now.getUTCMinutes(), now.getUTCSeconds(), now.getUTCMilliseconds()));
			
			if(params.startDate) {
				startDate = await this.transformDate(params.startDate, "START");
			} else {
				const latestWish = await baseDao.findOne("wishes", { isGlobalWish: false } , { _id: 0, createdAt: 1 }, {}, { createdAt: 1 });
				if(!latestWish) startDate = nowUTC;
				else startDate = latestWish.createdAt;
			}

			if(params.endDate) {
				endDate = await this.transformDate(params.endDate, "END");
			} else {
				endDate = nowUTC
			}

			const match = {
				isGlobalWish: false,
				createdAt: {
					$lte: endDate,
					$gte: startDate
				}
			};

			return await this.aggregate("wishes", [
				{
					$project: { _id: 1, isGlobalWish: 1, wishNumber: 1, intension: 1, status: 1, createdAt: 1, userId: 1, hostId: 1, creatorDetail: 1, hostDetail: 1, closeDate: 1, wishType: 1, communityId: 1 }
				},
				{
					$match: match
				},
				{
					$lookup: {
						from: DB_MODEL_REF.USER,
						localField: "userId",
						foreignField: "_id",
						as: "creatorDetail",
						pipeline: [
							{
								$project: { _id: 0, email: 1, oldEmail: 1, status: 1, country: 1, state: 1, city: 1 }
							}
						]
					}
				},
				{
					$unwind: {
						path: "$creatorDetail",
						preserveNullAndEmptyArrays: true
					}
				},
				{
					$lookup: {
						from: DB_MODEL_REF.USER,
						localField: "hostId",
						foreignField: "_id",
						as: "hostDetail",
						pipeline: [
							{
								$project: { _id: 0, email: 1, oldEmail: 1, status: 1, "location.address": 1, country: 1, state: 1, city: 1 }
							}
						]
					}
				},
				{
					$unwind: {
						path: "$hostDetail",
						preserveNullAndEmptyArrays: true
					}
				},
				{
					$lookup: {
						from: DB_MODEL_REF.TAGWISHES,
						localField: "_id",
						foreignField: "wishId",
						as: "tagDetail",
						pipeline: [
							{
								$project: { _id: 0, type: 1, communityId: 1 }
							},
							{
								$match: {
									type: {
										$nin: [WISH_TAG_TYPE.HOST, WISH_TAG_TYPE.INVITED_HOST]
									},
									communityId: {
										$exists: false
									}
								}
							}
						]
					}
				},
				// {
				// 	$lookup: {
				// 		from: DB_MODEL_REF.TAGWISHES,
				// 		localField: "_id",
				// 		foreignField: "wishId",
				// 		as: "tagDetailCommunity",
				// 		pipeline: [
				// 			{
				// 				$project: { _id: 0, type: 1, communityId: 1 }
				// 			},
				// 			{
				// 				$match: {
				// 					type: {
				// 						$nin: [WISH_TAG_TYPE.HOST, WISH_TAG_TYPE.INVITED_HOST]
				// 					},
				// 					communityId: {
				// 						$exists: true
				// 					}
				// 				}
				// 			},
				// 			{
				// 				$group: {
				// 					_id: "$communityId",
				// 					communityId: { $first: "$communityId" }
				// 				}
				// 			}
				// 		]
				// 	}
				// },
				{
					$addFields: {
						userTagCount: {
							$size: {
								$filter: {
									input: "$tagDetail",
									as: "item",
									cond: {
										$in: ["$$item.type", [WISH_TAG_TYPE.CONTACTS, WISH_TAG_TYPE.INVITED]]
									}
								}
							}
						},
						cohostTagCount: {
							$size: {
								$filter: {
									input: "$tagDetail",
									as: "item",
									cond: {
										$eq: ["$$item.type", WISH_TAG_TYPE.COHOST]
									}
								}
							}
						},
						// communityTagCount: {
						// 	$size: "$tagDetailCommunity"
						// }
					}
				},
				{
					// $project: { tagDetail: 0, tagDetailCommunity: 0 }
					$project: { tagDetail: 0 }
				},
				{
					$sort: { createdAt: -1 }
				}
			]);
		} catch (error) {
			throw error;
		}
	}
	
	/**
	 * @function sheet1_WishDataMapping
	 */
	async sheet1_WishDataMapping(data: any) {
		try {
			const exportedData = [];
			let obj: any;
			data.forEach(item => {
				obj = {
					"Wish number": `W-${item?.wishNumber}`,
					"Creator ID": item?.creatorDetail?.status == STATUS.DELETED ? item?.creatorDetail?.oldEmail : item?.creatorDetail?.email,
					// "Wish Type": item?.wishType,
					"Host ID": item?.hostDetail?.status == STATUS.DELETED ? item?.hostDetail?.oldEmail : item?.hostDetail?.email,
					"Wish intention": item?.intension,
					"Creator Location": `${item?.creatorDetail?.city == undefined ? "" : item?.creatorDetail?.city+","} ${item?.creatorDetail?.state == undefined ? "" : item?.creatorDetail?.state+","} ${item?.creatorDetail?.country == undefined ? "" : item?.creatorDetail?.country}`,
					"Creation Date": item?.createdAt,
					"Count of Users Tagged": item?.userTagCount,
					// "Count of Communities Tagged": item?.communityTagCount,
					"Count of Communities Tagged": item?.communityId.length,
					"Count of Co-Hosts Tagged": item?.cohostTagCount,
					// "Host Location": item?.hostDetail?.location?.address,
					"Host Location": `${item?.hostDetail?.city == undefined ? "" : item?.hostDetail?.city+","} ${item?.hostDetail?.state == undefined ? "" : item?.hostDetail?.state+","} ${item?.hostDetail?.country == undefined ? "" : item?.hostDetail?.country}`,
					"Close Date": item?.closeDate
				}
				exportedData.push(obj);
			});
			return exportedData;
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function sheet2_BlessingData
	 */
	async sheet2_BlessingData(params: AdminRequest.DashboardExpansionExport) {
		try {
			let startDate;
			let endDate;
			const now = new Date();
			const nowUTC = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), now.getUTCHours(), now.getUTCMinutes(), now.getUTCSeconds(), now.getUTCMilliseconds()));
			
			if(params.startDate) {
				startDate = await this.transformDate(params.startDate, "START");
			} else {
				const latestBlessing = await baseDao.findOne("perform_blessings", { isGratitude: false } , { _id: 0, createdAt: 1 }, {}, { createdAt: 1 });
				if(!latestBlessing) startDate = nowUTC;
				else startDate = latestBlessing.createdAt;
			}

			if(params.endDate) {
				endDate = await this.transformDate(params.endDate, "END");
			} else {
				endDate = nowUTC
			}

			const match = {
				isGratitude: false,
				createdAt: {
					$lte: endDate,
					$gte: startDate
				}
			};

			return await this.aggregate("perform_blessings", [
				{
					$project: { _id: 1, blessingNumber: 1, createdAt: 1, wishDetail: 1, type: 1, audio: 1, notes: 1, creatorDetail: 1, isGratitude: 1, status: 1, wishId: 1, userId: 1 }
				},
				{
					$match: match
				},
				{
					$lookup: {
						from: DB_MODEL_REF.WISHES,
						localField: "wishId",
						foreignField: "_id",
						as: "wishDetail",
						pipeline: [
							{
								$project: { _id: 0, wishNumber: 1, "location.address": 1, userId: 1 }
							}
						]
					}
				},
				{
					$unwind: {
						path: "$wishDetail",
						preserveNullAndEmptyArrays: true
					}
				},
				{
					$lookup: {
						from: DB_MODEL_REF.USER,
						localField: "wishDetail.userId",
						foreignField: "_id",
						as: "wishCreatorDetail",
						pipeline: [
							{
								$project: { _id: 0, email: 1, country: 1, state: 1, city: 1 }
							}
						]
					}
				},
				{
					$unwind: {
						path: "$wishCreatorDetail",
						preserveNullAndEmptyArrays: true
					}
				},
				{
					$lookup: {
						from: DB_MODEL_REF.USER,
						localField: "userId",
						foreignField: "_id",
						as: "creatorDetail",
						pipeline: [
							{
								$project: { _id: 0, email: 1, oldEmail: 1, "location.address": 1, country: 1, state: 1, city: 1, status: 1 }
							}
						]
					}
				},
				{
					$unwind: {
						path: "$creatorDetail",
						preserveNullAndEmptyArrays: true
					}
				},
				{
					$project: { _id: 0, isGratutude: 0, wishId: 0, userId: 0 }
				},
				{
					$sort: { createdAt: -1 }
				}
			]);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function sheet2_BlessingDataMapping
	 */
	async sheet2_BlessingDataMapping(data: any) {
		try {
			const exportedData = [];
			let obj: any;
			data.forEach(item => {
				obj = {
					"Blessing number": `B-${item?.blessingNumber}`,
					"Associated Wish number": `W-${item?.wishDetail?.wishNumber}`,
					"Creator ID": item?.creatorDetail?.status == STATUS.DELETED ? item?.creatorDetail?.oldEmail : item?.creatorDetail?.email,
					"Creator Location": `${item?.creatorDetail?.city == undefined ? "" : item?.creatorDetail?.city+","} ${item?.creatorDetail?.state == undefined ? "" : item?.creatorDetail?.state+","} ${item?.creatorDetail?.country == undefined ? "" : item?.creatorDetail?.country}`,
					"Recipient Location": `${item?.wishCreatorDetail?.city == undefined ? "" : item?.wishCreatorDetail?.city+","} ${item?.wishCreatorDetail?.state == undefined ? "" : item?.wishCreatorDetail?.state+","} ${item?.wishCreatorDetail?.country == undefined ? "" : item?.wishCreatorDetail?.country}`,
					"Creation Date": item?.createdAt,
					"Audio/Note": item?.type == BLESSING_PERFORM_TYPE.TEXT ? "Note" : "Audio"
				}
				exportedData.push(obj);
			});
			return exportedData;
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function sheet2_GratitudeData
	 */
	async sheet3_GratitudeData(params: AdminRequest.DashboardExpansionExport) {
		try {
			let startDate;
			let endDate;
			const now = new Date();
			const nowUTC = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), now.getUTCHours(), now.getUTCMinutes(), now.getUTCSeconds(), now.getUTCMilliseconds()));
			
			if(params.startDate) {
				startDate = await this.transformDate(params.startDate, "START");
			} else {
				const latestGratitude = await baseDao.findOne("gratitudes", {}, { _id: 0, createdAt: 1 }, {}, { createdAt: 1 });
				if(!latestGratitude) startDate = nowUTC;
				else startDate = latestGratitude.createdAt;
			}

			if(params.endDate) {
				endDate = await this.transformDate(params.endDate, "END");
			} else {
				endDate = nowUTC
			}

			const match = {
				createdAt: {
					$lte: endDate,
					$gte: startDate
				}
			};

			return await this.aggregate("gratitudes", [
				{
					$project: { _id: 1, gratitudeNumber: 1, createdAt: 1, type: 1, notes: 1, audio: 1, wishId: 1, status: 1, userId: 1 }
				},
				{
					$match: match
				},
				{
					$lookup: {
						from: DB_MODEL_REF.WISHES,
						localField: "wishId",
						foreignField: "_id",
						as: "wishDetail",
						pipeline: [
							{
								$project: { _id: 0, wishNumber: 1, "location.address": 1, userId: 1 }
							}
						]
					}
				},
				{
					$unwind: {
						path: "$wishDetail",
						preserveNullAndEmptyArrays: true
					}
				},
				{
					$lookup: {
						from: DB_MODEL_REF.USER,
						localField: "wishDetail.userId",
						foreignField: "_id",
						as: "wishCreatorDetail",
						pipeline: [
							{
								$project: { _id: 0, email: 1, oldEmail: 1, status: 1, country: 1, state: 1, city: 1 }
							}
						]
					}
				},
				{
					$unwind: {
						path: "$wishCreatorDetail",
						preserveNullAndEmptyArrays: true
					}
				},
				{
					$lookup: {
						from: DB_MODEL_REF.USER,
						localField: "userId",
						foreignField: "_id",
						as: "creatorDetail",
						pipeline: [
							{
								$project: { _id: 0, email: 1, oldEmail: 1, status: 1, "location.address": 1, country: 1, state: 1, city: 1 }
							}
						]
					}
				},
				{
					$unwind: {
						path: "$creatorDetail",
						preserveNullAndEmptyArrays: true
					}
				},
				{
					$project: { _id: 0, wishId: 0, userId: 0, "wishDetail.userId": 0 }
				},
				{
					$sort: { createdAt: 1 }
				}
			]);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function sheet2_GratitudeDataMapping
	 */
	async sheet3_GratitudeDataMapping(data: any) {
		try {
			const exportedData = [];
			let obj: any;
			data.forEach(item => {
				obj = {
					"Gratitude number": `G-${item?.gratitudeNumber}`,
					"Associated Wish number": `W-${item?.wishDetail?.wishNumber}`,
					"Creator ID": item?.creatorDetail?.status == STATUS.DELETED ? item?.creatorDetail?.oldEmail : item?.creatorDetail?.email,
					"Creator Location": `${item?.creatorDetail?.city == undefined ? "" : item?.creatorDetail?.city+","} ${item?.creatorDetail?.state == undefined ? "" : item?.creatorDetail?.state+","} ${item?.creatorDetail?.country == undefined ? "" : item?.creatorDetail?.country}`,
					"Recipient ID": item?.wishCreatorDetail?.status == STATUS.DELETED ? item?.wishCreatorDetail?.oldEmail : item?.wishCreatorDetail?.email,
					"Recipient Location": `${item?.wishCreatorDetail?.city == undefined ? "" : item?.wishCreatorDetail?.city+","} ${item?.wishCreatorDetail?.state == undefined ? "" : item?.wishCreatorDetail?.state+","} ${item?.wishCreatorDetail?.country == undefined ? "" : item?.wishCreatorDetail?.country}`,
					"Creation Date": item?.createdAt,
					"Audio/Note": item?.type == BLESSING_PERFORM_TYPE.TEXT ? "Note" : "Audio"
				}
				exportedData.push(obj);
			});
			return exportedData;
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function createBlessing
	 */
	async createBlessing(params) {
		try {
			return await this.insert("blessing_library", params, {});
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function sheet6_ThankData
	 */
	async sheet6_ThankData(params: AdminRequest.DashboardExpansionExport) {
		try {
			let startDate;
			let endDate;
			const now = new Date();
			const nowUTC = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), now.getUTCHours(), now.getUTCMinutes(), now.getUTCSeconds(), now.getUTCMilliseconds()));
			
			if(params.startDate) {
				startDate = await this.transformDate(params.startDate, "START");
			} else {
				const latestThank = await baseDao.findOne("wishwell_thanks", {}, { _id: 0, createdAt: 1 }, {}, { createdAt: 1 });
				if(!latestThank) startDate = nowUTC;
				else startDate = latestThank.createdAt;
			}

			if(params.endDate) {
				endDate = await this.transformDate(params.endDate, "END");
			} else {
				endDate = nowUTC
			}

			const match = {
				createdAt: {
					$lte: endDate,
					$gte: startDate
				}
			};

			return await this.aggregate("wishwell_thanks", [
				{
					$match: match
				},
				{
					$project: { _id: 1, serialNumber: 1, userId: 1, type: 1, note: 1, audio: 1, amount: 1, paymentStatus: 1, isAllowShare: 1, createdAt: 1 }
				},
				{
					$lookup: {
						from: DB_MODEL_REF.USER,
						localField: "userId",
						foreignField: "_id",
						as: "creatorDetail",
						pipeline: [
							{
								$project: { _id: 0, email: 1, oldEmail: 1, status: 1, "location.address": 1 , country: 1, state: 1, city: 1}
							}
						]
					}
				},
				{
					$unwind: {
						path: "$creatorDetail",
						preserveNullAndEmptyArrays: true
					}
				},
				{
					$sort: { createdAt: -1 }
				}
			]);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function sheet6_ThankDataMapping
	 */
	async sheet6_ThankDataMapping(data: any) {
		try {
			const exportedData = [];
			let obj: any;
			data.forEach(item => {
				obj = {
					"Thank WW Number": `T-${item?.serialNumber}`,
					"Creator ID": item?.creatorDetail?.status == STATUS.DELETED ? item?.creatorDetail?.oldEmail : item?.creatorDetail?.email,
					"Creation Date": item?.createdAt,
					"Location": item?.creatorDetail?.location?.address && item?.creatorDetail?.location?.address !== "" ? item?.creatorDetail?.location?.address : `${item?.creatorDetail?.city == undefined ? "" : item?.creatorDetail?.city+","} ${item?.creatorDetail?.state == undefined ? "" : item?.creatorDetail?.state+","} ${item?.creatorDetail?.country == undefined ? "" : item?.creatorDetail?.country}`,
					"Note type (Note/Audio)": item?.type == BLESSING_PERFORM_TYPE.TEXT ? "Note" : "Audio",
					"Note": item?.type == BLESSING_PERFORM_TYPE.TEXT ? item?.note : "",
					"Amount of Donation": item?.amount,
					"Released": item?.isAllowShare ? 'Yes' : 'No'
				}
				exportedData.push(obj);
			});
			return exportedData;
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function sheet7_LibraryData
	 */
	async sheet7_LibraryData(params: AdminRequest.DashboardExpansionExport) {
		try {
			let startDate;
			let endDate;
			const now = new Date();
			const nowUTC = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), now.getUTCHours(), now.getUTCMinutes(), now.getUTCSeconds(), now.getUTCMilliseconds()));
			
			if(params.startDate) {
				startDate = await this.transformDate(params.startDate, "START");
			} else {
				const latestBlessingLibrary = await baseDao.findOne("blessing_library", { status: { $ne: STATUS.DELETED } }, { _id: 0, createdAt: 1 }, {}, { createdAt: 1 });
				if(!latestBlessingLibrary) startDate = nowUTC;
				else startDate = latestBlessingLibrary.createdAt;
			}

			if(params.endDate) {
				endDate = await this.transformDate(params.endDate, "END");
			} else {
				endDate = nowUTC
			}

			const match = {
				status: {
					$ne: STATUS.DELETED
				},
				createdAt: {
					$lte: endDate,
					$gte: startDate
				}
			};
			
			return await this.aggregate("blessing_library", [
				{
					$match: match
				},
				{
					$lookup: {
						from: DB_MODEL_REF.PERFORM_BLESSING,
						localField: "_id",
						foreignField: "listenBlessingId",
						as: "libraryDetail",
						pipeline: [
							{
								$project: { _id: 0, status: 1 }
							}
						]
					}
				},
				// {
				// 	$unwind: {
				// 		path: "$libraryDetail",
				// 		preserveNullAndEmptyArrays: true
				// 	}
				// },
				{
					$project: { _id: 0, name: 1, authorName: 1, intension: 1, blessingType: 1, voiceover: 1, language: 1, createdAt: 1, uses: { $size: "$libraryDetail" } }
				},
				{
					$sort: { createdAt: -1 }
				}
			]);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function sheet7_LibraryDataMapping
	 */
	async sheet7_LibraryDataMapping(data: any) {
		try {
			const exportedData = [];
			let obj: any;
			data.forEach(item => {
				obj = {
					"Name": item?.name,
					"Author": item?.authorName,
					"Intention": item?.intension,
					"Type": BLESSING_TYPE[item?.blessingType],
					"Voiceover": item?.voiceover,
					"Uses": item?.uses,
					"Language": item?.language
				}
				exportedData.push(obj);
			});
			return exportedData;
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function sheet5_CommunityData
	 */
	async sheet5_CommunityData(params: AdminRequest.DashboardExpansionExport) {
		try {
			let startDate;
			let endDate;
			const now = new Date();
			const nowUTC = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), now.getUTCHours(), now.getUTCMinutes(), now.getUTCSeconds(), now.getUTCMilliseconds()));
			
			if(params.startDate) {
				startDate = await this.transformDate(params.startDate, "START");
			} else {
				const latestCommunity = await baseDao.findOne("communities", {}, { _id: 0, createdAt: 1 }, {}, { createdAt: 1 });
				if(!latestCommunity) startDate = nowUTC;
				else startDate = latestCommunity.createdAt;
			}

			if(params.endDate) {
				endDate = await this.transformDate(params.endDate, "END");
			} else {
				endDate = nowUTC
			}

			const match = {
				createdAt: {
					$lte: endDate,
					$gte: startDate
				}
			};

			let communityListing = await this.aggregate("communities", [
				{
					$match: match
				},
				{
					$lookup: {
						from: DB_MODEL_REF.USER,
						localField: "userId",
						foreignField: "_id",
						as: "userDetail",
						pipeline: [
							{
								$project: { _id: 0, email: 1, oldEmail: 1, status: 1 }
							}
						]
					}
				},
				{
					$unwind: {
						path: "$userDetail",
						preserveNullAndEmptyArrays: true
					}
				},
				{
					$lookup: {
						from: DB_MODEL_REF.MEMBERS,
						localField: "_id",
						foreignField: "communityId",
						as: "communityDetail",
						pipeline: [
							{
								$project: { _id: 0, status: 1 }
							},
							{
								$match: {
									status: STATUS.ACTIVE
								}
							}
						]
					}
				},
				{
					$lookup: {
						from: DB_MODEL_REF.REPORTS,
						localField: "_id",
						foreignField: "subjectId",
						as: "reportDetail",
						pipeline: [
							{
								$project: { _id: 0, status: 1, type: 1 }
							},
							{
								$match: {
									type: REPORT_TYPE.COMMUNITY,
									status: {
										$ne: STATUS.DELETED
									}
								}
							}
						]
					}
				},
				{
					$lookup: {
						from: DB_MODEL_REF.TAGWISHES,
						localField: "_id",
						foreignField: "communityId",
						as: "tagWishReport",
						pipeline: [
							{
								$project: { _id: 0, status: 1, communityId: 1, wishId: 1 }
							},
							// {
							// 	$match: {
							// 		status: {
							// 			$ne: STATUS.DELETED
							// 		}
							// 	}
							// },
							{
								$group: {
									_id: "$wishId",
									status: { $first: "$status" }
								}
							},
							{
								$lookup: {
									from: DB_MODEL_REF.PERFORM_BLESSING,
									localField: "_id",
									foreignField: "wishId",
									as: "tagWishBlessingReport",
									pipeline: [
										{
											$project: { _id: 0, status: 1 }
										},
										// {
										// 	$match: {
										// 		status: {
										// 			$ne: STATUS.DELETED
										// 		}
										// 	}
										// }
									]
								}
							}
						]
					}
				},
				// {
				// 	$addFields: {
				// 		wishIds: {
				// 			$map: {
				// 				input: "$tagWishReport",
				// 				as: "wish",
				// 				in: "$$wish._id"
				// 			}
				// 		}
				// 	}
				// },
				{
					$project: { _id: 0, communityNumber: 1, name: 1, status: 1, flagStatus: 1, createdAt: 1, userDetail: 1,  memberCount: { $size: "$communityDetail" }, reportCount: { $size: "$reportDetail" }, tagWishReportCount: { $size: "$tagWishReport" }, tagWishReport: 1 }
				},
				{
					$sort: { createdAt: -1 }
				}
			]);

			communityListing = communityListing.map(data => {
				let blessingCount = 0;
				for(let i in data.tagWishReport) {
					blessingCount = blessingCount + data.tagWishReport[i].tagWishBlessingReport.length;
				}
				data.tagWishBlessingReportCount = blessingCount;
				delete data.tagWishReport;
				return data;
			});

			return communityListing;
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function sheet5_CommunityDataMapping
	 */
	async sheet5_CommunityDataMapping(data: any) {
		try {
			const exportedData = [];
			let obj: any;
			data.forEach(item => {
				obj = {
					"Community Number": `C-${item?.communityNumber}`,
					"Community Name": item?.name,
					"Creator ID": item?.userDetail?.status == STATUS.DELETED ? item?.userDetail?.oldEmail : item?.userDetail?.email,
					"Creation Date": item?.createdAt,
					"Number times reported": item?.reportCount,
					"Status": item?.status == STATUS.DELETED ? item?.status : item?.flagStatus.replace("_", ""),
					"Count of Members": item?.memberCount,
					"Wishes Tagged to": item?.tagWishReportCount,
					"Blessings Performed by Community Members on Wishes Tagged to": item?.tagWishBlessingReportCount
				}
				exportedData.push(obj);
			});
			return exportedData;
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function sheet4_UserData
	 */
	async sheet4_UserData(params: AdminRequest.DashboardExpansionExport) {
		try {
			let startDate;
			let endDate;
			const now = new Date();
			const nowUTC = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), now.getUTCHours(), now.getUTCMinutes(), now.getUTCSeconds(), now.getUTCMilliseconds()));
			
			if(params.startDate) {
				startDate = await this.transformDate(params.startDate, "START");
			} else {
				const latestUser = await baseDao.findOne("users", {}, { _id: 0, createdAt: 1 }, {}, { createdAt: 1 });
				if(!latestUser) startDate = nowUTC;
				else startDate = latestUser.createdAt;
			}

			if(params.endDate) {
				endDate = await this.transformDate(params.endDate, "END");
			} else {
				endDate = nowUTC
			}

			const match = {
				createdAt: {
					$lte: endDate,
					$gte: startDate
				}
			};
			
			return await this.aggregate("users", [
				{
					$match: match
				},
				{
					$lookup: {
						from: DB_MODEL_REF.REPORTS,
						localField: "_id",
						foreignField: "ownerId",
						as: "reportDetail",
						pipeline: [
							{
								$project: { _id: 0, status: 1 }
							},
							{
								$match: {
									status: {
										$ne: STATUS.DELETED
									}
								}
							}
						]
					}
				},
				{
					$lookup: {
						from: DB_MODEL_REF.WISHWELL_THANKS,
						localField: "_id",
						foreignField: "userId",
						as: "wwThankDetail",
						pipeline: [
							{
								$project: { _id: 0, type: 1 }
							}
						]
					}
				},
				{
					$lookup: {
						from: DB_MODEL_REF.MEMBERS,
						localField: "_id",
						foreignField: "userId",
						as: "communityDetail",
						pipeline: [
							{
								$project: { _id: 0, status: 1 }
							},
							{
								$match: {
									status: STATUS.ACTIVE
								}
							}
						]
					}
				},
				{
					$lookup: {
						from: DB_MODEL_REF.WISHES,
						localField: "invitationWishId",
						foreignField: "_id",
						as: "invitationWishDetail",
						pipeline: [
							{
								$project: { _id: 0, userId: 1 }
							}
						]
					}
				},
				{
					$unwind: {
						path: "$invitationWishDetail",
						preserveNullAndEmptyArrays: true
					}
				},
				{
					$lookup: {
						from: DB_MODEL_REF.USER,
						localField: "invitationWishDetail.userId",
						foreignField: "_id",
						as: "invitationWishUserDetail",
						pipeline: [
							{
								$project: { _id: 0, email: 1, oldEmail: 1, status: 1 }
							}
						]
					}
				},
				{
					$unwind: {
						path: "$invitationWishUserDetail",
						preserveNullAndEmptyArrays: true
					}
				},
				{
					$project: {
						_id: 0,
						email: 1,
						firstName: 1,
						lastName: 1,
						status: 1,
						createdAt: 1,
						itemFlagCount: { $size: "$reportDetail" },
						wwThankCount: { $size: "$wwThankDetail" },
						wishCount: 1,
						blessingCount: 1,
						gratitudeCount: 1,
						communityCount: 1,
						communityJoinCount: { $size: "$communityDetail" },
						invitationWishDetail: 1,
						invitationWishId: 1,
						invitationType: 1,
						invitationDate: 1,
						invitationWishUserDetail: 1,
						oldEmail: 1
					}
				},
				{
					$sort: { createdAt: -1 }
				}
			]);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function sheet4_UserDataMapping
	 */
	async sheet4_UserDataMapping(data: any) {
		try {
			const exportedData = [];
			let obj: any;
			data.forEach(item => {
				obj = {
					"User ID": item?.status == STATUS.DELETED ? item?.oldEmail : item?.email,
					// "User email": item?.email,
					"User Name": `${item?.firstName == undefined ? "" : item?.firstName} ${item?.lastName == undefined ? "" : item?.lastName}`,
					"Invitation Type": item?.invitationType,
					"Invitation sent date": item?.invitationDate,
					"Status": item?.status == STATUS.UN_BLOCKED ? STATUS.ACTIVE : item?.status == STATUS.BLOCKED ? STATUS.IN_ACTIVE.replace("_", "") : item?.status == STATUS.DELETED ? STATUS.DELETED : "",
					"Count of Items flagged": item?.itemFlagCount,
					"Referral User’s ID": item?.invitationWishUserDetail?.status == STATUS.DELETED ? item?.invitationWishUserDetail?.oldEmail : item?.invitationWishUserDetail?.email,
					"Member Since": item?.createdAt,
					"Count of Thank WW Notes": item.wwThankCount,
					"Count of Wishes Created": item?.wishCount,
					"Count of Blessings Performed": item?.blessingCount,
					"Count of Gratitude Given": item?.gratitudeCount,
					"Count of Communities Created": item?.communityCount,
					"Count of Communities Joined": item?.communityJoinCount
				}
				exportedData.push(obj);
			});
			return exportedData;
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function blessingListing
	 */
	async blessingListing(params) {
		try {
			let match: any = {};
			let aggPipe: any = [];
			let sort: any = {};

			match.status = {
				$in: [ STATUS.UN_PUBLISHED, STATUS.ACTIVE, STATUS.IN_ACTIVE ]
			};

			if(params.searchKey) {
				match["$or"] = [
					{ "name": { "$regex": params.searchKey, "$options": "i" } }
				]
			}

			if (params.sortBy == 1) {
				params.sortBy = 1;
			} else {
				params.sortBy = -1;
			}

			if (params.sortCriteria == BLESSING_SORT_CRITERIA.NAME) {
				sort = { "name": params.sortBy }
				aggPipe.push({ "$sort": sort });
			} else if (params.sortCriteria == BLESSING_SORT_CRITERIA.CREATED_AT) {
				sort = { "createdAt": params.sortBy }
				aggPipe.push({ "$sort": sort });
			} else if (params.sortCriteria == BLESSING_SORT_CRITERIA.STATUS) {
				sort = { "status": params.sortBy }
				aggPipe.push({ "$sort": sort });
			} else if (params.sortCriteria == BLESSING_SORT_CRITERIA.LANGUAGE) {
				sort = { "language": params.sortBy }
				aggPipe.push({ "$sort": sort });
			} else if (params.sortCriteria == BLESSING_SORT_CRITERIA.VOICEOVER) {
				sort = { "voiceover": params.sortBy }
				aggPipe.push({ "$sort": sort });
			} else if (params.sortCriteria == BLESSING_SORT_CRITERIA.INTENSION) {
				sort = { "intension": params.sortBy }
				aggPipe.push({ "$sort": sort });
			} else if (params.sortCriteria == BLESSING_SORT_CRITERIA.AUTHOR_NAME) {
				sort = { "authorName": params.sortBy }
				aggPipe.push({ "$sort": sort });
			} else {				
				aggPipe.push({
					$facet: {
						unpublished: [
						  	{ $match: { status: STATUS.UN_PUBLISHED } }
						],
						active: [
						  	{ $match: { status: STATUS.ACTIVE } }
						],
						inactive: [
						  	{ $match: { status: STATUS.IN_ACTIVE } }
						]
					}
				});
				aggPipe.push({
					$project: {
						blessings: { $concatArrays: ["$unpublished", "$active", "$inactive"] }
					}
				});
				aggPipe.push({ $unwind: "$blessings" });
				aggPipe.push({ $replaceRoot: { newRoot: "$blessings" } });
			}
			aggPipe.push({ "$match": match });

			const performBlessingLookup = {
				from: DB_MODEL_REF.PERFORM_BLESSING,
				localField: "_id",
				foreignField: "listenBlessingId",
				as: "performBlessingDetail",
				pipeline: [
					{
						$project: { _id: 0, status: 1 }
					},
					{
						$match: { status: { $ne: STATUS.DELETED } }
					}
				]
			}
			aggPipe.push({ "$lookup": performBlessingLookup });
			
			const project = { _id: 1, status: 1, image: 1, name: 1, blessingType: 1, language: 1, audioFile: 1, voiceover: 1, intension: 1, authorName: 1, createdAt: 1, uses: { $size: "$performBlessingDetail" } };
			aggPipe.push({ "$project": project });

			const options = { collation: true };
			return this.paginate("blessing_library", aggPipe, params.limit, params.pageNo, options, true);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function createWish
	 */
	async createWish(params: AdminRequest.CreateWish, tokenData: TokenData) {
		try {
			params.status = STATUS.UN_PUBLISHED;
			params.userId = tokenData.userId;
			params.createdBy = WISH_CREATED_BY.ADMIN;
			return await this.insert("wishes", params, {});
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function wishListing
	 */
	async wishListing(params: AdminRequest.WishList) {
		try {
			let match: any = {};
			let aggPipe: any = [];
			let sort: any = {};

			match["isGlobalWish"] = true;
			match["status"] = { $ne: STATUS.DELETED }

			if(params.searchKey) {
				match["$or"] = [
					{ "title": { "$regex": params.searchKey, "$options": "i" } }
				]
			}

			aggPipe.push({ "$match": match });

			const project = {
				_id: 1,
				isGlobalWish: 1,
				totalBlessings: 1,
				status: 1,
				title: 1,
				description: 1,
				intension: 1,
				createdAt: 1,
				wishNumber: 1,
				isGlobalPinned: 1,
				// disasterCategory: '$disasterCategoryDetails',
				// familyName: 1,
				blessingTime: { $sum: "$performBlessingDetail.listenDuration" }
			}

			if (params.sortBy == 1) {
				params.sortBy = 1;
			} else {
				params.sortBy = -1;
			}

			if(params.sortCriteria == WISH_SORT_CRITERIA.TITLE) {
				sort = { "title": params.sortBy }
				aggPipe.push({ "$sort": sort });
			} else if (params.sortCriteria == WISH_SORT_CRITERIA.INTENSION) {
				sort = { "intension": params.sortBy }
				aggPipe.push({ "$sort": sort });
			} else if (params.sortCriteria == WISH_SORT_CRITERIA.CREATED_AT) {
				sort = { "createdAt": params.sortBy }
				aggPipe.push({ "$sort": sort });
			} else {
				aggPipe.push({
					$facet: {
						pinned: [{
							$match: { isGlobalPinned: true }
						}, {
							$sort: { createdAt: -1 }
						}],
						unpublished: [{
							$match: { isGlobalPinned: false, status: STATUS.UN_PUBLISHED }
						}, {
							$sort: { createdAt: -1 }
						}],
						active: [{
							$match: { isGlobalPinned: false, status: STATUS.ACTIVE }
						}, {
							$sort: { createdAt: -1 }
						}],
						inactive: [{
							$match: { isGlobalPinned: false, status: { $in: [STATUS.IN_ACTIVE, STATUS.CLOSED] } }
						}, {
							$sort: { createdAt: -1 }
						}]
					}
				});
				aggPipe.push({
					$project: {
						wishes: { $concatArrays: [ "$pinned", "$unpublished", "$active", "$inactive" ] }
					}
				});
				aggPipe.push({ $unwind: "$wishes" });
				aggPipe.push({ $replaceRoot: { newRoot: "$wishes" } });
			}

			const performBlessingLookup = {
				from: DB_MODEL_REF.PERFORM_BLESSING,
				localField: "_id",
				foreignField: "wishId",
				as: "performBlessingDetail",
				pipeline: [
					{
						$project: {
							isGratitude: 1,
							listenDuration: 1,
							status: 1
						}
					},
					{
						$match: {
							isGratitude: false,
							status: {
								$ne: STATUS.DELETED
							}
						}
					}
				]
			}

			// // Lookup for disasterCategory
			// const disasterCategoriesLookup = {
			// 	from: "disaster_categories",
			// 	localField: "disasterCategory",
			// 	foreignField: "disasterCategory",
			// 	as: "disasterCategoryDetails"
			// };

			// // Unwind disaster category to simplify structure
			// const disasterCategoriesUnwind = {
			// 	path: "$disasterCategoryDetails",
			// 	preserveNullAndEmptyArrays: true
			// };

			aggPipe.push({ "$lookup": performBlessingLookup });
			// aggPipe.push({ "$lookup": disasterCategoriesLookup });
			// aggPipe.push({ "$unwind": disasterCategoriesUnwind });
			aggPipe.push({ "$project": project });

			const options = { collation: true };
			return this.paginate("wishes", aggPipe, params.limit, params.pageNo, options, true);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function wishDetails
	 */
	async wishDetails(params: AdminRequest.WishDetail) {
		try {
			const query: any = {};
			query._id = params.id;

			return await this.findOne("wishes", query);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function wishDetailsAgg
	 */
	async wishDetailsAgg(params: AdminRequest.WishDetail) {
		try {
			const data = await this.aggregate("wishes", [
				{
					$match: { _id: toObjectId(params.id) }
				},
				{
					$lookup: {
						from: DB_MODEL_REF.PERFORM_BLESSING,
						localField: "_id",
						foreignField: "wishId",
						as: "performBlessingDetail",
						pipeline: [
							{
								$project: {
									isGratitude: 1,
									listenDuration: 1,
									status: 1
								}
							},
							{
								$match: {
									isGratitude: false,
									status: {
										$ne: STATUS.DELETED
									}
								}
							}
						]
					}
				},
				{
					$lookup: {
						from: "disaster_categories", 
						localField: "disasterCategory",
						foreignField: "disasterCategory",
						as: "disasterCategoryDetails"
					}
				},
				{
					$unwind: {
						path: "$disasterCategoryDetails",
						preserveNullAndEmptyArrays: true
					}
				},
				{
					$project: {
						_id: 1,
						userId: 1,
						userDetail: 1,
						image: 1,
						isGlobalWish: 1,
						totalBlessings: 1,
						status: 1,
						title: 1,
						description: 1,
						intension: 1,
						createdAt: 1,
						wishNumber: 1,
						isGlobalPinned: 1,
						flagStatus: 1,
						unflagHistory: 1,
						deleteReason: 1,
						familyName: 1,
						disasterCategory: '$disasterCategoryDetails',
						location: 1,
						blessingTime: { $sum: "$performBlessingDetail.listenDuration" }
					}
				}
			]);
			return data[0];
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function editWish
	 */
	async editWish(params: AdminRequest.WishEdit) {
		try {
			const query: any = {};
			query["_id"] = params.id;
			
			const update = {};
			update["$set"] = params;
			return await this.updateOne("wishes", query, update, {});
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function deleteWish
	 */
	async deleteWish(params: AdminRequest.WishDetail) {
		try {
			const query: any = {};
			query["_id"] = params.id;
			
			const update = {};
			update["$set"] = { status: STATUS.DELETED, deletedAt: Date.now() };
			return this.updateOne("wishes", query, update, {});
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function flaggedWishes
	 */
	async flaggedWishes(params: AdminRequest.FlaggedWish) {
		try {
			let match: any = {};
			let aggPipe: any = [];
			let sort: any = {};

			match["flagStatus"] = { $in: [FLAG_TYPE.FLAGGED, FLAG_TYPE.UN_FLAGGED, FLAG_TYPE.DELETED] };

			if(params.searchKey) {
				match = {
					...match,
					$or: [
						{
							$expr: {
								$regexMatch: {
									input: { $concat: ['$userDetail.firstName', ' ', '$userDetail.lastName'] },
									regex: params.searchKey,
									options: 'i'
								}
							}
						},
						{ "wishNumber": { "$regex": params.searchKey, "$options": "i" } },
						{ "intension": { "$regex": params.searchKey, "$options": "i" } },
						{ "status": { "$regex": params.searchKey, "$options": "i" } }
					]
				}
			}

			aggPipe.push({ "$match": match });

			const project = {
				_id: 1, userId: 1, status: 1, wishType: 1, intension: 1, userDetail: 1, isReported: 1, reportCount: 1, wishNumber: 1, title: 1, description: 1, isFlagged: 1, flagStatus: 1, createdAt: 1, createdBy: 1
			};

			aggPipe.push({
				$addFields: {
					flagStatus: {
						$cond: [
							{ $eq: ["$status", STATUS.DELETED] },  // Check if status is "DELETED"
							"$status",                             // If true, set flagStatus to the value of status
							"$flagStatus"                          // Otherwise, keep flagStatus as is
						]
					}
				}
			});

			if (params.sortBy == 1) {
				params.sortBy = 1;
			} else {
				params.sortBy = -1;
			}

			if(params.sortCriteria == FLAGGED_WISH_SORT_CRITERIA.WISH_NUMBER) {
                sort = { wishNumber: params.sortBy }
            } else if (params.sortCriteria == FLAGGED_WISH_SORT_CRITERIA.WISH_OWNER) {
                sort = { "userDetail.firstName": params.sortBy }
            } else if (params.sortCriteria == FLAGGED_WISH_SORT_CRITERIA.INTENTION) {
                sort = { intension: params.sortBy }
            } else if (params.sortCriteria == FLAGGED_WISH_SORT_CRITERIA.STATUS) {
                sort = { "flagStatus": params.sortBy }
            } else if (params.sortCriteria == FLAGGED_WISH_SORT_CRITERIA.CREATED_AT) {
                sort = { "createdAt": params.sortBy }
            }

			aggPipe.push({ "$sort": sort });
			aggPipe.push({ "$project": project });
			
			const options = { collation: true };
			return this.paginate("wishes", aggPipe, params.limit, params.pageNo, options, true);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function saveUnflagWish
	 */
	async unflagWish(params: AdminRequest.WishDetail, tokenData: TokenData) {
		try {
			const query: any = {};
			const update: any = {};

			query["_id"] = params.id;
			update["$set"] = {
				isFlagged: false,
				flagStatus: FLAG_TYPE.UN_FLAGGED
			}
			update["$push"] = {
				unflagHistory: {
					adminId: tokenData.userId
				}
			}

			return this.updateOne("wishes", query, update, {});
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function unflagTagWishes
	 */
	async unflagTagWishes(params: AdminRequest.WishDetail) {
		try {
			const query: any = {};
			const update: any = {};

			query["wishId"] = params.id;
			update["$set"] = {
				isFlagged: false
			}

			return this.updateMany("tagwishes", query, update, {});
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function deleteFlaggedWish
	 */
	async deleteFlaggedWish(params: AdminRequest.WishDetail) {
		try {
			const query: any = {};
			const update: any = {};

			query["_id"] = params.id;
			update["$set"] = {
				status: STATUS.DELETED,
				flagStatus: STATUS.DELETED,
				deleteReason: params.deleteReason,
				deletedAt: Date.now()
			}

			return await this.updateOne("wishes", query, update, {});
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function deleteTaggedWish
	 */
	async deleteTaggedWish(params: AdminRequest.WishDetail) {
		try {
			const query: any = {};
			const update: any = {};

			query["wishId"] = params.id;
			update["$set"] = {
				status: STATUS.DELETED
			}

			return await this.updateMany("tagwishes", query, update, {});
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function flaggedWishReports
	 */
	async flaggedWishReports(params: AdminRequest.FlaggedWish) {
		try {
			let match: any = {};
			let aggPipe: any = [];
			let sort: any = {};

			match["subjectId"] = Types.ObjectId(params.wishId);
			match["type"] = REPORT_TYPE.WISH;

			const userLookup = {
				from: DB_MODEL_REF.USER,
				localField: "userId",
				foreignField: "_id",
				as: "userDetails"
			};

			aggPipe.push({ "$match": match });
			aggPipe.push({ "$lookup": userLookup });
			
			const project = {
				createdAt: 1, description: 1, reportType: 1, userId: 1, "userDetails.firstName": 1, "userDetails.lastName": 1, "userDetails.profilePicture": 1
			};

			if (params.sortBy == 1) {
				params.sortBy = 1;
			} else {
				params.sortBy = -1;
			}

			sort = { "createdAt": params.sortBy }

			aggPipe.push({ "$sort": sort });
			aggPipe.push({ "$project": project });

			const options = { collation: true };
			return this.paginate("reports", aggPipe, params.limit, params.pageNo, options, true);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function investorListing
	 */
	async investorListing(params: AdminRequest.InvestorList, tokenData: TokenData) {
		try {
			let match: any = {};
			let aggPipe: any = [];
			let sort: any = {};

			match["_id"] = { $ne: tokenData.userId }
			match["status"] = { $ne: STATUS.DELETED }

			if(params.searchKey) {
				match = {
					...match,
					$or: [
						{
							$expr: {
								$regexMatch: {
									input: { $concat: ['$firstName', ' ', '$lastName'] },
									regex: params.searchKey,
									options: 'i'
								}
							}
						},
						{ "email": { "$regex": params.searchKey, "$options": "i" } },
						{ "userType": { "$regex": params.searchKey, "$options": "i" } },
						{ "status": { "$regex": params.searchKey, "$options": "i" } }
					]
				}
			}

			const project = {
				_id: 1,
				userType: 1,
				email: 1,
				firstName: 1,
				lastName: 1,
				status: 1,
				lastSeen: 1,
				createdAt: 1
			};

			aggPipe.push({ "$match": match });
			aggPipe.push({ "$project": project });

			if (params.sortBy == 1) {
				params.sortBy = 1;
			} else {
				params.sortBy = -1;
			}

			if (params.sortCriteria == INVESTOR_SORT_CRITERIA.NAME) {
				sort = { "firstName": params.sortBy }
			} else if (params.sortCriteria == INVESTOR_SORT_CRITERIA.EMAIL) {
				sort = { "email": params.sortBy }
			} else if (params.sortCriteria == INVESTOR_SORT_CRITERIA.USER_TYPE) {
				sort = { "userType": params.sortBy }
			} else if (params.sortCriteria == INVESTOR_SORT_CRITERIA.STATUS) {
				sort = { "status": params.sortBy }
			} else {
				sort = { "createdAt": params.sortBy }
			}

			aggPipe.push({ "$sort": sort });

			const options = { collation: true };
			return this.paginate("admins", aggPipe, params.limit, params.pageNo, options, true);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function createInvestor
	 */
	async createInvestor(params: AdminRequest.CreateInvestor, randomString: string) {
		try {
			params["status"] = STATUS.PENDING;
			params["userType"] = USER_TYPE.INVESTOR;
			params["password"] = randomString;
			return this.insert("admins", params, {});
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function editInvestor
	 */
	async editInvestor(params: AdminRequest.EditInvestor) {
		try {
			const query: any = {};
			query._id = params.id;

			const update = {};
			update["$set"] = params;
			const options = { new: true };

			return await this.findOneAndUpdate("admins", query, update, options);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function editInvestorStatus
	 */
	async editInvestorStatus(params: AdminRequest.DeleteInvestor) {
		try {
			const query: any = {};
			const update: any = {};

			query["_id"] = Types.ObjectId(params.id);
			update["$set"] = { status: params.status };

			return await this.update("admins", query, update, {});
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function topSupporters
	 */
	async topSupporters() {
		try {
			return this.paginate("users", [
				{
					$match: {
						status: {
							$ne: STATUS.DELETED
						}
					}
				},
				{
					$lookup: {
						from: DB_MODEL_REF.PERFORM_BLESSING,
						localField: "_id",
						foreignField: "userId",
						as: "blessings",
						pipeline: [
							{
								$project: {
									isGratitude: 1,
									listenDuration: 1,
									status: 1
								}
							},
							{
								$match: {
									isGratitude: false,
									status: {
										$ne: STATUS.DELETED
									}
								}
							}
						]
					}
				},
				{
					$project: {
						firstName: 1,
						lastName: 1,
						streakCount: 1,
						createdAt: 1,
						// blessingCount: { $size: "$blessings" },
						blessingCount: 1,
						blessingTime: { $sum: "$blessings.listenDuration" }
					}
				},
				{
					$sort: {
						blessingTime: -1,
						streakCount: -1,
						createdAt: -1
					}
				}
			], 10, 1, {}, true);
		} catch (error) {
			throw error;
		}
	}
}

export const adminDao = new AdminDao();