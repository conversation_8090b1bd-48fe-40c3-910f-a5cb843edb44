"use strict";
import { BaseDao } from "@modules/baseDao/BaseDao";
import { STATUS, FLAG_TYPE, FLAGGED_GRATITUDE_SORT_CRITERIA, DB_MODEL_REF, REPORT_TYPE } from "@config/constant";
import { escapeSpec<PERSON><PERSON>haracter } from "@utils/appUtils";
import { toObjectId } from "@utils/appUtils";
export class GratitudesDao extends BaseDao {

       /**    
	 * @function findGratitudeById
	 */
	async findGratitudeById(gratitudeId: string, project = {}) {
		try {
			const query: any = {};
			query._id = toObjectId(gratitudeId);
			query.status ={ '$nin': [STATUS.DELETED,STATUS.IN_ACTIVE,STATUS.UN_PUBLISHED]};
			const projection = (Object.values(project).length) ? project : { createdAt: 0, updatedAt: 0 };
			return await this.findOne("gratitudes", query, projection);
		} catch (error) {
			throw error;
		}
	}

    /**    
	 * @function findFlaggedGratitudeById
	 */
    async findFlaggedGratitudeById(gratitudeId: string, project = {}) {
		try {
			const query: any = {};
			query._id = toObjectId(gratitudeId);
			const projection = (Object.values(project).length) ? project : { createdAt: 0, updatedAt: 0 };
			return await this.findOne("gratitudes", query, projection);
		} catch (error) {
			throw error;
		}
	}
    
     /**
      * @function saveGratitudes
      */
     async saveGratitudes(params: GratitudesRequest.Add) {
        try {
            return await this.save("gratitudes", params);
        } catch (error) {
            throw error;
        }
    }
    /** 
	 * @function flaggedGratitudes
	 */
    async flaggedGratitudes(params: GratitudesRequest.FlaggedGratitude) {
        try {
            let match: any = {};
			let aggPipe: any = [];
			let sort: any = {};

            match["flagStatus"] = { $in: [FLAG_TYPE.FLAGGED, FLAG_TYPE.UN_FLAGGED, FLAG_TYPE.DELETED] };

            const userLookup = {
				from: DB_MODEL_REF.USER,
				localField: "userId",
				foreignField: "_id",
				as: "userDetails"
			};

            if(params.searchKey) {
                params.searchKey = params.searchKey.replace(/G-|W-/g, "");
                params.searchKey = escapeSpecialCharacter(params.searchKey);
				match = {
					...match,
                    $expr: {
                        $or: [
                            {
                                $regexMatch: {
                                    input: { $concat: ['$userDetails.firstName', ' ', '$userDetails.lastName'] },
                                    regex: params.searchKey,
                                    options: 'i'
                                }
                            },
                            {
                                $regexMatch: {
                                    input: { $toString: "$gratitudeNumber" },
                                    regex: params.searchKey,
                                    options: 'i'
                                }
                            },
                            {
                                $regexMatch: {
                                    input: { $toString: "$wishDetail.wishNumber" },
                                    regex: params.searchKey,
                                    options: 'i'
                                }
                            },
                            {
                                $regexMatch: {
                                    input: "$flagStatus",
                                    regex: params.searchKey,
                                    options: 'i'
                                }
                            },
                        ]
                    }
				}
			}

            aggPipe.push({ "$lookup": userLookup });
            aggPipe.push({ "$unwind": "$userDetails" });
            aggPipe.push({ "$match": match });

            const project = {
                _id: 1,
                gratitudeNumber: 1,
                wishDetail: 1,
                audio: 1,
                notes: 1,
                type: 1,
                flagStatus: 1,
                reportCount: 1,
                createdAt: 1,
                "userDetails._id": 1,
                "userDetails.firstName": 1,
                "userDetails.lastName": 1
            }

            if (params.sortBy == 1) {
				params.sortBy = 1;
			} else {
				params.sortBy = -1;
			}

            if(params.sortCriteria == FLAGGED_GRATITUDE_SORT_CRITERIA.GRATITUDE_BY) {
                sort = { "userDetails.firstName": params.sortBy }
            } else if (params.sortCriteria == FLAGGED_GRATITUDE_SORT_CRITERIA.WISH_NUMBER) {
                sort = { "wishDetail.wishNumber": params.sortBy }
            } else if (params.sortCriteria == FLAGGED_GRATITUDE_SORT_CRITERIA.GRATITUDE_NUMBER) {
                sort = { "gratitudeNumber": params.sortBy }
            } else if (params.sortCriteria == FLAGGED_GRATITUDE_SORT_CRITERIA.STATUS) {
                sort = { "flagStatus": params.sortBy }
            } else {
                sort = { "createdAt": params.sortBy }
            }

            aggPipe.push({ "$sort": sort });
			aggPipe.push({ "$project": project });

            const options = { collation: true };
            return this.paginate("gratitudes", aggPipe, params.limit, params.pageNo, options, true);
        } catch (error) {
            throw error;
        }
    }

    /** 
	 * @function flaggedGratitudeDetail
	 */
    async flaggedGratitudeDetail(params: GratitudesRequest.GratitudeId) {
        try {
            let match: any = {};
			let aggPipe: any = [];

            match["_id"] = toObjectId(params.id);
            aggPipe.push({ "$match": match });
            
            const userLookup = {
                from: DB_MODEL_REF.USER,
				localField: "userId",
				foreignField: "_id",
				as: "userDetails"
            };

            const wishLookup = {
                from: DB_MODEL_REF.WISHES,
				localField: "wishId",
				foreignField: "_id",
				as: "wishDetails"
            }
            
            const project = {
                _id: 1,
                gratitudeNumber: 1,
                userId: 1,
                "userDetails._id": 1,
                "userDetails.firstName": 1,
                "userDetails.lastName": 1,
                createdAt: 1,
                type: 1,
                notes: 1,
                audio: 1,
                flagStatus: 1,
                reportCount: 1,
                unflagHistory: 1,
                deleteReason: 1,
                deletedAt: 1,
                "wishDetails._id": 1,
                "wishDetails.userId": 1,
                "wishDetails.userDetail": 1,
                "wishDetails.wishNumber": 1,
                "wishDetails.createdAt": 1,
                "wishDetails.intension": 1,
                "wishDetails.description": 1,
                "wishDetails.image": 1
            }

            aggPipe.push({ "$lookup": userLookup });
            aggPipe.push({ "$lookup": wishLookup });
            aggPipe.push({ "$unwind": "$userDetails" });
            aggPipe.push({ "$unwind": "$wishDetails" });
            aggPipe.push({ "$project": project });
            const result = await this.aggregate("gratitudes", aggPipe);
            return result[0];
        } catch (error) {
            throw error;
        }
    }
    /** 
	 * @function flaggedGratitudeReports
	 */
    async flaggedGratitudeReports(params: GratitudesRequest.FlaggedGratitude) {
        try {
            let match: any = {};
			let aggPipe: any = [];
			let sort: any = {};

            match["subjectId"] = toObjectId(params.gratitudeId);
			match["type"] = REPORT_TYPE.GRATITUDE;

            const userLookup = {
				from: DB_MODEL_REF.USER,
				localField: "userId",
				foreignField: "_id",
				as: "userDetails"
			};

			aggPipe.push({ "$match": match });
			aggPipe.push({ "$lookup": userLookup });

            const project = {
				createdAt: 1, description: 1, reportType: 1, userId: 1, "userDetails.firstName": 1, "userDetails.lastName": 1, "userDetails.profilePicture": 1
			};

            if (params.sortBy == 1) {
				params.sortBy = 1;
			} else {
				params.sortBy = -1;
			}

			sort = { "createdAt": params.sortBy }

			aggPipe.push({ "$sort": sort });
			aggPipe.push({ "$project": project });

            const options = { collation: true };
            return this.paginate("reports", aggPipe, params.limit, params.pageNo, options, true);
        } catch (error) {
            throw error;
        }
    }
    /** 
	 * @function unflagDeleteGratitude
	 */
    async unflagDeleteGratitude(params: GratitudesRequest.UnflagDeleteGratitude, tokenData: TokenData) {
        try {
            const query: any = {};
			const update: any = {};

            query["_id"] = toObjectId(params.id);

            if(params.status == FLAG_TYPE.UN_FLAGGED) {
                update["$set"] = {
                    isFlagged: false,
                    flagStatus: FLAG_TYPE.UN_FLAGGED
                }
                update["$push"] = {
                    unflagHistory: {
                        adminId: tokenData.userId
                    }
                }
            } else {
                update["$set"] = {
                    status: STATUS.DELETED,
                    flagStatus: STATUS.DELETED,
                    deleteReason: params.deleteReason,
                    deletedAt: Date.now()
                }
            }

            return this.updateOne("gratitudes", query, update, {});
        } catch (error) {
            throw error;
        }
    }

     /**
		 * @function gratitudeLocation
		 */
	async gratitudeLocation(params,userId,type, dateFilter) {
		try {
			
			const aggPipe = [];
			const match: any = {};
			// match['status'] = STATUS.ACTIVE;
			match['location'] = { $exists: true };
            // if(type==1)match['userId']  = toObjectId(userId);
			aggPipe.push({ "$match": match });
			aggPipe.push({ "$sort": { createdAt: -1}});
            if(type!=0 && dateFilter){
                if (params.startDate) match.createdAt = { "$gte": params.startDate };
            }
            // aggPipe.push({
			// 	"$lookup": {
			// 		"from": "perform_blessings",
			// 		"let": { "blessingId": "$blessingId" },
			// 		"pipeline": [
			// 			{
			// 				"$match": {

			// 					"$expr":
			// 					{
			// 						$and: [
			// 							{
			// 								"$eq": ["$_id", "$$blessingId"]
			// 							}
			// 						]
			// 					}

			// 			 },
			// 			},
			// 			{ "$project": {_id:0, location:1} }
			// 		],
			// 		"as": "blessLocation"
			// 	}
			// });
			// aggPipe.push({
			// 	'$unwind': {
			// 		"path": "$blessLocation",
			// 		"preserveNullAndEmptyArrays": true
			// 	}
			// });
			aggPipe.push({"$project":{id:1, location:1}});
			const options = {};
			let data = await this.aggregate("gratitudes", aggPipe,options);
			return data;
		} catch (error) {
			throw error;
		}
	}
}

export const gratitudesDao = new GratitudesDao();