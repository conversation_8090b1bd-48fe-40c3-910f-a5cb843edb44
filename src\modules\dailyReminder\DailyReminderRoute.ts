"use strict";
import { Request, ResponseToolkit } from "@hapi/hapi";
import { responseHand<PERSON> } from "@utils/ResponseHandler";

import {
	SWAGGER_DEFAULT_RESPONSE_MESSAGES,
	SERVER,
} from "@config/index";
import { dailyReminderController } from "./DailyReminderController";

export const dailyReminderRoute = [
    {
        method: "POST",
        path:  `${SERVER.API_BASE_URL}/v1/daily-reminder`,
        handler: async (request: Request | any, h: ResponseToolkit) => {
            try {
                const data = await dailyReminderController.dailyReminder();
                console.log("data = ", data);
                const results = {
                    "statusCode": 200,
			        data
                };
                return responseHandler.sendSuccess(h, results);
            } catch (error) {
                return responseHandler.sendError(request, error);
            }
        },
        config: {
            plugins: {
                "hapi-swagger": {
                    responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
                }
            }
        }
    }
];