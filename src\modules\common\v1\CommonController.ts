"use strict";
import * as _ from "lodash";
import { Request } from "@hapi/hapi";
import { baseDao } from "@modules/baseDao/index";
import { userDao } from "@modules/user/v1/UserDao"
import { wishesDao } from "@modules/wishes/v1/WishesDao"
import { commonDao } from "@modules/common/v1/CommonDao"
import { blessingDao } from "@modules/blessings/v1/BlessingDao"
import {
	MESSAGES,
	SERVER,
	DEEPLINK_TYPE,
	LOCATION_TYPE,
	STATUS,
	CONTENT_TYPE,
	WISH_TYPE,
	SMS_CONTENT,
	WISH_SHARE
} from "@config/index";
import {
	consolelog,
	toObjectId
} from "@utils/appUtils";
import { TemplateUtil } from "@utils/TemplateUtil";
import { userDaoV1 } from "@modules/user/index"
import { paymentIntetntDaoV1 } from "@modules/common/index"
import { loginHistoryDao } from "@modules/loginHistory/index"
import { validate } from "@lib/tokenManager";
const stripe = require('stripe')(SERVER.STRIPE_SECRETE);
import { awsSQS } from "@lib/AwsSqs"
import { redisClient } from "@lib/index";
import { wishesDaoV1 } from "@modules/wishes";
const AWS = require('aws-sdk');
const sqs = new AWS.SQS();
const buffer = require('buffer');
const url = require('url');

export class CommonController {
	/**
	 * @function deepLink
	 */
	async deepLink(request: Request, params: DeeplinkRequest) {
		try {
			console.log("deepLink===================>", JSON.stringify(params), SERVER.DEEPLINK.IOS_SCHEME, SERVER.DEEPLINK.IOS_PACKAGE_NAME);
			switch (params.type) {
				case DEEPLINK_TYPE.VERIFY_USER_EMAIL: {
					const userData = await baseDao.findOne("users", { email: params.email });
					if (!userData) return Promise.reject(MESSAGES.ERROR.BAD_TOKEN);
					const payload = await validate(params.token, request);
					const userLoginHistory = await loginHistoryDao.findDeviceById({ "userId": payload.sub, "deviceId": payload.deviceId, "salt": payload.prm });
					if(userLoginHistory.salt !== payload.prm) return Promise.reject(MESSAGES.ERROR.BAD_TOKEN);
					const checkExp = await redisClient.getValue(params.email+"_"+DEEPLINK_TYPE.VERIFY_USER_EMAIL);
					if(!checkExp) return Promise.reject(MESSAGES.ERROR.BAD_TOKEN);
					await baseDao.updateOne("users", { _id: userData._id }, { isEmailVerified: true }, {});
					await redisClient.deleteKey(params.email+"_"+DEEPLINK_TYPE.VERIFY_USER_EMAIL);
					const responseHtml = await (new TemplateUtil(SERVER.TEMPLATE_PATH + "deeplink.html"))
						.compileFile({
							url: "", // android scheme,
							iosLink: SERVER.DEEPLINK.IOS_SCHEME+'?type='+DEEPLINK_TYPE.VERIFY_USER_EMAIL+'&token='+params.token, // ios scheme
							fallback: SERVER.FALLBACK_URL,
							title: SERVER.APP_NAME,
							android_package_name: "",
							ios_store_link:SERVER.FALLBACK_URL
						});
					return responseHtml;
				}
				case DEEPLINK_TYPE.FORGOT_PASSWORD: {
					const userData = await baseDao.findOne("users", { passwordResetToken: params.token });
					if (!userData) return Promise.reject(MESSAGES.ERROR.INVALID_RESET_PASSWORD_TOKEN);
					const checkExp = await redisClient.getValue(userData.email+"_"+DEEPLINK_TYPE.FORGOT_PASSWORD);
					if(!checkExp) return Promise.reject(MESSAGES.ERROR.BAD_TOKEN);
					await redisClient.deleteKey(userData.email+"_"+DEEPLINK_TYPE.FORGOT_PASSWORD);
					const responseHtml = await (new TemplateUtil(SERVER.TEMPLATE_PATH + "deeplink.html"))
						.compileFile({
							url: "", // android scheme,
							iosLink: SERVER.DEEPLINK.IOS_SCHEME+'?type='+DEEPLINK_TYPE.FORGOT_PASSWORD+'&token='+params.token, // ios scheme
							fallback: SERVER.FALLBACK_URL,
							title: SERVER.APP_NAME,
							android_package_name: "",
							ios_store_link: SERVER.FALLBACK_URL
						});
					return responseHtml;
				}
				case DEEPLINK_TYPE.MAGIC_LINK: {
					console.log('INSIDE DEEPLINK ==================================>>>>>>>>>>>>>>>>>>>>>>>>>>>');
					const userData = await baseDao.findOne("users", { email: params.email });
					if (!userData) return Promise.reject(MESSAGES.ERROR.BAD_TOKEN);
					const responseHtml = await (new TemplateUtil(SERVER.TEMPLATE_PATH + "deeplink.html"))
						.compileFile({
							url: "", // android scheme,
							iosLink: SERVER.DEEPLINK.IOS_SCHEME+'?type='+DEEPLINK_TYPE.MAGIC_LINK, // ios scheme
							fallback: SERVER.FALLBACK_URL,
							title: SERVER.APP_NAME,
							android_package_name: "",
							ios_store_link: SERVER.FALLBACK_URL
						});
					return responseHtml;
				}
				case DEEPLINK_TYPE.SMS_INVITE: {
					console.log('INSIDE DEEPLINK ==================================>>>>>>>>>>>>>>>>>>>>>>>>>>>');
					//const userData = await baseDao.findOne("users", { email: params.email });
					//if (!userData) return Promise.reject(MESSAGES.ERROR.BAD_TOKEN);
					const responseHtml = await (new TemplateUtil(SERVER.TEMPLATE_PATH + "deeplink.html"))
						.compileFile({
							url: "", // android scheme,
							iosLink: SERVER.DEEPLINK.IOS_SCHEME+'?type='+DEEPLINK_TYPE.SMS_INVITE, // ios scheme
							fallback: SERVER.FALLBACK_URL,
							title: SERVER.APP_NAME,
							android_package_name: "",
							ios_store_link: SERVER.FALLBACK_URL
						});
					return responseHtml;
				}
				case DEEPLINK_TYPE.COM_MAP: {
					console.log('INSIDE DEEPLINK ==================================>>>>>>>>>>>>>>>>>>>>>>>>>>>');
					const responseHtml = await (new TemplateUtil(SERVER.TEMPLATE_PATH + "deeplink.html"))
						.compileFile({
							url: "", // android scheme,
							iosLink: SERVER.DEEPLINK.IOS_SCHEME+'?type='+DEEPLINK_TYPE.COM_MAP, // ios scheme
							fallback: SERVER.FALLBACK_URL,
							title: SERVER.APP_NAME,
							android_package_name: "",
							ios_store_link: SERVER.FALLBACK_URL
						});
					return responseHtml;
				}
				case DEEPLINK_TYPE.WISH_SHARE: {
					console.log('INSIDE DEEPLINK ==================================>>>>>>>>>>>>>>>>>>>>>>>>>>>');
					const wishId = Buffer.from(params.wid, 'base64').toString('utf-8');
					// const payload = await validate(params.token, request);
					// let wishDetail = wishesDaoV1.wishDetail({"wishId":wishId},{"userId": payload.sub},{})
					let wishDetail = await baseDao.findOne("wishes",{"_id":wishId,},{"isGlobalWish":1, "image":1, "hostId":1, "userId":1, "userDetail":1})
					let msg = "";
					let userid = Buffer.from(params.uid, 'base64').toString('utf-8');
						if(wishDetail.hostId && wishDetail.hostId !== ""){
							if(wishDetail.hostId.toString() == userid){
								msg = WISH_SHARE.WISH_SHARE_CONTENT1;
							}
							else{
								let hostname = await baseDao.findOne("users",{"_id":wishDetail.hostId,},{"name":1, "_id":0})
								let name = hostname.name ? hostname.name  :  "" ;
								// msg = SMS_CONTENT.TAG_CONTACT_ON_WISH_FOR_OTHERS1 + name + SMS_CONTENT.TAG_CONTACT_ON_WISH_FOR_OTHERS3;
								msg = WISH_SHARE.WISH_SHARE_CONTENT2+name+WISH_SHARE.WISH_SHARE_CONTENT3
							}
						}
						else{
							if(wishDetail.userId.toString() == userid){
								msg = WISH_SHARE.WISH_SHARE_CONTENT4;
							}
							else{
								let name = wishDetail.userDetail && wishDetail.userDetail.firstName ? wishDetail.userDetail.firstName : "" ;
								// msg = SMS_CONTENT.TAG_CONTACT_ON_WISH_FOR_OTHERS1 + name + SMS_CONTENT.TAG_CONTACT_ON_WISH_FOR_OTHERS3;
								msg = WISH_SHARE.WISH_SHARE_CONTENT5+name+WISH_SHARE.WISH_SHARE_CONTENT6;
							}
						}
					let image = wishDetail.isGlobalWish == true ? wishDetail.image : await this.getReplaceURL(wishDetail.image);
					const responseHtml = await (new TemplateUtil(SERVER.TEMPLATE_PATH + "wishShareDeeplink.html"))
						.compileFile({
							url: "", // android scheme,
							iosLink: SERVER.DEEPLINK.IOS_SCHEME+'?type='+DEEPLINK_TYPE.WISH_SHARE, // ios scheme
							fallback: SERVER.FALLBACK_URL,
							title: SERVER.APP_NAME,
							android_package_name: "",
							ios_store_link: SERVER.FALLBACK_URL,
							image:image,
							description: msg
						});
					return responseHtml;
				}
			}
		} catch (error) {
			throw error;
		}
	}
	/**
	   * @function userSearch
	   * @description get the list of user in our application
	   * @param params 
	   * @returns returns the list of the users
	   */
	async userSearch(params: ListingRequest, tokenData: TokenData) {
		try {
			let step1 = await userDao.userSearch(params, tokenData);
			return MESSAGES.SUCCESS.LIST(step1);
		} catch (error) {
			console.log(error, "error detected");
			throw error;
		}
	}
	/**
	  * @function userContacts
	  * @description get the contact list of user 
	  * @param params 
	  * @returns returns the contact list of the user
	  */
	async userContacts(params: ListingRequest, tokenData: TokenData) {
		try {
			let users = [];
			if(params.wishId){
				let data = await baseDao.findOne("wishes", {_id:toObjectId(params.wishId)}, {});
				users.push(toObjectId(data.userId));
				if(data.hostId) users.push(toObjectId(data.hostId));
			}
			users.push(toObjectId(tokenData.userId));			
			let step1;
			step1 = await userDao.userContacts(params, tokenData, users);
			return MESSAGES.SUCCESS.LIST(step1);
		} catch (error) {
			console.log(error, "error detected");
			throw error;
		}
	}

	/**
	  * @function userContactsV2
	  * @description get the contact list of user 
	  * @param params 
	  * @returns returns the contact list of the user
	  */
	async userContactsV2(params: ListingRequest, tokenData: TokenData) {
		try {
			let users = [];
			if(params.wishId){
				let data = await baseDao.findOne("wishes", {_id:toObjectId(params.wishId)}, {});
				users.push(toObjectId(data.userId));
				if(data.hostId) users.push(toObjectId(data.hostId));
			}
			users.push(toObjectId(tokenData.userId));			
			let step1;
			step1 = await userDao.userContactsV2(params, tokenData, users);
			console.log("STEP 1 = ", step1);
			step1.data = this._formatUserContactsV2Response(step1.data);
			step1.total = step1.data.length;
			return MESSAGES.SUCCESS.LIST(step1);
		} catch (error) {
			console.log(error, "error detected");
			throw error;
		}
	}

	_formatUserContactsV2Response(input: any) {
		const output = [];

		input.forEach(contact => {
			contact.phoneNumbers.forEach(phone => {
			const newContact = {
				"_id": contact._id,
				isAppUser: contact.isAppUser,
				name: contact.name,
				phoneNumber: phone.phoneNumber,
				originalPhoneNumber: phone.originalPhoneNumber
			};
			if (contact.isAppUser) {
				newContact['contactUserId'] = phone.userDetail._id;
				newContact['profilePicture'] = phone.profilePicture;
				newContact['userDetail'] = phone.userDetail;
			}
			output.push(newContact);
			});
		});

		return output;
	}

	/**
	   * @function globalWishList
	   * @description here are getting the wishes.
	   *
	   */
	async globalWishList(params: ListingRequest, tokenData: TokenData) {
		try {
			let step1 = await userDaoV1.findUserById(tokenData.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			let step2 = await wishesDao.getGlobalWishes(params, tokenData);
			await step2.data.map(obj=>{
				let encodedWishId  = Buffer.from((obj.wishDetail._id).toString()).toString('base64');
				// obj["shareLink"] = `${SERVER.APP_URL}${SERVER.API_BASE_URL}/v1/common/deeplink?type=${DEEPLINK_TYPE.WISH_SHARE}&id=${encodedId}`
				// obj["wishDetail"]["shareLink"] =  `${SERVER.APP_URL}${SERVER.API_BASE_URL}/v1/common/deeplink?type=${DEEPLINK_TYPE.WISH_SHARE}&id=${encodedId}`
				let encodedUserId = Buffer.from((tokenData.userId).toString()).toString('base64');
				obj["wishDetail"]["shareLink"] =  `${SERVER.APP_URL}${SERVER.API_BASE_URL}/v1/common/deeplink?type=${DEEPLINK_TYPE.WISH_SHARE}&wid=${encodedWishId}&uid=${encodedUserId}`
			})
			return MESSAGES.SUCCESS.WISH_LIST(step2);
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function dropdown
	 * @description here are getting the wishes.
	 *
	 */
	async dropdown(params: Dropdown) {
		try {
			let data
			if (params.type == LOCATION_TYPE.COUNTRIES) {
				data = await commonDao.countryList(params)
			} else if (params.type == LOCATION_TYPE.CITIES) {
				if (!params.countryId) return Promise.reject(MESSAGES.ERROR.COUNTRY_ID_NOT_FOUND);
				if (!params.stateId) return Promise.reject(MESSAGES.ERROR.STATE_ID_NOT_FOUND);
				data = await commonDao.cityList(params)
			} else {
				if (!params.countryId) return Promise.reject(MESSAGES.ERROR.COUNTRY_ID_NOT_FOUND);
				data = await commonDao.stateList(params)
			}
			return MESSAGES.SUCCESS.COMMON_DROPDOWN(data);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function userBlessingLib
	 * @description get the list of blessing   
	 *
	 */
	async userBlessingLib(params: ListingRequest, tokenData) {
		try {
			const step1 = await userDaoV1.findUserById(tokenData.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			let data;
			data = await blessingDao.userBlessingLib(params)
			return MESSAGES.SUCCESS.LIST(data);
		} catch (error) {
			throw error;
		}
	}

	async deleteAccountScript(step1, type) {
		let updateEmail = step1.email + "_deleted-" + new Date().getTime();

		// UPDATE USER EMAIL, STATUS ETC.
		await baseDao.update("users",
			{ _id: step1._id },
			{
				status: STATUS.DELETED,
				oldEmail: step1.email,
				email: updateEmail,
				isDeletedBy: type,
				deleteReason:step1.deleteReason?step1.deleteReason:"",
				deletedAt: Date.now()
			},
			{}
		);

		await baseDao.deleteMany("favourites", { userId: step1._id });

		const myWishes = await baseDao.distinct("wishes", "_id", { $or: [{ userId: step1._id }, { hostId: step1._id }], status: { $ne: STATUS.DELETED } });

		// DELETE ALL WISHES RELATED TO USER & HOST
		await baseDao.updateMany("wishes",
			{ $or: [{ userId: step1._id }, { hostId: step1._id }], status: { "$ne": STATUS.DELETED } },
			{
				// "$pop":{userRemovedWish:(step1._id)},
				"$set": { status: STATUS.DELETED }
			},
			{}
		);

		// DELETE ALL IN WHICH USER IS TAGGED
		await baseDao.updateMany("tagwishes", 
			{ userId: step1._id, status: { "$ne": STATUS.DELETED } }, 
			{ status: STATUS.DELETED }, 
			{}
		);

		//-----------FIND BLESSINGS AND reduce totalBlessings by 1 for wish realted to that perform blessing------------------------
		const myBlessings = await baseDao.find("perform_blessings", { userId: step1._id, status: { "$ne": STATUS.DELETED } }, { wishId: 1, userId: 1 });
		if (myBlessings.length) {
			await Promise.all(myBlessings.map(async item => {
				if (item.wishId) {
					const checkBlessingCount = await baseDao.findOne("wishes", { _id: item.wishId }, { totalBlessings: 1 });
					if (checkBlessingCount && checkBlessingCount.totalBlessings > 0) {
						await baseDao.updateOne("wishes", { _id: item.wishId }, { $inc: { totalBlessings: -1 } }, {});
					}
				}
			}));
		}
		//--------------------------------------------------------------------------------------------------------------------------

		if(myWishes.length) {
			await baseDao.updateMany("perform_blessings", { wishId: { $in: myWishes } }, { status: STATUS.DELETED }, {});
			await baseDao.updateMany("gratitudes", { wishId: { $in: myWishes } }, { status: STATUS.DELETED }, {});
			await baseDao.updateMany("tagwishes", { wishId: { $in: myWishes } }, { status: STATUS.DELETED }, {});
			await baseDao.deleteMany("favourites", { wishId: { $in: myWishes } });
		}

		// DELETE ALL PERFORM BLESSING RELATED TO USER
		await baseDao.updateMany("perform_blessings", 
			{ userId: step1._id, status: { "$ne": STATUS.DELETED } }, 
			{ status: STATUS.DELETED }, 
			{}
		);

		// DELETE ALL GRATITUDES RELATED TO USER
		await baseDao.updateMany("gratitudes", 
			{ userId: step1._id, status: { "$ne": STATUS.DELETED } }, 
			{ status: STATUS.DELETED }, 
			{}
		);

		// DELETE ALL COMMUNITUES RELATED TO USER
		await baseDao.updateMany("communities", 
			{ userId: step1._id, status: { "$ne": STATUS.DELETED } }, 
			{ status: STATUS.DELETED }, 
			{}
		);

		// DELETE MEMBERS
		await baseDao.updateMany("members", 
			{ $or: [ { userId: step1._id }, { creatorId: step1._id } ], status: { "$ne": STATUS.DELETED } }, 
			{ status: STATUS.DELETED }, 
			{}
		);

		// // CONTACTS
		// await baseDao.updateMany("contacts", 
		// 	{ contactUserId: step1._id, status: { "$ne": STATUS.DELETED } }, 
		// 	{ "$set": { isAppUser: false } }, 
		// 	{}
		// );

		// // CONTACTS
		// await baseDao.updateMany("contacts",
		// 	{ contactUserId: step1._id, status: { "$ne": STATUS.DELETED } },
		// 	{ "$unset": { contactUserId: "" } },
		// 	{}
		// );

		// // CONTACTS
		// await baseDao.deleteMany("contacts", 
		// 	{ userId: step1._id, status: { "$ne": STATUS.DELETED } }
		// );

		// CONTACTS
		// Update the connectedToAccount field for phone numbers
		await baseDao.updateMany('contacts_v2',
			{ 'phoneNumbers.contactUserId': step1._id },
			{ "$set": { 'phoneNumbers.$[elem].connectedToAccount': null } },
			{ arrayFilters: [{ 'elem.contactUserId': step1._id }] }
		);
	
		// Fetch relevant contacts
		const contacts = await baseDao.find('contacts_v2',
			{ 'phoneNumbers.contactUserId': step1._id },
			{}
		);
	
		// Check within each document if any other phone numbers are still connected to an account
		for (const contact of contacts) {
			const hasConnectedPhoneNumber = contact.phoneNumbers.some(phoneNumber => phoneNumber.connectedToAccount);
	
			if (!hasConnectedPhoneNumber) {
				await baseDao.updateOne('contacts_v2',
					{ _id: contact._id },
					{ "$set": { isAppUser: false } },
					{}
				);

				// Check and set default phone number
				let hasDefaultPhoneNumber = contact.phoneNumbers.some(phoneNumber => phoneNumber.default);

				if (!hasDefaultPhoneNumber) {
					let updated = false;
					for (const phoneNumber of contact.phoneNumbers) {
						if (phoneNumber.type.toLowerCase() === 'mobile') {
							phoneNumber.default = true;
							updated = true;
							break;
						}
					}
	
					if (!updated && contact.phoneNumbers.length > 0) {
						contact.phoneNumbers[0].default = true;
					}
	
					await baseDao.update('contacts_v2',
						{ _id: contact._id },
						{ "$set": { phoneNumbers: contact.phoneNumbers } },
						{}
					);
				}
			}
		}
	
		// Unset contactUserId
		await baseDao.updateMany('contacts_v2',
			{ 'phoneNumbers.contactUserId': step1._id },
			{ "$set": { 'phoneNumbers.$[elem].contactUserId': null } },
			{ arrayFilters: [{ 'elem.contactUserId': step1._id }] }
		);
	
		// DELETE CONTACTS
		await baseDao.deleteMany('contacts_v2',
			{ userId: step1._id }
		);

		// MANAGE WELL SECTION
		await baseDao.updateMany("well_logs", { senderId: step1._id, status: { $ne: STATUS.DELETED } }, { status: STATUS.DELETED }, {});
		await baseDao.updateMany("well_logs", {}, { $pull: { receiverId: step1._id } }, {});
		await baseDao.deleteMany("well_logs", { receiverId: { $size: 0 } });

		// DELETE PINNED WISHES BY USER
		// await baseDao.updateMany("pinned_wishes", { userId: step1._id, status: { $ne: STATUS.DELETED } }, { status: STATUS.DELETED }, {});
		await baseDao.deleteMany("pinned_wishes", { userId: step1._id });

		// DELETE WISHES OF USER USED IN PINNED WISHES
		// await baseDao.updateMany("pinned_wishes", { wishId: { $in: myWishes } }, { status: STATUS.DELETED }, {});
		await baseDao.deleteMany("pinned_wishes", { subjectId: { $in: myWishes } });

		return MESSAGES.SUCCESS.DELETE_USER;

	}

	// async deleteWellScript(step1) {
	// 	const myWishes = await baseDao.distinct("wishes", "_id", { userId: step1._id, status: { $ne: STATUS.DELETED } });
	// 	const myPerformBlessings = await baseDao.distinct("perform_blessings", "_id", { userId: step1._id, status: { $ne: STATUS.DELETED } });
	// 	const myGratitudes = await baseDao.distinct("gratitudes", "_id", { userId: step1._id, status: { $ne: STATUS.DELETED } });
	// 	// DELETE MY WISHES
	// 	if(myWishes.length) {
	// 		await baseDao.updateMany("well_logs", { subjectId: { $in: myWishes } }, { status: STATUS.DELETED }, {});
	// 		const linkedPerformBlessings = await baseDao.distinct("perform_blessings", "_id", { wishId: { $in: myWishes } });
	// 		// DELETE PERFORM BLESSINGS LINKED TO MY WISHES
	// 		if(linkedPerformBlessings.length) {
	// 			await baseDao.updateMany("well_logs", { subjectId: { $in: linkedPerformBlessings } }, { status: STATUS.DELETED }, {});
	// 		}
	// 	}
	// 	// DELETE MY PERFORM BLESSINGS
	// 	if(myPerformBlessings.length) {
	// 		await baseDao.updateMany("well_logs", { subjectId: { $in: myPerformBlessings } }, { status: STATUS.DELETED }, {});
	// 		const linkedGratitudes = await baseDao.distinct("gratitudes", "_id", { blessingId: { $in: myPerformBlessings } });
	// 		if(linkedGratitudes.length) {
	// 			// DELETE GRATITUDES LINKED TO MY PERFORM BLESSINGS
	// 			await baseDao.updateMany("well_logs", { subjectId: { $in: linkedGratitudes } }, { status: STATUS.DELETED }, {});
	// 		}
	// 	}
	// 	// DELETED MY GRATITUDES
	// 	if(myGratitudes.length) {
	// 		await baseDao.updateMany("well_logs", { subjectId: { $in: myGratitudes } }, { status: STATUS.DELETED }, {});
	// 	}
	// }


	async userDeactivateScript(params) {
		try {
			const myWishes = await baseDao.find("wishes", { userId: params._id, status: { $nin: [STATUS.DELETED, STATUS.UN_PUBLISHED] } }, { _id: 1, status: 1 });

			if (myWishes.length) {
				const myWishIds = [];
				myWishes.map(data => myWishIds.push(data._id));

				await baseDao.updateMany("wishes", { _id: { $in: myWishIds } }, [{ $set: { oldStatus: "$status", status: STATUS.BLOCKED } }], {});
				await baseDao.updateMany("tagwishes", { wishId: { $in: myWishIds } }, [{ $set: { oldStatus: "$status", status: STATUS.BLOCKED } }], {});
			}
		} catch (error) {
			throw error;
		}
	}

	async userActivateScript(params) {
		try {
			const myWishes = await baseDao.find("wishes", { userId: params._id, status: STATUS.BLOCKED }, { _id: 1, status: 1 });

			if (myWishes.length) {
				const myWishIds = [];
				myWishes.map(data => myWishIds.push(data._id));

				await baseDao.updateMany("wishes", { _id: { $in: myWishIds } }, [{ $set: { status: "$oldStatus" } }], {});
				await baseDao.updateMany("tagwishes", { wishId: { $in: myWishIds } }, [{ $set: { status: "$oldStatus" } }], {});
			}
		} catch (error) {
			throw error;
		}
	}

	async thankWishwellTooltip() {
		try {
			const step1 = await baseDao.findOne("contents", { type: CONTENT_TYPE.THANK_WISHWELL_TOOLTIP }, { data: 1 });
			return MESSAGES.SUCCESS.THANK_WISHWELL_TOOLTIP(step1);
		} catch (error) {
			throw error;
		}
	}

	async editThankWishwellTooltip(params) {
		try {
			await baseDao.updateOne("contents", { type: CONTENT_TYPE.THANK_WISHWELL_TOOLTIP }, { data: params.data }, {});
			return MESSAGES.SUCCESS.THANK_WISHWELL_TOOLTIP_UPDATED;
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function topPinnedGlobal
	 * @description get the admin 3 pinned global wish 
	 * @param params 
	 * @returns returns the contact list of the global wish
	 */
	async topPinnedGlobal(tokenData: TokenData) {
		try {
			let step1 = await userDaoV1.findUserById(tokenData.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			let step2 = await wishesDao.topPinnedGlobal();
			return MESSAGES.SUCCESS.DETAILS(step2);
		} catch (error) {
			console.log(error, "error detected");
			throw error;
		}
	}
	// payment-intent function

	async createPaymentIntent(tokenData, params) {
		try {
			const isUserExist = await userDaoV1.findUserById(tokenData.userId);
			if (!isUserExist) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			let stripeCustomerId = isUserExist.stripeCustomerId;
			if (isUserExist && !isUserExist.stripeCustomerId) {
				// creating stripe customer
				await stripe.customers.create({
					name: isUserExist.name,
					email: isUserExist.email
				})
					.then(async customer => {
						console.log(customer.id)
						stripeCustomerId = customer.id;
						await userDaoV1.editUserProfile({ "stripeCustomerId": customer.id }, tokenData.userId);
					})
					.catch(error => console.error(error));
			}

			const paymentIntent = await stripe.paymentIntents.create({
				amount: params.amount * 100, //convert in cent
				currency: params.currency,
				automatic_payment_methods: { enabled: true },
				customer: stripeCustomerId,
				metadata: { userId: tokenData.userId },
				// confirm: true
			});
			const intentParams = {
				userId: isUserExist._id,
				stripeCutomerId: stripeCustomerId,
				paymentIntentid: paymentIntent.id,
				amount: paymentIntent.amount, //cents
				currency: paymentIntent.currency,
				amount_capturable: paymentIntent.amount_capturable,
				client_secret: paymentIntent.client_secret,
				capture_method: paymentIntent.capture_method,
				status: paymentIntent.status
			}
			let data = await commonDao.createPaymentIntent(intentParams);
			return MESSAGES.SUCCESS.DETAILS(data);
		} catch (error) {
			throw error;
		}
	}

	// payment-logs function
	async stripeWebhook(tokenData, req) {
		console.log("============>> ", req.payload, "WorkingProprtly");
		let event = req.payload
		this.webhookPaymentSignatureVerification(event);
		try {
			let transactionType = event.type;
			let transaction = event.data.object;
			if(event){
				let logsParams = {
					transactionId: transaction.payment_intent,
					amountCaptured: transaction.amount_captured.toString(),
					amountRefunded: transaction.amount_refunded.toString(),
					currency: transaction.currency,
					status: transaction.status,
					object: transaction.object,
					// calculatedStatement_descriptor: { S: transaction },
					customerId: transaction.customer,
					paymentMethod: transaction.payment_method_details.type,
					receiptUrl: transaction.receipt_url,
					transactionType: transactionType,
					webhookEvent: event
					// createdAt: new Date().toISOString(),
					// updatedAt: new Date().toISOString(),
				}
				const createdlogs = await paymentIntetntDaoV1.createPaymentLogs(logsParams);
				console.log("=========", JSON.stringify(createdlogs));

			}

			let paymentIntentData = await paymentIntetntDaoV1.getPaymentIntent({"paymentIntentid":transaction.payment_intent})
			// const intentParams = {
			// 	userId: tokenData.userId,
			// 	paymentIntentid: transaction.payment_intent,
			// 	amount: transaction.amount_refunded.toString(), //cents
			// 	currency: transaction.currency,
			// 	amount_capturable: transaction.amount_captured.toString(),
			// 	status: transaction.status,
			// 	stripeCutomerId: transaction.customer,
			// 	// client_secret: paymentIntent.client_secret,
			// 	// capture_method: paymentIntent.capture_method,
			// }

			let params = {};
			switch (transactionType) {
				case 'charge.captured':
					params = {
						"status":transaction.status
					}
					await paymentIntetntDaoV1.updatePaymentIntent({"_id":paymentIntentData.id}, params)
					break;

				case 'charge.expired':
					params = {
						"status":transaction.status
					}
					await paymentIntetntDaoV1.updatePaymentIntent({"_id":paymentIntentData.id}, params)

					break;

				case 'charge.failed':
					params = {
						"status":transaction.status
					}
					await paymentIntetntDaoV1.updatePaymentIntent({"_id":paymentIntentData.id}, params)
					break;

				case 'charge.pending':
					params = {
						"status":transaction.status
					}
					await paymentIntetntDaoV1.updatePaymentIntent({"_id":paymentIntentData.id}, params)
					break;

					case 'charge.refund.updated':
					params = {
						"status":transaction.status
					}
					await paymentIntetntDaoV1.updatePaymentIntent({"_id":paymentIntentData.id}, params)
					break;

				case 'charge.refunded':
					params = {
						"status":transaction.status
					}
					await paymentIntetntDaoV1.updatePaymentIntent({"_id":paymentIntentData.id}, params)
					break;

				case 'charge.succeeded':
					params = {
						"status":transaction.status
					}
					await paymentIntetntDaoV1.updatePaymentIntent({"_id":paymentIntentData.id}, params)
					break;

				case 'charge.updated':
					params = {
						"status":transaction.status
					}
					await paymentIntetntDaoV1.updatePaymentIntent({"_id":paymentIntentData.id}, params)
					break;
				
				case 'invoice.payment_failed':
					params = {
						"status":transaction.status
					}
					await paymentIntetntDaoV1.updatePaymentIntent({"_id":paymentIntentData.id}, params)
					break;

				case 'invoice.payment_succeeded':
					params = {
						"status":transaction.status
					}
					await paymentIntetntDaoV1.updatePaymentIntent({"_id":paymentIntentData.id}, params)
					break;

					case 'payment_intent.canceled':
					params = {
						"status":transaction.status
					}
					await paymentIntetntDaoV1.updatePaymentIntent({"_id":paymentIntentData.id}, params)
					break;

				case 'payment_intent.created':
					params = {
						"status":transaction.status
					}
					await paymentIntetntDaoV1.updatePaymentIntent({"_id":paymentIntentData.id}, params)
					break;

				case 'payment_intent.payment_failed':
					params = {
						"status":transaction.status
					}
					await paymentIntetntDaoV1.updatePaymentIntent({"_id":paymentIntentData.id}, params)
					break;

				case 'payment_intent.succeeded':
					params = {
						"status":transaction.status
					}
					await paymentIntetntDaoV1.updatePaymentIntent({"_id":paymentIntentData.id}, params)
					break;
  
			}
			

			//   const queryParams = {
			// 	TableName: transactionTable,
			// 	IndexName: "transactionId",
			// 	KeyConditionExpression: "transactionId = :indexValue", // Replace with your index key condition
			// 	ExpressionAttributeValues: { ":indexValue": transaction.payment_intent },
			//   };

			//   let transactionData = await ddoc.query(queryParams).promise();

			//   if (
			// 	transactionData &&
			// 	transactionData.Items &&
			// 	transactionData.Items.length()
			//   ) {
			// 	const updateParams = {
			// 	  TableName: transactionTable,
			// 	  Key: {
			// 		id: transactionData.Items[0].id,
			// 	  },
			// 	  UpdateExpression: "SET #status = :newValue",
			// 	  ExpressionAttributeNames: {
			// 		"#status": "status",
			// 	  },
			// 	  ExpressionAttributeValues: {
			// 		":newValue": transaction.status,
			// 	  },
			// 	  ReturnValues: "UPDATED_NEW", // If you want to get the updated values
			// 	};

			// 	let updatedData = await ddoc.update(updateParams).promise();

			// 	console.log("updatedData---==>---", updatedData);
			//   }
		} catch (err) {
			console.log("==============>", err);
			return { status: 400, err };
		}
	}

	async saveAdminWellLogs(senderId, receiverIds, subjectDetail, deleteReason, deleteType) {
		try {
			const wellLogData: any = {
				senderId: senderId,
				receiverId: receiverIds,
				subjectId: subjectDetail._id,
				subjectDetail: {
					notes: subjectDetail.description ? subjectDetail.description : "",
					type: subjectDetail.type ? subjectDetail.type : "",
					audio: subjectDetail.audio ? subjectDetail.audio : "",
					audioLength: subjectDetail.description ? subjectDetail.description : "",
					deleteReason: deleteReason
				},
				type: deleteType
			};
			await baseDao.save("well_logs", wellLogData);
		} catch (error) {
			throw error;
		}
	}

	//save location for all wish,bless,gratitude
	async saveLocation(params,type,subjectId,userId)
	{
		try {
		let saveObj = {};
		let cityExist = await baseDao.findOne("locations", { "location.city": params.location.city });
		if(cityExist){
			//update cityCount in location
			saveObj['cityCount'] = cityExist.cityCount + 1;
			await baseDao.updateOne("locations", {"location.city": params.location.city }, saveObj, {});
			// data for mostUsedLocation
			saveObj['type'] = type;
			saveObj['location'] = params.location;
			saveObj['cityCount'] = 1;
			saveObj['userId'] = userId;
			saveObj['subjectId']  = subjectId;
			await baseDao.save("most_used_locations",saveObj);
		}
		else{
			saveObj['type'] = type;
			saveObj['location'] = params.location;
			saveObj['cityCount'] = 1;
			//save new doc in location
			await baseDao.save("locations",saveObj);
			//save new doc in mostUsedLocation
			saveObj['userId'] = userId;
			saveObj['subjectId']  = subjectId;
			await baseDao.save("most_used_locations",saveObj);
		}

	} catch (error) {
		throw error;
	}
	}

	//delete location for all wish,bless,gratitude
	async deleteLocation(params,type,subjectId,userId)
	{
		try {
		let saveObj = {};
		let cityExist = await baseDao.findOne("locations", { "location.city": params.location.city });
		if(cityExist){
			//update cityCount in location
			saveObj['cityCount'] = cityExist.cityCount -1;
			await baseDao.updateOne("locations", {"location.city": params.location.city }, saveObj, {});
			// deleting document from mostUsedLocation
			const deleteQuery = {
				subjectId,
				userId,
				// type
			};
			await baseDao.deleteOne('most_used_locations', deleteQuery);
			// await baseDao.save("most_used_locations",saveObj);
		}
	} catch (error) {
		throw error;
	}
	}

    /**
	   * @function toolTipSave
	   * @description save tool tip view by user
	   *
	   */
	async toolTipSave(tokenData, params) {
		try {
			const isUserExist = await userDaoV1.findUserById(tokenData.userId);
			if (!isUserExist) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			let update={};
			update["$addToSet"] = {
				completeTooltip: params.type,
			  };
			let data = await baseDao.updateOne("users", { _id: tokenData.userId }, update, {});
			if(!data)return Promise.reject(MESSAGES.ERROR.SOMETHING_WENT_WRONG);
			return MESSAGES.SUCCESS.DEFAULT;
		} catch (error) {
			throw error;
		}
	}


	/**
	 * @function webhookPaymentSignatureVerification
	 * @descitpion check webhook signature
	 */
	async webhookPaymentSignatureVerification(data: any) {
		try {
			const signature = data.headers["stripe-signature"];
			const payload = data.payload.toString();
			return await stripe.webhooks.constructEvent(
				payload,
				signature,
				SERVER.STRIPE_SECRETE
			);
		} catch (error) {
			console.log("Webhook Payment Signature verification failed", error);
			return Promise.reject(MESSAGES.ERROR.SOMETHING_WENT_WRONG);
		}
	}

	/**
	 * @function userLastSeen
	 * @descitpion Update user last seen
	 */
	async userLastSeen(tokenData: TokenData) {
		try {
			await baseDao.updateOne("users", { _id: tokenData.userId }, { $set: { lastSeen: Date.now() } }, {});
			return MESSAGES.SUCCESS.DEFAULT;
		} catch (error) {
			throw error;
		}
	}

	async getReplaceURL(imgUrl){
		try{
			const parsedUrl =  new URL(imgUrl);
			// parsedUrl.hostname = "dxopuqcrjzek8.cloudfront.net/"
			let arr = parsedUrl.pathname.split("/")
			parsedUrl.pathname = arr[2];
			parsedUrl.hostname = process.env.STAGE_CLOUD_FRONT;
			const modifiedUrl = parsedUrl.toString();
			return modifiedUrl;
		}catch(err){
			console.log("err===>",err)
		}
	}
}
export const commonController = new CommonController();