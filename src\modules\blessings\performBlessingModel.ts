"use strict";

const mongoose = require("mongoose");
import { Document, model, Model, Schema } from "mongoose";
import { DB_MODEL_REF, STATUS, BLESSING_PERFORM_TYPE, FLAG_TYPE } from "@config/constant";
import { tagwishes, users, wishes } from "@modules/models";
export interface PerformBlessings extends Document {
    blessingNumber: number;
    wishId: string;
    userId: string;
    listenBlessingId: string;
    listenAudio: string;
    listenDuration?: string;
    audio?: string;
    audioLength?:string;
    notes: string;
    status: string;
    createdBy: string
    created: number;
    isGratitude:boolean;
}

const unflagHistorySchema = new Schema({
    adminId: { type: Schema.Types.ObjectId, required: true },
    _id: false
}, {
    versionKey: false,
    timestamps: true
});
const geoSchema: Schema = new mongoose.Schema({
	type: { type: String, default: "Point" },
	address: { type: String, required: false },
	coordinates: { type: [Number], index: "2dsphere", required: false }, // [longitude, latitude]
    city: { type: String, required: false },
    country: { type: String, required: false },
    state: { type: String, required: false },
}, {
	_id: false
});
const performBlessingsSchema: Schema = new mongoose.Schema({
    _id: { type: Schema.Types.ObjectId, required: true, auto: true },
    blessingNumber: { type: Number, required: false },
    wishId: { type: Schema.Types.ObjectId, required: false },
    gratitudeId: { type: Schema.Types.ObjectId, required: false },  // for global-gratitude
    wishDetail:{
        wishNumber: { type: String, required: false },
    },
    userId: { type: Schema.Types.ObjectId, required: true },
    listenBlessingId: { type: Schema.Types.ObjectId, required: false },
    listenAudio: { type: String, required: false },
    listenDuration: { type: Number, requred: false },//in seconds
    audioLength:{type:Number,required:false}, // audio length is length of audio user has recorded manually from the screen
    audio: { type: String, required: false },
    notes: { type: String, required: false },
    type: {
        type: String,
        enum: [BLESSING_PERFORM_TYPE.AUDIO, BLESSING_PERFORM_TYPE.TEXT,BLESSING_PERFORM_TYPE.NONE],
        default: BLESSING_PERFORM_TYPE.NONE,
    },
    status: {
        type: String,
        enum: [STATUS.ACTIVE, STATUS.IN_ACTIVE, STATUS.DELETED],
        default: STATUS.ACTIVE
    },
    isFlagged: { type: Boolean, required: false, default: false }, // for admin (if admin unflags the wish from admin panel)
    flagStatus: {
        type: String,
        required: false,
        enum: [FLAG_TYPE.FLAGGED, FLAG_TYPE.UN_FLAGGED, STATUS.ACTIVE, STATUS.DELETED],
        default: STATUS.ACTIVE
    }, // for admin (status to show in admin flagged wishes module)
    unflagHistory: [unflagHistorySchema],
    reportCount: { type: Number, required: true, default: 0 },
    deleteReason: { type: String, required: false },
    deletedAt: { type: Date, required: false },
    isGratitude:{type: Boolean, required: true, default: false},
    location: geoSchema,
    created: { type: Number, default: Date.now }
}, {
    versionKey: false,
    timestamps: true
});
performBlessingsSchema.pre<PerformBlessings>('save', async function (next) {
    if (this.isNew) {
        const lastEntry = await perform_blessings.findOne({}, {}, { sort: { createdAt: -1 } });
        const lastKeyValue = lastEntry ? (lastEntry.blessingNumber == undefined) ? 0 : lastEntry.blessingNumber : 0;
        this.blessingNumber = lastKeyValue + 1;
    }
    next();
});
performBlessingsSchema.post<PerformBlessings>('save', async function (doc) {
    if(!doc.isGratitude){
     await users.updateOne({ _id: doc.userId }, { $inc: { blessingCount: 1 } }, {});
     await wishes.updateOne({ _id: doc.wishId}, {  $inc: { totalBlessings: 1 } }, {});
     await tagwishes.updateOne({ wishId: doc.wishId,userId:doc.userId }, {  $set: { "lastBlessTime": new Date(Date.now()) }}, {});
    //  await wishes.updateOne({ _id: doc.wishId,userId: doc.userId}, {  $set: { "userLastBlessTime": new Date(Date.now()) }}, {});
     await wishes.updateOne({ _id: doc.wishId }, {  $set: { "userLastBlessTime": new Date(Date.now()) }}, {});
     await wishes.updateOne({ _id: doc.wishId, hostId: doc.userId}, {  $set: { "hostLastBlessTime": new Date(Date.now()) } }, {});
    }
});

performBlessingsSchema.index({ userId: 1 });
performBlessingsSchema.index({ status: 1 });
performBlessingsSchema.index({ created: -1 });
// Export categories
export const perform_blessings: Model<PerformBlessings> = model<PerformBlessings>(DB_MODEL_REF.PERFORM_BLESSING, performBlessingsSchema);