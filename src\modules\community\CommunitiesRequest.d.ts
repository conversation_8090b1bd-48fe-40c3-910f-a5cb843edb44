declare namespace CommunitiesRequest {
	export interface Id {
		communityId: string;
	}
	export interface Add {
		    name: string;
			purpose: string;
			userId?:string;
			image:string;
			members: Array<Object>;
			status?:string;
		}
	export interface CommunityList extends Pagination, Filter {
			userId?:string;
			type?:string;
		}
	export interface EditCommunity {
		name?: string;
		purpose?: string;
		communityId:string;
		image?:string;
		addMembers?: Array<Object>;
		removeMembers?: Array<Object>;
	}
	export interface CommunityDetail {
		communityId:string;
	}
	export interface DeleteCommunity {
		communityId: string;
	}
	export interface AcceptDecline {
		communityId: string;
		type: string;
	}

	export interface LeaveCommunity {
		communityId: string;
	}

	export interface CommunityListing {
		userId?: string;
		communityId?: string;
		type: string;
		pageNo: number;
		limit: number;
		sortCriteria?: string;
		sortBy?: number;
		searchKey?: string;
	}

	export interface CommunityId {
		id: string;
	}

	export interface UnflagDeleteCommunity {
		id: string;
		status: string;
		deleteReason?: string;
	}

	export interface JoinRequest {
		communityId: string;
	}

	export interface JoinRequestResponse {
		communityId: string;
		userId: string;
		accept: boolean;
	}

	export interface JoinRequestsList extends Pagination {
		communityId: string;
	}
	
}
