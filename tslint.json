{"rulesDirectory": ["node_modules/codelyzer"], "rules": {"class-name": true, "indent": [true, "tabs"], "eofline": false, "max-line-length": [true, 250], "comment-format": [true, "check-space"], "no-consecutive-blank-lines": true, "no-duplicate-variable": true, "no-eval": true, "no-internal-module": true, "no-trailing-whitespace": true, "no-var-keyword": true, "one-line": [true, "check-open-brace", "check-catch", "check-else", "check-whitespace"], "quotemark": [true, "double"], "semicolon": [true, "always"], "triple-equals": [true, "allow-null-check"], "typedef-whitespace": [true, {"call-signature": "nospace", "index-signature": "nospace", "parameter": "nospace", "property-declaration": "nospace", "variable-declaration": "nospace"}], "variable-name": [true, "ban-keywords"], "whitespace": [true, "check-branch", "check-decl", "check-operator", "check-separator", "check-type"]}}