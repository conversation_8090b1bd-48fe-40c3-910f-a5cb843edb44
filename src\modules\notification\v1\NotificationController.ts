"use strict";

import { MESSAGES, MESSAGES_NOTIFICATION, STATUS, TITLE_NOTIFICATION, TYPE_NOTIFICATION } from "@config/constant";
import { notificationDaoV1 } from "@modules/notification/index";
import { baseDao } from "@modules/baseDao";
import { notificationManager } from "@utils/NotificationManager";

export class NotificationController {

	/**
	 * @function notificationList
	 */
	async notificationList(params: ListingRequest, tokenData: TokenData) {
		try {
			const step1 = await notificationDaoV1.notificationList(params, tokenData.userId);
			return MESSAGES.SUCCESS.LIST(step1);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function notificationDelete
	 */
	async notificationDelete(tokenData: TokenData) {
		try {
			await notificationDaoV1.notificationDelete(tokenData.userId);
			return MESSAGES.SUCCESS.NOTIFICATION_DELETED;
		} catch (error) {
			throw error;
		}
	}

	async updateApp(params: UpdateAppNotification) {
        try {
            const activeUsers = await baseDao.find("users", {status: STATUS.UN_BLOCKED}, {'_id': 1});
			const version = await baseDao.findOne("versions",{});
    
            if(version) {
				let pushParamsContact = {
					title: TITLE_NOTIFICATION.UPDATE_APP,
					message: params.type == 1 ? MESSAGES_NOTIFICATION.UPDATE_APP_1.replace('[VERSION]', version.currentVersion) : MESSAGES_NOTIFICATION.UPDATE_APP_2.replace('[VERSION]', version.currentVersion),
					type: TYPE_NOTIFICATION.UPDATE_APP
				};
				for(const activeUser of activeUsers) {
					await notificationManager.PublishNotification([{"userId":activeUser._id}], null, pushParamsContact);
				}
			}
            return 'Notications(s) Sent';
        } catch (error) {
            console.log("ENTERED ERROR CATCH BLOCK");
            console.log("ERROR = ", error);
        }
    }  
}

export const notificationController = new NotificationController();