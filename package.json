{"name": "wishwell", "version": "1.0.0", "description": "Node backend in Hapi.js and TypeScript.", "main": "./build/server.js", "engines": {"node": "14.15.1", "npm": "6.14.8"}, "scripts": {"prestart": "tsc", "local": "tsc && NODE_ENV=local node ./build/server.js", "local:windows": " tsc && set NODE_ENV=local && node ./build/server.js", "default": "tsc && NODE_ENV=default node ./build/server.js", "start": "NODE_ENV=local nodemon --exec ts-node -- server.ts", "start:windows": "set NODE_ENV=local && nodemon --exec ts-node -- server.ts", "local:ts-node": "NODE_ENV=local ts-node -r tsconfig-paths/register server.ts --fast", "local:ts-node-dev": "NODE_ENV=local ts-node-dev -r tsconfig-paths/register server.ts --fast", "dev": "nodemon server.ts", "dev:debug": "nodemon --inspect server.ts", "pm2:dev": "tsc && NODE_ENV=development pm2 start ./build/server.js --name rcc-dev", "test": "mocha -r ts-node/register **/*.[sS]pec.ts", "lint": "tslint -c tslint.json 'src/**/*.ts'", "tslint": "tslint --project tsconfig.json"}, "repository": {"type": "git", "url": ""}, "author": "Technologies", "license": "ISC", "dependencies": {"@aws-sdk/client-sqs": "^3.405.0", "amqplib": "^0.7.0", "aws-sdk": "^2.802.0", "axios": "^0.19.2", "crypto-js": "^4.0.0", "csvtojson": "^2.0.10", "del": "^3.0.0", "dotenv": "^6.2.0", "exceljs": "^4.3.0", "fcm-node": "^1.5.2", "fetch": "^1.1.0", "firebase-admin": "^12.0.0", "google-auth-library": "^7.0.4", "google-libphonenumber": "^3.2.34", "googleapi": "^1.0.2", "googleapis": "^67.1.1", "handlebars": "^4.7.6", "hapi": "^18.1.0", "hapi-auth-basic": "^5.0.0", "hapi-auth-bearer-token": "^6.2.1", "hapi-i18n": "^3.0.0", "hapi-swagger": "^16.0.1", "html-pdf": "^3.0.1", "html-to-pdf": "^0.1.11", "i": "^0.3.6", "imap": "^0.8.19", "inert": "^5.1.3", "inspect": "0.0.2", "joi": "^17.9.2", "json2xls": "^0.1.2", "jsonwebtoken": "^8.5.1", "lodash": "^4.17.19", "mailparser": "^3.2.0", "md5": "^2.2.1", "messagebird": "^3.4.0", "module-alias": "^2.2.2", "moment": "^2.29.1", "moment-timezone": "^0.5.45", "mongoose": "^5.11.0", "node-cron": "^3.0.3", "nodemailer": "^6.4.16", "nodemailer-sendgrid-transport": "^0.2.0", "nodemailer-ses-transport": "^1.5.1", "nodemon": "^2.0.7", "npm": "^7.6.3", "npm-upgrade": "^3.1.0", "nvm": "0.0.4", "random-int": "^2.0.1", "randomstring": "^1.1.5", "readable": "^1.1.3", "redis": "^2.8.0", "saslprep": "^1.0.3", "shopify-api-node": "^3.7.2", "sqs-consumer": "^7.2.2", "sqs-producer": "^3.2.1", "stripe": "^12.17.0", "tinyurl": "^1.1.5", "twilio": "^3.62.0", "vision": "^5.4.4", "winston": "^3.2.1", "winston-daily-rotate-file": "^4.4.2", "xlsx": "^0.16.9"}, "devDependencies": {"@hapi/basic": "^6.0.0", "@hapi/boom": "^9.1.0", "@hapi/good": "^9.0.0", "@hapi/good-console": "^9.0.0", "@hapi/hapi": "^21.3.2", "@hapi/inert": "^7.1.0", "@hapi/vision": "^6.1.0", "@types/express-serve-static-core": "^4.17.30", "@types/hapi__hapi": "^20.0.1", "@types/hapi__inert": "^5.1.3", "@types/hapi__joi": "^17.1.6", "@types/hapi__vision": "^5.5.2", "@types/mongoose": "^5.10.1", "@types/node": "^18.11.18", "ts-node": "^8.4.1", "ts-node-dev": "^1.0.0-pre.42", "tsconfig-paths": "^3.9.0", "typescript": "4.9", "typescript-tools": "^0.3.1"}, "peerDependencies": {"@types/node": "^18.11.18"}, "_moduleAliases": {"@config": "./build/src/config", "@controllers": "./build/src/controllers", "@dao": "./build/src/dao", "@json": "./build/src/json", "@lib": "./build/src/lib/", "@mappers": "./build/src/mappers", "@models": "./build/src/models", "@plugins": "./build/src/plugins", "@routes": "./build/src/routes", "@utils": "./build/src/utils/", "@modules": "./build/src/modules"}}