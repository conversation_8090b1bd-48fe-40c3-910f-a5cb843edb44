"use strict";
import { Request, ResponseToolkit } from "@hapi/hapi";
import * as <PERSON><PERSON> from "joi";
import { failActionFunction } from "@utils/appUtils";
import { commonControllerV1 } from "@modules/common/index";
import {
	MESSAGES,
	fileUploadExts,
	SWAGGER_DEFAULT_RESPONSE_MESSAGES,
	SERVER,
	DEEPLINK_TYPE,
	REGEX,
	BLESSING_TYPE,
	LOCATION_TYPE,
	APP_TOOL_TIP
} from "@config/index";
import { imageUtil } from "@lib/ImageUtil";
import { responseHandler } from "@utils/ResponseHandler";
import { authorizationHeaderObj, headerObject } from "@utils/validator";
import { baseDao } from "@modules/baseDao/index";
export const commonRoute = [
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/common/media-upload`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const payload = request.payload;
				const result = { "image": await imageUtil.uploadSingleMediaToS3(payload.file) };
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "common"],
			description: "Media Upload",
			auth: {
				strategies: ["BasicAuth"]
			},
			payload: {
				maxBytes: 1000 * 1000 * 500,
				output: "stream",
				allow: "multipart/form-data", // important
				parse: true,
				timeout: false,
				multipart: true // <-- this fixed the media type error
			},
			validate: {
				payload: Joi.object({
					file: Joi.any().meta({ swaggerType: "file" }).required().description(fileUploadExts.join(", "))
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					payloadType: "form",
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/common/deeplink`,
	    handler: async (request: Request | any, h: any) => {
			try {
				const query: DeeplinkRequest = request.query;
				console.log("IN common route =======================>>");
				return await commonControllerV1.deepLink(request, query);
			} catch (error) {
				const message = MESSAGES.ERROR.LINK_EXPIRED.message;
				return h.view("mail-link-expired", { "name": request.query.name, "message": message, "year": new Date().getFullYear() });
			}
		},
		options: {
			tags: ["api", "common"],
			description: "Deep Link",
			validate: {
				query: Joi.object({
					android: Joi.string().trim().optional(),
					ios: Joi.string().trim().optional(),
					fallback: Joi.string().trim().optional(),
					token: Joi.string().trim().optional(),
					name: Joi.string().optional(),
					wid: Joi.string().optional(),
					uid: Joi.string().optional(),
					type: Joi.string()
						.trim()
						.valid(
							DEEPLINK_TYPE.MAGIC_LINK,
							DEEPLINK_TYPE.COM_MAP,
							DEEPLINK_TYPE.SMS_INVITE,
							DEEPLINK_TYPE.VERIFY_USER_EMAIL,
							DEEPLINK_TYPE.FORGOT_PASSWORD,
							DEEPLINK_TYPE.WISH_SHARE
						)
						.required(),
					email: Joi.string().optional(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},

	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/common/user-search`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
		  try {
			const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
			const query: ListingRequest = request.query;
			const result = await commonControllerV1.userSearch(query,tokenData);
			return responseHandler.sendSuccess(h, result);
		  } catch (error) {
			return responseHandler.sendError(request, error);
		  }
		},
		options: {
		  tags: ["api", "common"],
		  description: "user list api",
		  auth: {
			strategies: ["CommonAuth"],
		  },
		  validate: {
			headers: authorizationHeaderObj,
			query: Joi.object({
			  pageNo: Joi.number().required().description("Page no"),
			  limit: Joi.number().required().description("limit"),
			  searchKey: Joi.string()
				.optional()
				.description("Search by user name"),
			 
			}),
			failAction: failActionFunction,
		  },
		  plugins: {
			"hapi-swagger": {
			  responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES,
			},
		  },
		},
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/common/user-contacts`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
		  try {
			const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
			const query: ListingRequest = request.query;
			const result = await commonControllerV1.userContacts(query,tokenData);
			return responseHandler.sendSuccess(h, result);
		  } catch (error) {
			return responseHandler.sendError(request, error);
		  }
		},
		options: {
		  tags: ["api", "common"],
		  description: "user contact list api",
		  auth: {
			strategies: ["CommonAuth"],
		  },
		  validate: {
			headers: authorizationHeaderObj,
			query: Joi.object({
			  pageNo: Joi.number().required().description("Page no"),
			  limit: Joi.number().required().description("limit"),
			  type:Joi.string().required().description("1->contcat & appuser both,2->exists in app with my contact"),
			  wishId: Joi.string().trim().regex(REGEX.MONGO_ID).optional(),
			  searchKey: Joi.string()
				.optional()
				.description("Search by user name"),
			deviceId: Joi.string().trim().optional(),	
			}),
			failAction: failActionFunction,
		  },
		  plugins: {
			"hapi-swagger": {
			  responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES,
			},
		  },
		},
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v2/common/user-contacts`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
		  try {
			const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
			const query: ListingRequest = request.query;
			const result = await commonControllerV1.userContactsV2(query,tokenData);
			return responseHandler.sendSuccess(h, result);
		  } catch (error) {
			return responseHandler.sendError(request, error);
		  }
		},
		options: {
		  tags: ["api", "common"],
		  description: "user contact list api",
		  auth: {
			strategies: ["CommonAuth"],
		  },
		  validate: {
			headers: authorizationHeaderObj,
			query: Joi.object({
			  pageNo: Joi.number().required().description("Page no"),
			  limit: Joi.number().required().description("limit"),
			  type:Joi.string().required().description("1->contcat & appuser both,2->exists in app with my contact"),
			  wishId: Joi.string().trim().regex(REGEX.MONGO_ID).optional(),
			  searchKey: Joi.string()
				.optional()
				.description("Search by user name"),
			deviceId: Joi.string().trim().optional(),	
			}),
			failAction: failActionFunction,
		  },
		  plugins: {
			"hapi-swagger": {
			  responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES,
			},
		  },
		},
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/common/global-wishes`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
		  try {
			const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
			const query: ListingRequest = request.query;
			const result = await commonControllerV1.globalWishList(query,tokenData);
			return responseHandler.sendSuccess(h, result);
		  } catch (error) {
			return responseHandler.sendError(request, error);
		  }
		},
		options: {
		  tags: ["api", "common"],
		  description: "global wish list",
		  auth: {
			strategies: ["CommonAuth"],
		  },
		  validate: {
			headers: authorizationHeaderObj,
			query: Joi.object({
			  pageNo: Joi.number().required().description("Page no"),
			  limit: Joi.number().required().description("limit"),
			  searchKey: Joi.string()
				.optional()
				.description("Search by user name"),
			  disasterCategory: Joi.number()
				.optional()
				.description("Filter by disaster category"),
			}),
			failAction: failActionFunction,
		  },
		  plugins: {
			"hapi-swagger": {
			  responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES,
			},
		  },
		},
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/common/dropdown`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
		  try {
			const query: Dropdown = request.query;
			const result = await commonControllerV1.dropdown(query);
			return responseHandler.sendSuccess(h, result);
		  } catch (error) {
			return responseHandler.sendError(request, error);
		  }
		},
		options: {
		  tags: ["api", "common"],
		  description: "dropdown",
		  validate: {
			query: Joi.object({
				type: Joi.string().trim().valid(LOCATION_TYPE.CITIES,LOCATION_TYPE.COUNTRIES,LOCATION_TYPE.STATES).required(),
				countryId: Joi.number().optional(),
				stateId: Joi.number().optional(),
				searchKey: Joi.string()
				.optional()
				.description("Search by country, state and city"),
			 
			 
			}),
			failAction: failActionFunction,
		  },
		  plugins: {
			"hapi-swagger": {
			  responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES,
			},
		  },
		},
	},
		
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/common/blessing-library`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
		  try {
			const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
			const query: ListingRequest = request.query;
			const result = await commonControllerV1.userBlessingLib(query,tokenData);
			return responseHandler.sendSuccess(h, result);
		  } catch (error) {
			return responseHandler.sendError(request, error);
		  }
		},
		options: {
		  tags: ["api", "common"],
		  description: "user bless lib list api",
		  auth: {
			strategies: ["CommonAuth"],
		  },
		  validate: {
			headers: authorizationHeaderObj,
			query: Joi.object({
				type: Joi.string().trim().valid(BLESSING_TYPE.MEDITATION,BLESSING_TYPE.PRAYER).required(),
				pageNo: Joi.number().optional().description("Page no"),
				limit: Joi.number().optional().description("limit"),
				
			}),
			failAction: failActionFunction,
		  },
		  plugins: {
			"hapi-swagger": {
			  responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES,
			},
		  },
		},
	},

	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/common/top-pinned-global-wish`, //admin 3 pinned global wish list
		handler: async (request: Request | any, h: ResponseToolkit) => {
		  try {
			const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
			const query: ListingRequest = request.query;
			const result = await commonControllerV1.topPinnedGlobal(tokenData);
			return responseHandler.sendSuccess(h, result);
		  } catch (error) {
			return responseHandler.sendError(request, error);
		  }
		},
		options: {
		  tags: ["api", "common"],
		  description: "user contact list api",
		  auth: {
			strategies: ["CommonAuth"],
		  },
		  validate: {
			headers: authorizationHeaderObj,
			query: Joi.object({
			
			}),
			failAction: failActionFunction,
		  },
		  plugins: {
			"hapi-swagger": {
			  responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES,
			},
		  },
		},
	},

	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/common/thank-wishwell-tooltip`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
		  try {
			const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
			const result = await commonControllerV1.thankWishwellTooltip();
			return responseHandler.sendSuccess(h, result);
		  } catch (error) {
			return responseHandler.sendError(request, error);
		  }
		},
		options: {
		  tags: ["api", "common"],
		  description: "thank wishwell tooltip",
		  auth: {
			strategies: ["CommonAuth"],
		  },
		  validate: {
			headers: authorizationHeaderObj,
			failAction: failActionFunction,
		  },
		  plugins: {
			"hapi-swagger": {
			  responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES,
			},
		  },
		},
	},

	{
		method: "PUT",
		path: `${SERVER.API_BASE_URL}/v1/common/thank-wishwell-tooltip`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
		  try {
			const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
			const payload = request.payload;
			const result = await commonControllerV1.editThankWishwellTooltip(payload);
			return responseHandler.sendSuccess(h, result);
		  } catch (error) {
			return responseHandler.sendError(request, error);
		  }
		},
		options: {
		  tags: ["api", "common"],
		  description: "edit thank wishwell tooltip",
		  auth: {
			strategies: ["CommonAuth"],
		  },
		  validate: {
			headers: authorizationHeaderObj,
			payload: Joi.object({
				data: Joi.string().required()
			}),
			failAction: failActionFunction,
		  },
		  plugins: {
			"hapi-swagger": {
			  responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES,
			},
		  },
		},
	},
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/common/payment-intent`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
		  try {
			const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
			const payload = request.payload;
			const result = await commonControllerV1.createPaymentIntent(tokenData, payload);
			return responseHandler.sendSuccess(h, result);
		  } catch (error) {
			return responseHandler.sendError(request, error);
		  }
		},
		options: {
		  tags: ["api", "common"],
		  description: "create payment intent for thanks-wishwell api",
		  auth: {
			strategies: ["CommonAuth"],
		  },
		  validate: {
			headers: authorizationHeaderObj,
			payload: Joi.object({
				amount: Joi.number().required(),
				currency: Joi.string().required()
			}),
			failAction: failActionFunction,
		  },
		  plugins: {
			"hapi-swagger": {
			  responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES,
			},
		  },
		},
	},

	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/common/stripe-webhook`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
		  try {
			const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
			const result = await commonControllerV1.stripeWebhook(tokenData,request);
			return responseHandler.sendSuccess(h, result);
		  } catch (error) {
			return responseHandler.sendError(request, error);
		  }
		},
		options: {
		  tags: ["api", "common"],
		  description: "webhook API for stripe payents",
		  validate: {
			//headers: authorizationHeaderObj,
			failAction: failActionFunction,
		  },
		  plugins: {
			"hapi-swagger": {
			  responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES,
			},
		  },
		},
	},

	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/common/tool-tip`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
		  try {
			const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
			const result = APP_TOOL_TIP;
			return responseHandler.sendSuccess(h, result);
		  } catch (error) {
			return responseHandler.sendError(request, error);
		  }
		},
		options: {
		  tags: ["api", "common"],
		  description: "app all 14 toolTip data",
		  auth: {
			strategies: ["CommonAuth"],
		  },
		  validate: {
			headers: authorizationHeaderObj,
			query: Joi.object({
			 
			}),
			failAction: failActionFunction,
		  },
		  plugins: {
			"hapi-swagger": {
			  responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES,
			},
		  },
		},
	},
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/common/tool-tip-step-view`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
		  try {
			const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
			const payload = request.payload;
			const result = await commonControllerV1.toolTipSave(tokenData,payload);
			return responseHandler.sendSuccess(h, result);
		  } catch (error) {
			return responseHandler.sendError(request, error);
		  }
		},
		options: {
		  tags: ["api", "common"],
		  description: "user view tool tip one by one and this api manage all steps for one time view and next time hide",
		  auth: {
			strategies: ["CommonAuth"],
		  },
		  validate: {
			headers: authorizationHeaderObj,
			payload: Joi.object({
				type: Joi.number().required().valid(1,2,3,4,5,6,7,8,9,10,11,12,13,14,15).description("1->Blessings,2->Add Wish to Home Screen,3->Quick Pin,4->Home page>Stats,5->Remove wish from Home screen,6->Wishes,7->Create Wish,8->Co-Host,9->Map,10->well,11->Account/Stats,12->Communities,13->Thank Wishwell,14->Create Communities"),
				
			}),
			failAction: failActionFunction,
		  },
		  plugins: {
			"hapi-swagger": {
			  responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES,
			},
		  },
		},
	},
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/common/user-last-seen`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const result = await commonControllerV1.userLastSeen(tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "common"],
			description: "Update user last seen",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				failAction: failActionFunction,
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES,
				},
			},
		},
	},
];