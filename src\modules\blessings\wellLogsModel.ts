"use strict";
const mongoose = require("mongoose");
import { Document, model, Model, Schema } from "mongoose";
import { DB_MODEL_REF, NOTIFICATION_TYPE, STATUS } from "@config/constant";
export interface IWellLogs extends Document {
	senderId?: Schema.Types.ObjectId;
	hostId?: Schema.Types.ObjectId;
	receiverId: Schema.Types.ObjectId;
	subjectId: Schema.Types.ObjectId;
	subjectDetail:Object;
	type: string;
	isRead: boolean;
	status?: string;
	// isEdited?: boolean;
	created: number;
}
const wellLogsSchema: Schema = new mongoose.Schema({
	senderId: {
		type: Schema.Types.ObjectId,
		ref: DB_MODEL_REF.USER,
		required: false
	},  
	receiverId: { 
		type: [Schema.Types.ObjectId],
		ref: DB_MODEL_REF.USER,
		required: false,
		default:[]
	},
	taggedUser: { 
		type: [Schema.Types.ObjectId],
		ref: DB_MODEL_REF.USER,
		required: false,
		default:[]
	},
	globalGratiduteUser: { 
		type: [Schema.Types.ObjectId],
		ref: DB_MODEL_REF.USER,
		required: false,
		default:[]
	},
	subjectId: {
		type: Schema.Types.ObjectId,
		ref: DB_MODEL_REF.USER,
		required: false
	},
	subjectDetail: {
		audio: { type: String, required: false },
		notes: { type: String, required: false },
		type: { type: String, required: false },
		description:{ type: String, required: false },
		audioLength:{type:String,required:false}, // audio length is length of audio user has recorded manually from the screen
		deleteReason: { type: String, required: false },
		listenDuration: { type: String, required: false }, // this is length of audio before blessing / audio uploaded from admin
		wishImage: { type: String, required: false },
	},
	type: {
		type: String,
		required: true,
		enum: Object.values(NOTIFICATION_TYPE)
	},
	isRead: { type: Boolean, default: false },
	status: {
		type: String,
		required: false,
		default: STATUS.ACTIVE
	},
	hostId:{
		type: Schema.Types.ObjectId,
		required: false
	},
	// isEdited: {
	// 	type: Boolean,
	// 	required: false,
	// },
	created: { type: Number, default: Date.now }
}, {
	versionKey: false,
	timestamps: true
});
wellLogsSchema.index({ receiverId: 1 });
wellLogsSchema.index({ senderId: 1 });
wellLogsSchema.index({ status: 1 });
// Export notification schema
export const well_logs: Model<IWellLogs> = model<IWellLogs>(DB_MODEL_REF.WELL_LOGS, wellLogsSchema);