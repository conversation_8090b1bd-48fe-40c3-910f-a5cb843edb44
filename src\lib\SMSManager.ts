"use strict";

import {  SERVER } from "@config/index";
import * as AWS from "aws-sdk";
let smsCounter = 0;

export class SMSManager {
	sendMessageViaAWS(countryCode, mobileNo, body) {
		// Set region
		console.log(body,'herehereherehereherehereherehere')
		AWS.config.update({
			region: SERVER.AWS_IAM_USER.REGION,
			accessKeyId: SERVER.AWS_IAM_USER.ACCESS_KEY_ID,
			secretAccessKey: SERVER.AWS_IAM_USER.SECRET_ACCESS_KEY
		});   
		let mobileNumber;
		if(mobileNo){
		if(mobileNo.length>10){
			mobileNumber = "+" + mobileNo
		}else{
			mobileNumber =  "+" + countryCode + mobileNo
		}
		let publishTextPromise;
		const params = {
			Protocol: 'sms', // Protocol for subscription (sms for text messages)
			TopicArn: SERVER.SMS_ARN_ENDPOINT,//'arn:aws:sns:ca-central-1:565790199342:dev-ww-ca-central-1-sns', // ARN of the SNS topic
			Endpoint: mobileNumber // Phone number to subscribe (include country code)
		  };
		  let subscribe = new AWS.SNS({ apiVersion: "2010-03-31" }).subscribe(params, (err, data) => {
			if (err) {
			  console.log('Error subscribing phone number:', err);
			} else {
			  console.log('Phone number subscribed successfully.');
			}
		  });
		  console.log("Mobile number : ", mobileNo);
		  console.log("Mobile number : ", mobileNumber);
		  //Create promise and SNS service object
		  publishTextPromise = new AWS.SNS({ apiVersion: "2010-03-31" }).publish({
			  Message: body,
			  PhoneNumber: mobileNumber
		  }).promise();
		publishTextPromise
			.then(function (data) {
				console.log("published data.............",data)
				console.log("MessageID...... is " + data.MessageId);
				smsCounter++;
			})
			.catch(function (error) {
				console.error(error, error.stack);
				throw error;
			});
		}else{
			console.log('Mobile number check')
		}
	}
}
export const smsManager = new SMSManager();