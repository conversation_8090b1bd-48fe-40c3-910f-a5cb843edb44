declare namespace GratitudesRequest {
    export interface Add {
		wishId?: string;
		blessingId?: string;
		audio: string;
		notes:string;
		type:string;
		userId?:string;
		userDetail?:Object;
		gratitudeType?;string;
		blessingCreatorId?:string;
		audioLength?:number;
		location:Object;
	}
    export interface FlaggedGratitude {
		gratitudeId?: string;
		pageNo: number;
		limit: number;
		sortBy?: number;
		sortCriteria?: string;
		searchKey?: string;
    }

    export interface GratitudeId {
        id: string;
    }
	
	export interface UnflagDeleteGratitude {
		id: string;
		status: string;
		deleteReason?: string;
	}
}