"use strict";
import { BaseDao, baseDao } from "@modules/baseDao/BaseDao";
import { STATUS } from "@config/constant";
import { escapeSpecialCharacter } from "@utils/appUtils";
import { toObjectId } from "@utils/appUtils";

export class CommunitiesDao extends BaseDao {
	/**    
	 * @function findCommunityById
	 */
	async findCommunityById(communityId: string, project = {}) {
		try {
			const query: any = {};
			query._id = toObjectId(communityId);
			query.status = { '$nin': [STATUS.DELETED, STATUS.IN_ACTIVE, STATUS.UN_PUBLISHED] };
			const projection = (Object.values(project).length) ? project : { createdAt: 0, updatedAt: 0 };
			return await this.findOne("communities", query, projection);
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function addCommunities
	 */
	async addCommunities(params: CommunitiesRequest.Add) {
		try {
			return await this.save("communities", params);
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function addMember
	 */
	async addMember(params) {
		try {
			return await this.insertMany("members", params, {});
		} catch (error) {
			throw error;
		}
	}

	async getPublicCommunities(params: any) {
		try {
			const aggPipe: any[] = [];

			const match: any = {
				visibility: "PUBLIC",
				status: STATUS.ACTIVE
			};

			if (params.searchKey) {
				const regex = new RegExp(escapeSpecialCharacter(params.searchKey), "i");
				match["name"] = { $regex: regex };
			}

			aggPipe.push({ $match: match });

			aggPipe.push({
				$project: {
					_id: 1,
					name: 1,
					purpose: 1,
					image: 1,
					created: 1
				}
			});

			aggPipe.push({ $sort: { created: -1 } });

			const response = await this.paginate("communities", aggPipe, params.limit, params.pageNo, {}, true);
			return response;
		} catch (error) {
			console.error("❌ Error in getPublicCommunities DAO:", error);
			throw error;
		}
	}
	async getPublicCommunityById(communityId: string, project = {}) {
		try {
			const query: any = {
				_id: toObjectId(communityId),
				visibility: "PUBLIC",
				status: { $nin: [STATUS.DELETED, STATUS.IN_ACTIVE, STATUS.UN_PUBLISHED, STATUS.REJECTED] }
			};

			const projection = Object.values(project).length
				? project
				: { createdAt: 0, updatedAt: 0 };

			return await this.findOne("communities", query, projection);
		} catch (error) {
			console.error("❌ Error in getPublicCommunityById DAO:", error);
			throw error;
		}
	}
}

export const communitiesDao = new CommunitiesDao();