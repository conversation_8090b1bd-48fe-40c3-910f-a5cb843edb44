"use strict";
const mongoose = require("mongoose");
import { Document, model, Model, Schema } from "mongoose";
import {
	DB_MODEL_REF,STATUS
} from "@config/index";

export interface IPinnedWish extends Document {
	userId: string;
	subjectId: boolean;
	status: string;
    created: number;

}
const pinnedWishesSchema: Schema = new mongoose.Schema({
	_id: { type: Schema.Types.ObjectId, required: true, auto: true },
    userId: { type: Schema.Types.ObjectId, required: true },
	subjectId: { type: Schema.Types.ObjectId, required: true },  // for now wishId
	status: {
        type: String,
        enum: [STATUS.ACTIVE, STATUS.IN_ACTIVE,STATUS.DELETED,STATUS.CLOSED,STATUS.REPORTED],
        default: STATUS.ACTIVE
    },
	created: { type: Number, default: Date.now }
}, {
	versionKey: false,
	timestamps: true
});

pinnedWishesSchema.index({ created: -1 });

// Export user
export const pinned_wishes: Model<IPinnedWish> = model<IPinnedWish>(DB_MODEL_REF.PINNED_WISHES, pinnedWishesSchema);