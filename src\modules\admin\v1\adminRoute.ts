"use strict";

import { Request, ResponseToolkit } from "@hapi/hapi";
import * as <PERSON><PERSON> from "joi";

import { adminControllerV1 } from "@modules/admin/index";

import { failActionFunction } from "@utils/appUtils";
import { authorizationHeaderObj, headerObject } from "@utils/validator";
import { verifyAdminToken } from "@lib/tokenManager";
import {
	REGEX,
	SWAGGER_DEFAULT_RESPONSE_MESSAGES,
	STATUS,
	USER_TYPE,
	VALIDATION_CRITERIA,
	VALIDATION_MESSAGE,
	SERVER,
	DEVICE_TYPE,
	BLESSING_TYPE,
	BLESSING_VOICEOVER,
	BLESSING_CREATED_BY,
	BLESSING_SORT_CRITERIA,
	DASH_EXP_TYPE,
	WISH_INTESION,
	WISH_SORT_CRITERIA,
	USER_SORT_CRITERIA,
	INVESTOR_SORT_CRITERIA,
	FLAGGED_WISH_SORT_CRITERIA,
	MIME_TYPE
} from "@config/index";
import { responseHandler } from "@utils/ResponseHandler";

export const adminRoute = [
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/admin/login`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const headers = request.headers;
				const payload: AdminRequest.Login = request.payload;
				payload.remoteAddress = request["headers"]["x-forwarded-for"] || request.info.remoteAddress;
				const result = await adminControllerV1.login({ ...headers, ...payload });
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin onboarding"],
			description: "Admin Login",
			auth: {
				strategies: ["BasicAuth"]
			},
			validate: {
				headers: headerObject["required"],
				payload: Joi.object({
					email: Joi.string()
						.trim()
						.lowercase()
						.email({ minDomainSegments: 2 })
						.regex(REGEX.EMAIL)
						.default(SERVER.ADMIN_CREDENTIALS.EMAIL)
						.required(),
					password: Joi.string()
						.trim()
						.regex(REGEX.PASSWORD_V2)
						.min(VALIDATION_CRITERIA.PASSWORD_MIN_LENGTH)
						.max(VALIDATION_CRITERIA.PASSWORD_MAX_LENGTH)
						.default(SERVER.DEFAULT_PASSWORD)
						.required()
						.messages({
							"string.pattern.base": VALIDATION_MESSAGE.password.pattern,
							"string.min": VALIDATION_MESSAGE.password.minlength,
							"string.max": VALIDATION_MESSAGE.password.maxlength,
							"string.empty": VALIDATION_MESSAGE.password.required,
							"any.required": VALIDATION_MESSAGE.password.required
						}),
					deviceId: Joi.string().trim().required(),
					platform: Joi.string().required().valid(DEVICE_TYPE.WEB),
					deviceToken: Joi.string().optional()
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/admin/logout`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const result = await adminControllerV1.logout(tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin onboarding"],
			description: "Logout",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "PUT",
		path: `${SERVER.API_BASE_URL}/v1/admin/set-new-password`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const headers = request.headers;
				const payload: AdminRequest.SetNewPassword = request.payload;
				payload.remoteAddress = request["headers"]["x-forwarded-for"] || request.info.remoteAddress;
				const result = await adminControllerV1.setNewPassword({...headers, ...payload});
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin onboarding"],
			description: "Set New Password",
			auth: {
				strategies: ["BasicAuth"]
			},
			validate: {
				headers: headerObject["required"],
				payload: Joi.object({
					userId: Joi.string().trim().regex(REGEX.MONGO_ID).required(),
					password: Joi.string()
						.trim()
						.regex(REGEX.PASSWORD_V2)
						.min(VALIDATION_CRITERIA.PASSWORD_MIN_LENGTH)
						.max(VALIDATION_CRITERIA.PASSWORD_MAX_LENGTH)
						.default(SERVER.DEFAULT_PASSWORD)
						.required()
						.messages({
							"string.pattern.base": VALIDATION_MESSAGE.password.pattern,
							"string.min": VALIDATION_MESSAGE.password.minlength,
							"string.max": VALIDATION_MESSAGE.password.maxlength,
							"string.empty": VALIDATION_MESSAGE.password.required,
							"any.required": VALIDATION_MESSAGE.password.required
						}),
					deviceId: Joi.string().trim().required(),
					platform: Joi.string().required().valid(DEVICE_TYPE.WEB),
					deviceToken: Joi.string().optional()
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "PUT",
		path: `${SERVER.API_BASE_URL}/v1/admin/profile-complete`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const headers = request.headers;
				const payload: AdminRequest.AdminDetails = request.payload;
				payload.remoteAddress = request["headers"]["x-forwarded-for"] || request.info.remoteAddress;
				const result = await adminControllerV1.updateAdminDetails({...headers, ...payload});
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin onboarding"],
			description: "Update admin details first time",
			auth: {
				strategies: ["BasicAuth"]
			},
			validate: {
				headers: headerObject["required"],
				payload: Joi.object({
					userId: Joi.string().trim().regex(REGEX.MONGO_ID).required(),
					firstName: Joi.string()
						.trim()
						.min(VALIDATION_CRITERIA.NAME_MIN_LENGTH)
						.required(),
					lastName: Joi.string()
						.trim()
						.min(VALIDATION_CRITERIA.NAME_MIN_LENGTH)
						.required(),
					contactNo: Joi.string()
						.trim()
						.optional(),
					deviceId: Joi.string().trim().required(),
					platform: Joi.string().required().valid(DEVICE_TYPE.WEB),
					deviceToken: Joi.string().optional()
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/admin/forgot-password`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const payload: AdminRequest.ForgotPasswordRequest = request.payload;
				const result = await adminControllerV1.forgotPassword(payload);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin onboarding"],
			description: "Forgot Password",
			auth: {
				strategies: ["BasicAuth"]
			},
			validate: {
				headers: headerObject["required"],
				payload: Joi.object({
					email: Joi.string()
						.trim()
						.lowercase()
						.email({ minDomainSegments: 2 })
						.regex(REGEX.EMAIL)
						.default(SERVER.ADMIN_CREDENTIALS.EMAIL)
						.required()
						.messages({
							"string.pattern.base": VALIDATION_MESSAGE.email.pattern
						})
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/admin/verify-reset-password-token`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const payload: AdminRequest.VerifyResetPasswordToken = request.query;
				const result = await adminControllerV1.verifyResetPasswordToken(payload);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin onboarding"],
			description: "Verify reset password token",
			auth: {
				strategies: ["BasicAuth"]
			},
			validate: {
				headers: headerObject["required"],
				query: Joi.object({
					token: Joi.string().required()
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/admin/reset-password`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const payload: AdminRequest.ChangeForgotPassword = request.payload;
				const result = await adminControllerV1.resetPassword(payload);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin onboarding"],
			description: "Reset Password After forgot password",
			auth: {
				strategies: ["BasicAuth"]
			},
			validate: {
				headers: headerObject["required"],
				payload: Joi.object({
					token: Joi.string()
						.required(),
					password: Joi.string()
						.trim()
						.regex(REGEX.PASSWORD_V2)
						.min(VALIDATION_CRITERIA.PASSWORD_MIN_LENGTH)
						.max(VALIDATION_CRITERIA.PASSWORD_MAX_LENGTH)
						.default(SERVER.DEFAULT_PASSWORD)
						.required()
						.messages({
							"string.pattern.base": VALIDATION_MESSAGE.password.pattern,
							"string.min": VALIDATION_MESSAGE.password.minlength,
							"string.max": VALIDATION_MESSAGE.password.maxlength,
							"string.empty": VALIDATION_MESSAGE.password.required,
							"any.required": VALIDATION_MESSAGE.password.required
						})
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/admin/dashboard`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const result = await adminControllerV1.dashboard();
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin dashboard"],
			description: "Dashboard counts",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/admin/dashboard-expansion`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const headers = request.headers;
				const query: AdminRequest.DashboardExpansion = request.query;
				const result = await adminControllerV1.dashboardExpansion({ ...headers, ...query });
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin dashboard"],
			description: "Dashboard expansion",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				query: Joi.object({
					type: Joi.string().required().valid(DASH_EXP_TYPE.MONTH, DASH_EXP_TYPE.DATE),
					startDate: Joi.string().optional(),
					endDate: Joi.string().optional(),
					pageNo: Joi.number().required(),
					limit: Joi.number().required(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/admin/dashboard-expansion-export`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const query = request.query;
				const step1 = await verifyAdminToken(query.token);
				if(!step1) {
					return h.redirect(`${SERVER.ADMIN_URL}/notFound`);
				}
				const result = await adminControllerV1.dashboardExpansionExport(query);
				// return responseHandler.sendSuccess(h, result);

				const response = request.raw.res;
                const Reportres = result.data;
                response.setHeader("Content-Type", MIME_TYPE.XLSX);
				const ct = new Date();
				const formattedTime = `${ct.getDate()}-${ct.getMonth() + 1}-${ct.getFullYear()}-${ct.getTime()}`;
                const fileName: string = `Dashboard-Report-${formattedTime}.xlsx`;
                response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
                response.setHeader("Access-Control-Allow-Origin", "*");
                await Reportres.write(response);
                response.end();
                return h.abandon;
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin dashboard"],
			description: "Dashboard expansion export data",
			validate: {
				query: Joi.object({
					token: Joi.string().required(),
					startDate: Joi.string().optional(),
					endDate: Joi.string().optional()
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/admin/profile`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const result = await adminControllerV1.adminDetails(tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin"],
			description: "Admin Details",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "PUT",
		path: `${SERVER.API_BASE_URL}/v1/admin/profile`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const payload: AdminRequest.EditProfile = request.payload;
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const result = await adminControllerV1.editProfile(payload, tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin"],
			description: "Edit Profile",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
					profilePicture: Joi.string().trim().optional().allow(''),
					firstName: Joi.string()
						.trim()
						// .min(VALIDATION_CRITERIA.NAME_MIN_LENGTH)
						.required().allow(""),
					lastName: Joi.string()
						.trim()
						// .min(VALIDATION_CRITERIA.NAME_MIN_LENGTH)
						.required().allow(""),
					email: Joi.string()
						.trim()
						.lowercase()
						.email({ minDomainSegments: 2 })
						.regex(REGEX.EMAIL)
						.optional(),
					contactNo: Joi.string()
						.trim()
						.optional()
						.allow(''),
					company: Joi.string()
						.trim()
						.optional()
						.allow(''),
					jobTitle: Joi.string()
						.trim()
						.optional()
						.allow(''),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/admin/change-password`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const payload: ChangePasswordRequest = request.payload;
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;

				const result = await adminControllerV1.changePassword(payload, tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin"],
			description: "Change Password",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
					oldPassword: Joi.string()
						.trim()
						.min(VALIDATION_CRITERIA.PASSWORD_MIN_LENGTH)
						.max(VALIDATION_CRITERIA.PASSWORD_MAX_LENGTH)
						.default(SERVER.DEFAULT_PASSWORD)
						.required(),
					password: Joi.string()
						.trim()
						.regex(REGEX.PASSWORD_V2)
						.min(VALIDATION_CRITERIA.PASSWORD_MIN_LENGTH)
						.max(VALIDATION_CRITERIA.PASSWORD_MAX_LENGTH)
						.default(SERVER.DEFAULT_PASSWORD)
						.required()
						.messages({
							"string.pattern.base": VALIDATION_MESSAGE.password.pattern,
							"string.min": VALIDATION_MESSAGE.password.minlength,
							"string.max": VALIDATION_MESSAGE.password.maxlength,
							"string.empty": VALIDATION_MESSAGE.password.required,
							"any.required": VALIDATION_MESSAGE.password.required
						})
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/admin/blessings`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const query: AdminRequest.BlessingList = request.query;
				const result = await adminControllerV1.blessingListing(query);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin blessings"],
			description: "Blessing Listing",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				query: Joi.object({
					pageNo: Joi.number().required(),
					limit: Joi.number().required(),
					sortCriteria: Joi.string().trim().valid(BLESSING_SORT_CRITERIA.NAME, BLESSING_SORT_CRITERIA.CREATED_AT, BLESSING_SORT_CRITERIA.STATUS, BLESSING_SORT_CRITERIA.LANGUAGE, BLESSING_SORT_CRITERIA.VOICEOVER, BLESSING_SORT_CRITERIA.INTENSION, BLESSING_SORT_CRITERIA.AUTHOR_NAME).optional(),
					sortBy: Joi.number().valid(1, 0).optional(),
					searchKey: Joi.string().optional(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/admin/blessings`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const payload = request.payload;
				const result = await adminControllerV1.createBlessing(payload);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin blessings"],
			description: "Create Global Blessing",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
					image: Joi.string().required(),
					name: Joi.string().required(),
					blessingType: Joi.string().required().valid(BLESSING_TYPE.MEDITATION, BLESSING_TYPE.PRAYER),
					language: Joi.string().required(),
					audioFile: Joi.string().required(),
					voiceover: Joi.string().required().valid(BLESSING_VOICEOVER.MASCULINE, BLESSING_VOICEOVER.FEMININE),
					authorName: Joi.string().required(),
					intension: Joi.string().required().valid(WISH_INTESION.ABUNDANCE, WISH_INTESION.HEALTH, WISH_INTESION.LOVE, WISH_INTESION.PEACE)
					// isGlobalBlessing: Joi.boolean().required().valid(true),
					// type: Joi.string().required().valid(BLESSING_CREATED_BY.ADMIN)
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/admin/wish`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const payload: AdminRequest.CreateWish = request.payload;
				const result = await adminControllerV1.createWish(payload, tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin wish"],
			description: "Create Global Wish",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
					title: Joi.string().required().trim(),
					description: Joi.string().required().trim(),
					intension: Joi.string().required().valid(WISH_INTESION.ABUNDANCE, WISH_INTESION.HEALTH, WISH_INTESION.LOVE, WISH_INTESION.PEACE),
					image: Joi.string().required(),
					isGlobalWish: Joi.boolean().required().valid(true),
					disasterCategory: Joi.number().optional(),
					familyName: Joi.string().optional(),
					location: Joi.object({
						type: Joi.string().valid("Point").required(),
						address: Joi.string().required(),
						coordinates: Joi.array()
							.length(2)
							.items(Joi.number())
							.required(),
						city: Joi.string().required(),
						country: Joi.string().required(),
						state: Joi.string().required()
					  }).optional()
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/admin/wish`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const query: AdminRequest.WishList = request.query;
				const result = await adminControllerV1.wishListing(query);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin wish"],
			description: "Global Wish listing",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				query: Joi.object({
					searchKey: Joi.string().optional(),
					pageNo: Joi.number().required(),
					limit: Joi.number().required(),
					sortCriteria: Joi.string().trim().valid(WISH_SORT_CRITERIA.TITLE, WISH_SORT_CRITERIA.INTENSION, WISH_SORT_CRITERIA.CREATED_AT).optional(),
					sortBy: Joi.number().valid(1, 0).optional(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/admin/wish/{id}`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const params: AdminRequest.WishDetail = request.params;
				const result = await adminControllerV1.wishDetails(params);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin wish"],
			description: "Global Wish details",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				params: Joi.object({
					id: Joi.string().required(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "PUT",
		path: `${SERVER.API_BASE_URL}/v1/admin/wish/{id}`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const params: AdminRequest.WishEdit = request.params;
				const payload: AdminRequest.CreateWish = request.payload;
				const result = await adminControllerV1.editWish({ ...params, ...payload });
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin wish"],
			description: "Edit Global Wish",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				params: Joi.object({
					id: Joi.string().required(),
				}),
				payload: Joi.object({
					title: Joi.string().required().trim(),
					description: Joi.string().required().trim(),
					intension: Joi.string().required().valid(WISH_INTESION.ABUNDANCE, WISH_INTESION.HEALTH, WISH_INTESION.LOVE, WISH_INTESION.PEACE),
					image: Joi.string().optional(),
					isGlobalWish: Joi.boolean().required().valid(true)
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "DELETE",
		path: `${SERVER.API_BASE_URL}/v1/admin/wish`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const query: AdminRequest.WishDetail = request.query;
				const result = await adminControllerV1.deleteWish(query);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin wish"],
			description: "Delete Global Wish",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				query: Joi.object({
					id: Joi.string().required(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/admin/flagged-wishes`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const query: AdminRequest.FlaggedWish = request.query;
				const result = await adminControllerV1.flaggedWishes(query);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin flagged wish"],
			description: "Flagged wishes",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				query: Joi.object({
					pageNo: Joi.number().required(),
					limit: Joi.number().required(),
					sortCriteria: Joi.string().trim().valid(FLAGGED_WISH_SORT_CRITERIA.WISH_NUMBER, FLAGGED_WISH_SORT_CRITERIA.WISH_OWNER, FLAGGED_WISH_SORT_CRITERIA.INTENTION, FLAGGED_WISH_SORT_CRITERIA.STATUS, FLAGGED_WISH_SORT_CRITERIA.CREATED_AT).optional().default(FLAGGED_WISH_SORT_CRITERIA.CREATED_AT),
					sortBy: Joi.number().valid(1, 0).optional(),
					searchKey: Joi.string().optional(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "PUT",
		path: `${SERVER.API_BASE_URL}/v1/admin/unflag-wish/{id}`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const params: AdminRequest.WishDetail = request.params;
				const result = await adminControllerV1.unflagWish(params, tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin flagged wish"],
			description: "Unflag wishes",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				params: Joi.object({
					id: Joi.string().required(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "PUT",
		path: `${SERVER.API_BASE_URL}/v1/admin/delete-wish/{id}`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const params: AdminRequest.WishDetail = request.params;
				const payload: AdminRequest.WishDetail = request.payload;
				const result = await adminControllerV1.deleteFlaggedWish({ ...params, ...payload }, tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin flagged wish"],
			description: "Delete wishes",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				params: Joi.object({
					id: Joi.string().required(),
				}),
				payload: Joi.object({
					deleteReason: Joi.string().required(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/admin/flagged-wish-reports`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const query: AdminRequest.FlaggedWish = request.query;
				const result = await adminControllerV1.flaggedWishReports(query);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin flagged wish"],
			description: "Flagged wish reports",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				query: Joi.object({
					wishId: Joi.string().required(),
					pageNo: Joi.number().required(),
					limit: Joi.number().required(),
					sortCriteria: Joi.string().trim().valid("createdAt").optional(),
					sortBy: Joi.number().valid(1, 0).optional(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/admin/users`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const query: AdminRequest.UserList = request.query;
				const result = await adminControllerV1.userListing(query);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin user"],
			description: "User listing",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				query: Joi.object({
					pageNo: Joi.number().required(),
					limit: Joi.number().required(),
					sortCriteria: Joi.string().trim().valid(USER_SORT_CRITERIA.NAME, USER_SORT_CRITERIA.STATUS, USER_SORT_CRITERIA.CREATED_AT).optional(),
					sortBy: Joi.number().valid(1, 0).optional(),
					searchKey: Joi.string().optional(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/admin/users/{id}`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const params: AdminRequest.UserDetail = request.params;
				const result = await adminControllerV1.userDetails(params);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin user"],
			description: "User details",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				params: Joi.object({
					id: Joi.string().required(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "PUT",
		path: `${SERVER.API_BASE_URL}/v1/admin/users/{id}`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const params: AdminRequest.EditUser = request.params;
				const payload: AdminRequest.EditUser = request.payload;
				const result = await adminControllerV1.editUser({ ...params, ...payload }, tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin user"],
			description: "Edit user",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				params: Joi.object({
					id: Joi.string().required(),
				}),
				payload: Joi.object({
					status: Joi.string().required().valid(STATUS.BLOCKED, STATUS.UN_BLOCKED, STATUS.DELETED),
					reason: Joi.string().optional().allow("")
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/admin/investors`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const query: AdminRequest.InvestorList = request.query;
				const result = await adminControllerV1.investorListing(query, tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin investor"],
			description: "Investor account listing",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				query: Joi.object({
					pageNo: Joi.number().required(),
					limit: Joi.number().required(),
					sortCriteria: Joi.string().trim().valid(INVESTOR_SORT_CRITERIA.NAME, INVESTOR_SORT_CRITERIA.EMAIL, INVESTOR_SORT_CRITERIA.USER_TYPE, INVESTOR_SORT_CRITERIA.STATUS, INVESTOR_SORT_CRITERIA.CREATED_AT, INVESTOR_SORT_CRITERIA.LAST_SEEN).optional(),
					sortBy: Joi.number().valid(1, 0).optional(),
					searchKey: Joi.string().optional(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/admin/investors`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const payload: AdminRequest.CreateInvestor = request.payload;
				const result = await adminControllerV1.createInvestor(payload);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin investor"],
			description: "Create investor",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
					firstName: Joi.string().required().trim(),
					lastName: Joi.string().required().trim(),
					email: Joi.string()
						.trim()
						.lowercase()
						.email({ minDomainSegments: 2 })
						.regex(REGEX.EMAIL)
						.default(SERVER.ADMIN_CREDENTIALS.EMAIL)
						.required(),
					contactNo: Joi.string().optional().trim().allow(""),
					company: Joi.string().optional().trim().allow(""),
					jobTitle: Joi.string().optional().trim().allow(""),
					profilePicture: Joi.string().optional().trim().allow("")
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/admin/investors/{id}`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const params: AdminRequest.InvestorDetail = request.params;
				const result = await adminControllerV1.investorDetails(params);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin investor"],
			description: "Investor details",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				params: Joi.object({
					id: Joi.string().required(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "PUT",
		path: `${SERVER.API_BASE_URL}/v1/admin/investors/{id}`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const params: AdminRequest.InvestorDetail = request.params;
				const payload: AdminRequest.EditInvestor = request.payload;
				const result = await adminControllerV1.editInvestor({ ...params, ...payload });
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin investor"],
			description: "Update investor details",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				params: Joi.object({
					id: Joi.string().required(),
				}),
				payload: Joi.object({
					firstName: Joi.string().required().trim(),
					lastName: Joi.string().required().trim(),
					email: Joi.string()
						.trim()
						.lowercase()
						.email({ minDomainSegments: 2 })
						.regex(REGEX.EMAIL)
						.default(SERVER.ADMIN_CREDENTIALS.EMAIL)
						.required(),
					contactNo: Joi.string().optional().trim().allow(""),
					company: Joi.string().optional().trim().allow(""),
					jobTitle: Joi.string().optional().trim().allow(""),
					profilePicture: Joi.string().optional().trim().allow("")
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "DELETE",
		path: `${SERVER.API_BASE_URL}/v1/admin/investors/{id}`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const params: AdminRequest.DeleteInvestor = request.params;
				const query: AdminRequest.DeleteInvestor = request.query;
				const result = await adminControllerV1.editInvestorStatus({ ...params, ...query });
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin investor"],
			description: "Activate/Deactivate/Delete investor",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				params: Joi.object({
					id: Joi.string().required(),
				}),
				query: Joi.object({
					status: Joi.string().required().valid(STATUS.BLOCKED, STATUS.UN_BLOCKED, STATUS.DELETED),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
];