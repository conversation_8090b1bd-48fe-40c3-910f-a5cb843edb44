{"compilerOptions": {"baseUrl": ".", "target": "ES6", "allowJs": false, "module": "commonjs", "removeComments": false, "emitDecoratorMetadata": true, "experimentalDecorators": true, "moduleResolution": "node", "pretty": true, "sourceMap": true, "resolveJsonModule": true, "outDir": "./build", "paths": {"@config/*": ["./src/config/*"], "@controllers/*": ["./src/controllers/*"], "@dao/*": ["./src/dao/*"], "@json/*": ["./src/json/*"], "@lib/*": ["./src/lib/*"], "@mappers/*": ["./src/mappers/*"], "@models/*": ["./src/models/*"], "@plugins/*": ["./src/plugins/*"], "@routes/*": ["./src/routes/*"], "@utils/*": ["./src/utils/*"], "@modules/*": ["./src/modules/*"]}, "types": ["node"]}, "exclude": ["node_modules", "src/lib/customTypings"]}