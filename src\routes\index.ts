"use strict";

/**
 * v1 routes
 */

// admin routes
import { adminRoute as adminRouteV1 } from "@modules/admin/v1/adminRoute";
// category routes
import { categoryRoute as categoryRouteV1 } from "@modules/category/v1/categoryRoute";
// common routes
import { commonRoute as commonRouteV1 } from "@modules/common/v1/commonRoute";
// content routes
import { contentRoute as contentRouteV1 } from "@modules/content/v1/contentRoute";
// friend routes
import { friendRoute as friendRouteV1 } from "@modules/friend/v1/friendRoute";

import { notificationRoute as notificationRouteV1 } from "@modules/notification/v1/notificationRoute";

import { userRoute as userRouteV1 } from "@modules/user/v1/userRoute";
// version routes
import { versionRoute as versionRouteV1 } from "@modules/version/v1/versionRoute";

import { roleRoute as roleRouteV1 } from "@modules/role/v1/roleRoute";
import { wishesRoute as wishesRouteV1 } from "@modules/wishes/v1/wishesRoute";
import { wishesRoute as wishesRouteV2 } from "@modules/wishes/v2/wishesRoute";
import { reportRoute as reportRouteV1 } from "@modules/reports/v1/reportRoute";
import { communitiesRoute as communitiesRouteV1 } from "@modules/community/v1/communitiesRoute";
import { communitiesRoute as communitiesRouteV2 } from "@modules/community/v2/communitiesRoute";
import { blessingRoute as blessingsRouteV1 } from "@modules/blessings/v1/blessingRoute";
import { gratitudesRoute as gratitudesRouteV1} from "@modules/gratitude/v1/gratitudeRoute";
import { wishesReminderRoute as wishesReminderRouterV1 } from "@modules/wishesReminder";
import { dailyReminderRoute as dailyReminderRouteV1 } from "@modules/dailyReminder";
import { disastersRoute as disastersRouteV1 } from "@modules/disasters/v1/disastersRoute";


export const routes: any = [

	...adminRouteV1,
	// ...categoryRouteV1,
	...commonRouteV1,
	...contentRouteV1,
	...friendRouteV1,
	...notificationRouteV1,

	...userRouteV1,
	// ...versionRouteV1,
	// ...roleRouteV1,
	...wishesRouteV1,
	...wishesRouteV2,
	...reportRouteV1,
	...communitiesRouteV1,
	...communitiesRouteV2,
	...blessingsRouteV1,
	...gratitudesRouteV1,
	...wishesReminderRouterV1,
	...dailyReminderRouteV1,
	...versionRouteV1,
	...disastersRouteV1

];
