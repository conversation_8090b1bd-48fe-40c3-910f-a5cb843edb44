"use strict";
const mongoose = require("mongoose");
import { Document, model, Model, Schema } from "mongoose";
import {
	DB_MODEL_REF,
} from "@config/index";
// import { userControllerV1 } from "@modules/user/index";
export interface IUser extends Document {
	userId: String;
	transactionId: String;
	amountCaptured: String;
	amountRefunded: String;
	currency: String;
	status: String;
	object: String;
	customerId: String;
	paymentMethod: String;
	receiptUrl: String;
	transactionType: String;
	webhookEvent: Object;
}


const paymentWebhookSchema: Schema = new mongoose.Schema({
	_id: { type: Schema.Types.ObjectId, required: true, auto: true },
	userId: {type: Schema.Types.ObjectId, required: false},
	transactionId: {type: String, required: true},
	amountCaptured: {type: String, required: true},
	amountRefunded: {type: String, required: true},
	currency: {type: String, required: true},
	object: {type: String, required: true},
	customerId: {type: String, required: true},
	paymentMethod: {type: String, required: true},
	receiptUrl: {type: String, required: true},
	transactionType: {type: String, required: true},
	status: {type: String, default: "INITIATE"},
	webhookEvent:{type: Object, required: false},
	created: { type: Number, default: Date.now }
}, {
	versionKey: false,
	timestamps: true
});

paymentWebhookSchema.index({ transactionId: 1 });

// Export user
export const payment_webhooks: Model<IUser> = model<IUser>(DB_MODEL_REF.PAYMET_WEBHOOK, paymentWebhookSchema);
