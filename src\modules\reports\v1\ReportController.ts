"use strict";
import { MESSAGES, STATUS,REPORT_TYPE, SQS_TYPES } from "@config/constant";
import { reportDaoV1 } from "@modules/reports/index";
import {userDaoV1} from "@modules/user/index"
import { wishesDaoV1 } from "@modules/wishes/index";
import { baseDao } from "@modules/baseDao/index";
import { communitiesDaoV1 } from "@modules/community";
import { blessingDaoV1 } from "@modules/blessings/index";
import { gratitudesDaoV1 } from "@modules/gratitude/index";
import { mailManager } from "@lib/MailManager";
import { awsSQS } from "@lib/AwsSqs";
export class ReportController {

    /**
	 * @function reportWish
	 * @description here are reporting the wishes.
	 *
	 */
	async reportWish(params: ReportRequest.ReportWish,tokenData:TokenData) {
		try {
			let step1 = await userDaoV1.findUserById(tokenData.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			let step2 = await wishesDaoV1.findWishById(params.subjectId); 
			if (!step2) return Promise.reject(MESSAGES.ERROR.WISH_NOT_FOUND);
			params.status = STATUS.REPORTED
			params.userId =  tokenData.userId
            params.type = REPORT_TYPE.WISH
			params.ownerId = step2.userId;
            let step3 = await baseDao.findOne("reports", { userId: params.userId, subjectId: params.subjectId, status:{'$ne':STATUS.ACTIVE}  });
            if(step3) return Promise.reject(MESSAGES.ERROR.WISH_ALREADY_REPORTED);
            await reportDaoV1.reportWish(params); // reported-wishes	
            await reportDaoV1.flagWish(params)  // update flag status and count in wishes 
			await reportDaoV1.reportPinnedWish(params, tokenData.userId); // update status of pinned wish
			await reportDaoV1.reportFavouriteWish(params, tokenData.userId); // update status of favourite wish
			await reportDaoV1.manageUserReportCount(params); // update wish report count in user table
			// mailManager.incidenReportdMail({type:REPORT_TYPE.WISH,subjectId:params.subjectId});
			awsSQS.signupMagicLinkProducer({  reportType:REPORT_TYPE.WISH, subjectId:params.subjectId, type:SQS_TYPES.REPORT_INCIDENT});

			return MESSAGES.SUCCESS.REPORT_WISH;
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function reportCommunity
	 * @description here are reporting communities
	 *
	 */
	  async reportCommunity(params: ReportRequest.ReportCommunity,tokenData:TokenData) {
		try {
			let step1 = await userDaoV1.findUserById(tokenData.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			let step2 = await communitiesDaoV1.findCommunityById(params.subjectId); 
			if (!step2) return Promise.reject(MESSAGES.ERROR.COMMUNITY_NOT_FOUND);
			params.status = STATUS.REPORTED
			params.userId =  tokenData.userId
            params.type = REPORT_TYPE.COMMUNITY
			params.ownerId = step2.userId;
            let step3 = await baseDao.findOne("reports", { userId: params.userId, subjectId: params.subjectId, status:{'$ne':STATUS.ACTIVE}  });
            if(step3) return Promise.reject(MESSAGES.ERROR.COMMUNITY_ALREADY_REPORTED);
            await reportDaoV1.reportWish(params); // reported-communities	
            await reportDaoV1.flagCommunity(params)  // update flag status and count in communities 
			await reportDaoV1.manageUserReportCount(params); // update community report count in user table
			//remove member from the community
			await baseDao.updateOne("members",{communityId:params.subjectId,userId:tokenData.userId},{status:STATUS.DELETED},{})
			// mailManager.incidenReportdMail({type:REPORT_TYPE.COMMUNITY,subjectId:params.subjectId});
			awsSQS.signupMagicLinkProducer({  reportType:REPORT_TYPE.COMMUNITY, subjectId:params.subjectId, type:SQS_TYPES.REPORT_INCIDENT});
			return MESSAGES.SUCCESS.REPORT_COMMUNITY;
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function report
	 * @description here are reporting blessings, wishes and community
	 *
	 */
	async report(params: ReportRequest.Report,tokenData:TokenData) {
		try {
			let step1 = await userDaoV1.findUserById(tokenData.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			params.status = STATUS.REPORTED
			let step2;
			if(params.type== REPORT_TYPE.WISH){
				step2 = await wishesDaoV1.findWishById(params.subjectId); 
			    if (!step2) return Promise.reject(MESSAGES.ERROR.WISH_NOT_FOUND);
			}else if(params.type == REPORT_TYPE.COMMUNITY){
				step2 = await communitiesDaoV1.findCommunityById(params.subjectId); 
			    if (!step2) return Promise.reject(MESSAGES.ERROR.COMMUNITY_NOT_FOUND);
			}else if(params.type == REPORT_TYPE.BLESSING){
				step2 = await blessingDaoV1.findBlessingById(params.subjectId); 
				if (!step2) return Promise.reject(MESSAGES.ERROR.BLESSING_NOT_FOUND);
			}else{
				step2 = await gratitudesDaoV1.findGratitudeById(params.subjectId); 
				if (!step2) return Promise.reject(MESSAGES.ERROR.GRATITUDE_NOT_FOUND);
			}
			params.userId =  tokenData.userId
			params.ownerId = step2.userId;
            let step3 = await baseDao.findOne("reports", { userId: params.userId, subjectId: params.subjectId, status:{'$ne':STATUS.ACTIVE}  });
            if(step3) return Promise.reject(MESSAGES.ERROR.ALREADY_REPORTED);
			if(params.type == REPORT_TYPE.WISH){
				await reportDaoV1.reportWish(params); // reported-wish	
				await reportDaoV1.flagWish(params)  // update flag status and count in wishes 
				await reportDaoV1.reportPinnedWish(params, tokenData.userId); // update status of pinned wish
				await reportDaoV1.reportFavouriteWish(params, tokenData.userId); // update status of favourite wish
			}else if(params.type == REPORT_TYPE.COMMUNITY){
				await reportDaoV1.reportWish(params); // reported-community	
				await reportDaoV1.flagCommunity(params)  // update flag status and count in communities 
				//remove member from the community
			    await baseDao.updateOne("members",{communityId:params.subjectId,userId:tokenData.userId},{status:STATUS.DELETED},{})
			
			}else if(params.type == REPORT_TYPE.BLESSING){
				if(step2.isGratitude== true){
					await reportDaoV1.reportWish(params,step2._id); // reported-blessing	
					await reportDaoV1.flagGratitude(params,step2.gratitudeId)  // update flag status and count in gratitude 
					await reportDaoV1.manageUserReportCount(params,step2.gratitudeId); // UPDATE WISH/BLESSING/COMMUNITY/GRATITUDE REPORT COUNT IN USERS TABLE
			        // mailManager.incidenReportdMail(params);
					params["type"] = SQS_TYPES.REPORT_INCIDENT;
					awsSQS.signupMagicLinkProducer(params);

			        return MESSAGES.SUCCESS.REPORT;
				}else{
					await reportDaoV1.reportWish(params); // reported-blessing	
					await reportDaoV1.flagBlessing(params)  // update flag status and count in blessing 
				}
			}else{
				await reportDaoV1.reportWish(params); // reported-blessing	
				await reportDaoV1.flagGratitude(params,params.subjectId)  // update flag status and count in gratitude 
			}
			await reportDaoV1.manageUserReportCount(params); // UPDATE WISH/BLESSING/COMMUNITY/GRATITUDE REPORT COUNT IN USERS TABLE
			// mailManager.incidenReportdMail(params);
			params["type"] = SQS_TYPES.REPORT_INCIDENT;
			awsSQS.signupMagicLinkProducer(params);

			return MESSAGES.SUCCESS.REPORT;
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function reportedPosts
	 * @description reported posts (wish, blessing, gratitude) byuser
	 *
	 */
	async reportedPosts(params: ReportRequest.ReportedPosts) {
		try {
			const step1 = await userDaoV1.findUserById(params.id, { _id: 1, firstName: 1, lastName: 1 });
			let step2 = await reportDaoV1.reportedPosts(params);
			step2["userDetails"] = step1;
			return MESSAGES.SUCCESS.REPORTED_POSTS(step2);
		} catch (error) {
			throw error;
		}
	}
}

export const reportController = new ReportController();