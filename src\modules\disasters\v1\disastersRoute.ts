"use strict";
import { Request, ResponseToolkit } from "@hapi/hapi";
import * as <PERSON><PERSON> from "joi";
import { responseHandler } from "@utils/ResponseHandler";

import {
	SWAGGER_DEFAULT_RESPONSE_MESSAGES,
	SERVER,
} from "@config/index";
import { authorizationHeaderObj } from "@utils/validator";
import { failActionFunction } from "@utils/appUtils";
import { disastersControllerV1 } from "@modules/disasters/index";

export const disastersRoute = [
    {
        method: "GET",
        path: `${SERVER.API_BASE_URL}/v1/disasters/categories`,
        handler: async (request: Request | any, h: ResponseToolkit) => {
            try {
            const query: ListingRequest = request.query;
            const result = await disastersControllerV1.categoriesSearch(query);
            return responseHandler.sendSuccess(h, result);
            } catch (error) {
            return responseHandler.sendError(request, error);
            }
        },
        options: {
            tags: ["api", "disasters"],
            description: "disaster categories list api",
            auth: {
            strategies: ["CommonAuth"],
            },
            validate: {
            headers: authorizationHeaderObj,
            query: Joi.object({
                pageNo: Joi.number().optional().description("Page no"),
                limit: Joi.number().optional().description("limit"),
                searchKey: Joi.string()
                .optional()
                .description("Search by disaster name"),
                
            }),
            failAction: failActionFunction,
            },
            plugins: {
            "hapi-swagger": {
                responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES,
            },
            },
        },
    },
];