const cron = require("node-cron");
const request = require("request");
import { BaseDao, baseDao } from "@modules/baseDao/BaseDao";
import { commonDao } from "@modules/common/v1/CommonDao";

import * as config from "@config/index";

const baseUrl = config.SERVER.APP_URL + config.SERVER.API_BASE_URL;
let task;

export class CronUtils {

	constructor() { }

	static init() {
		// this will execute on the server time at 00:00:00 each day by server time
		task = cron.schedule("0 0 * * *", async function () {
			console.log("this will execute on the server time at 00:00:00 each day by server time");

			const findUsers = await baseDao.find("users", { lastBlessingTime: { $exists: true }, streakCount: { $ne: 0 } }, { _id: 1, lastBlessingTime: 1, streakCount: 1 });
			// console.log(findUsers)
			const previousDate = new Date(new Date().setDate(new Date().getDate() - 1)).toISOString().slice(0, 10);
			await Promise.all(findUsers.map(async user => {
				const lastBlessingDate = user.lastBlessingTime.toISOString().slice(0,10);
				if(lastBlessingDate < previousDate) {
					await baseDao.updateOne("users", { _id: user._id }, { streakCount: 0 }, {});
				}
			}));
		}, { scheduled: false });
		task.start();
	}
}