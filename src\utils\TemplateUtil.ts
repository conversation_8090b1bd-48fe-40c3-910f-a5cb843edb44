"use strict";

import * as	fs from "fs";
import * as handlebars from "handlebars";

import { SERVER } from "@config/environment";

handlebars.registerHelper("hasName", function (value) {
	return value;
});

export class TemplateUtil {

	private template: string;

	constructor(template) {
		this.template = template;
	}

	compileFile(complieData: Object) {
		return new Promise((resolve, reject) => {
			complieData["year"] = new Date().getFullYear();
			complieData["appLogo"] = SERVER.APP_LOGO;
			complieData["linkedInUrl"] = SERVER.LINKED_IN_URL;
			complieData["instagramUrl"] = SERVER.INSTAGRAM_URL;
			complieData["facebookUrl"] = SERVER.FACEBOOK_URL;
			complieData["youtubeUrl"] = SERVER.YOUTUBE_URL;
			complieData["tiktokUrl"] = SERVER.TIKTOK_URL;
			fs.readFile(this.template, "utf8", (error, content) => {
				if (error)
					reject(error);
				try {
					const template = handlebars.compile(content);
					let html = template(complieData);
					resolve(html);
				} catch (error) {
					reject(error);
				}
			});
		});
	}
}