"use strict";
import * as _ from "lodash";
import * as crypto from "crypto";
import * as mongoose from "mongoose";
import * as promise from "bluebird";
import {
	buildToken,
	toObjectId,
	encryptHashPassword,
	genRandomString,
	matchPassword
} from "@utils/appUtils";
import {
	JOB_SCHEDULER_TYPE,
	MESSAGES,
	STATUS,
	TOKEN_TYPE,
	SERVER,
	USER_TYPE,
	UPDATE_TYPE,
	DEEPLINK_TYPE,
	ACTION_TYPE,
	REGISTRATION_FLAG,
	WISH_TYPE,
	LOGIN_TYPE,
	HOME_LIST,
	WISH_TAG_TYPE,
	REPORT_TYPE,
	THANK_WISHWELL_LISTING_TYPE,
	SQS_TYPES,
	CONTENT_TYPE,
	INVITATION_TYPE,
} from "@config/index";
import { userDaoV1 } from "@modules/user/index"
import { baseDao } from "@modules/baseDao/index";
import { loginHistoryDao } from "@modules/loginHistory/index";
import { redisClient } from "@lib/redis/RedisClient";
import { sendMessageToFlock } from "@utils/FlockUtils";
import { createToken } from "@lib/tokenManager";
import { mailManager } from "@lib/MailManager";
import { wishesDaoV1 } from "@modules/wishes/index";
import {commonControllerV1 } from "@modules/common/index";
import { commonDao } from "@modules/common/v1/CommonDao";
import { blessingDaoV1 } from "@modules/blessings";
import { reportDaoV1 } from "@modules/reports/index";
import { notificationManager } from "@utils/NotificationManager";
import { awsSQS } from "@lib/AwsSqs";
const stripe = require('stripe')(SERVER.STRIPE_SECRETE);

const libphonenumber = require('google-libphonenumber');
const phoneUtil = libphonenumber.PhoneNumberUtil.getInstance();
const PNF = libphonenumber.PhoneNumberFormat;
export class UserController {
	async removeSession(params, isSingleSession: boolean) {
		try {
			if (isSingleSession)
				await loginHistoryDao.removeDeviceById({ "userId": params.userId });
			else
				await loginHistoryDao.removeDeviceById({ "userId": params.userId, "deviceId": params.deviceId });

			if (SERVER.IS_REDIS_ENABLE) {
				if (isSingleSession) {
					let keys: any = await redisClient.getKeys(`*${params.userId}*`);
					keys = keys.filter(v1 => Object.values(JOB_SCHEDULER_TYPE).findIndex(v2 => v2 === v1.split(".")[0]) === -1);
					if (keys.length) await redisClient.deleteKey(keys);
				} else
					await redisClient.deleteKey(`${params.userId}.${params.deviceId}`);
			}
		} catch (error) {
			sendMessageToFlock({ "title": "_removeSession", "error": error.stack });
		}
	};
	/**
	 * @function updateUserDataInRedis
	 */
	async updateUserDataInRedis(params, isAlreadySaved = false) {
		try {
			delete params.salt;
			if (SERVER.IS_REDIS_ENABLE) {
				let keys: any = await redisClient.getKeys(`*${params.userId || params._id.toString()}*`);
				keys = keys.filter(v1 => Object.values(JOB_SCHEDULER_TYPE).findIndex(v2 => v2 === v1.split(".")[0]) === -1);
				const promiseResult = [], array = [];
				for (let i = 0; i < keys.length; i++) {
					if (isAlreadySaved) {
						let userData: any = await redisClient.getValue(`${params.userId || params._id.toString()}.${keys[i].split(".")[1]}`);
						array.push(keys[i]);
						array.push(JSON.stringify(buildToken(_.extend(JSON.parse(userData), params))));
						promiseResult.push(userData);
					} else {
						array.push(keys[i]);
						array.push(JSON.stringify(buildToken(params)));
					}
				}
				await Promise.all(promiseResult);
				if (array.length) redisClient.mset(array);
			}
			return {};
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function updateUserDataInDb
	 */
	async updateUserDataInDb(params) {
		try {
			await baseDao.updateMany("login_histories", { "userId._id": params._id }, { "$set": { userId: params } }, {});
			if (params) {
				let update = {};
				if (params.firstName || params.profilePicture) {
				  update["userDetail.firstName"] = params.firstName;
				  update["userDetail.lastName"] = params.lastName;
				  update["userDetail.profilePicture"] = params.profilePicture;
					}
				await baseDao.updateMany("wishes",{ "userId": params._id },update,{});
			}
			return {};
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function signUp
	 * @description signup of user using email verification
	 * @param params.email: user's email (required)
	 */
	async signUpV2(params: UserRequest.SignUp) {
		// MongoDB transactions
		const session = await mongoose.startSession();
		session.startTransaction();
		try {
			params.userType = USER_TYPE.USER 
			const isExist = await userDaoV1.isEmailExists(params); // to check is email already exists or not
			let step = await userDaoV1.deleteEmailCheck(params);
			if(step){
				if(step.status == STATUS.DELETED && step.isDeletedBy == USER_TYPE.ADMIN) return Promise.reject(MESSAGES.ERROR.USER_DELETED_BY_ADMIN);
				if(step.status == STATUS.BLOCKED) {
					let reason = step?.actionSummary.slice().reverse().find(obj => obj.status == STATUS.BLOCKED).reason.trim();
					reason = reason.length ? ` Reason: ${reason}` : "";
					return Promise.reject(MESSAGES.ERROR.USER_DEACTIVATED_BY_ADMIN(reason));
				}
			}
			let step1
			if((isExist && isExist.isEmailVerified && isExist.isEmailVerified == true && isExist.isPasswordSet && isExist.isPasswordSet == true && isExist.loginType == LOGIN_TYPE.NORMAL) || (isExist && isExist.isEmailVerified && isExist.isEmailVerified == true && isExist.loginType !== LOGIN_TYPE.NORMAL)) {
				return Promise.reject(MESSAGES.ERROR.EMAIL_ALREADY_EXIST);
			}
			if(!isExist || (isExist && !isExist.isEmailVerified)) {
				if(!isExist) step1 = await userDaoV1.signUp(params, session);
				if (SERVER.IS_REDIS_ENABLE)
				    redisClient.setExp(
				      params.email,
					  SERVER.TOKEN_INFO.EXPIRATION_TIME.VERIFY_USER_EMAIL / 1000,
					  JSON.stringify({ email: params.email })
					);
				const salt = crypto.randomBytes(64).toString("hex");
				const tokenData = {
					"userId": step1? step1._id:isExist._id,
					"deviceId": params.deviceId,
					"accessTokenKey": salt,
					"type": TOKEN_TYPE.USER_LOGIN,
					"userType": USER_TYPE.USER
				};
				await baseDao.updateMany("login_histories", { 'deviceToken': params.deviceToken, 'isLogin': true }, { 'arn': "", isLogin:false }, {});
				const [step2, accessToken] = await promise.join(
					loginHistoryDao.createUserLoginHistory({ ...(step1? step1:isExist), ...params, salt }),
					createToken(tokenData)
				);
				const deeplink = `${SERVER.APP_URL}${SERVER.API_BASE_URL}/v1/common/deeplink?type=${DEEPLINK_TYPE.VERIFY_USER_EMAIL}&email=${params.email}&token=${accessToken}`;
				if (SERVER.IS_REDIS_ENABLE) await redisClient.setExp(params.email+"_"+DEEPLINK_TYPE.VERIFY_USER_EMAIL, (SERVER.TOKEN_INFO.EXPIRATION_TIME.VERIFY_USER_EMAIL / 1000), JSON.stringify({ "email": params.email, "token": accessToken }));
				awsSQS.signupMagicLinkProducer({
					message: "Verify User Email",
					email: params.email,
					deeplink: deeplink,
					type: SQS_TYPES.VERIFY_USER_EMAIL
				});
				// await mailManager.verifyUserEmail({
				// 	"email": params.email,
				// 	"deeplink": deeplink,
				// });
				if (SERVER.IS_REDIS_ENABLE) redisClient.setExp(`${(step1? step1._id:isExist._id).toString()}.${params.deviceId}`, Math.floor(SERVER.TOKEN_INFO.EXPIRATION_TIME[TOKEN_TYPE.USER_LOGIN] / 1000), JSON.stringify(buildToken({ ...step1, ...params, salt })));
				// MongoDB transactions
				await session.commitTransaction();
				session.endSession();
				await notificationManager.subscribeDeviceTokenViaAWS({token: params.deviceToken, userId: step1? step1._id:isExist._id});
				//check contact sync with deviceId
				// let deviceContact = await baseDao.find("contacts",{userId: step1? step1._id:isExist._id,deviceId:params.deviceId},{_id:1,userId:1,deviceId:1},{});
				let deviceContact = await baseDao.find("contacts_v2",{userId: step1? step1._id:isExist._id,deviceId:params.deviceId},{_id:1,userId:1,deviceId:1},{});
				return MESSAGES.SUCCESS.SIGNUP({
					accessToken: "",
					_id: step1? step1._id:isExist._id, 
					email: step1? step1.email:isExist.email,
					"firstName": step1?step1.firstName:isExist.firstName,
					"lastName": step1?step1.lastName:isExist.lastName,
					//"isContactSynced": step1? step1.isContactSynced:isExist.isContactSynced,
					"isContactSynced":(deviceContact && deviceContact.length)?true:false,
					"loginType":LOGIN_TYPE.NORMAL,
					"profilePicture":step1?step1.profilePicture:isExist.profilePicture,
					"socialData":step1?step1.socialData:isExist.socialData,
					"pushNotificationStatus":step1?step1.pushNotificationStatus:isExist.pushNotificationStatus,
					"communityNotificationStatus": step1?step1.communityNotificationStatus:isExist.communityNotificationStatus,
					"wishesNotificationStatus":step1?step1.wishesNotificationStatus:isExist.wishesNotificationStatus,
					"gratitudeNotificationStatus":step1?step1.gratitudeNotificationStatus:isExist.gratitudeNotificationStatus,
					"perferMedidation":step1?step1.perferMedidation:isExist.perferMedidation, 
					"preferPrayer":step1?step1.preferPrayer:isExist.preferPrayer,
					"locationSharing":step1?step1.locationSharing:isExist.locationSharing,
					"offerBlessing":step1?step1.offerBlessing:isExist.offerBlessing,
					"countryFlagCode":step1?step1.countryFlagCode:isExist.countryFlagCode,
					"intension":step1?step1.intension:isExist.intension,
					"completeTooltip":step1?step1.completeTooltip:isExist.completeTooltip,
					"isEmailVerified": step1?step1.isEmailVerified:isExist.isEmailVerified,
					"isPasswordSet": step1?step1.isPasswordSet:isExist.isPasswordSet,
					"isProfileComplete": step1?step1.isProfileComplete:isExist.isProfileComplete
				});
			} else {
				await this.removeSession({ "userId": isExist._id, "deviceId": params.deviceId }, true);
				const salt = crypto.randomBytes(64).toString("hex");
				const tokenData = {
					"userId": isExist._id,
					"deviceId": params.deviceId,
					"accessTokenKey": salt,
					"type": TOKEN_TYPE.USER_LOGIN,
					"userType": USER_TYPE.USER
				};
				await baseDao.updateMany("login_histories", { 'deviceToken': params.deviceToken, 'isLogin': true }, { 'arn': "", isLogin:false }, {});
				const [step2, accessToken] = await promise.join(
					loginHistoryDao.createUserLoginHistory({ ...isExist, ...params, salt }),
					createToken(tokenData)
				);
				if (SERVER.IS_REDIS_ENABLE) redisClient.setExp(`${isExist._id.toString()}.${params.deviceId}`, Math.floor(SERVER.TOKEN_INFO.EXPIRATION_TIME[TOKEN_TYPE.USER_LOGIN] / 1000), JSON.stringify(buildToken({ ...isExist, ...params, salt })));
				// MongoDB transactions
				await session.commitTransaction();
				session.endSession();
				await notificationManager.subscribeDeviceTokenViaAWS({token: params.deviceToken, userId: step1? step1._id:isExist._id});
				//check contact sync with deviceId
				// let deviceContact = await baseDao.find("contacts",{userId: step1? step1._id:isExist._id,deviceId:params.deviceId},{_id:1,userId:1,deviceId:1},{});
				let deviceContact = await baseDao.find("contacts_v2",{userId: step1? step1._id:isExist._id,deviceId:params.deviceId},{_id:1,userId:1,deviceId:1},{});
				return MESSAGES.SUCCESS.SIGNUP({
					accessToken,
					_id: isExist._id,
					email: isExist.email,
					"firstName": isExist?.firstName,
					"lastName": isExist?.lastName,
					//"isContactSynced": isExist?.isContactSynced,
					"isContactSynced":(deviceContact && deviceContact.length)?true:false,
					"loginType":LOGIN_TYPE.NORMAL,
					"profilePicture":isExist?.profilePicture,
					"socialData":isExist?.socialData,
					"pushNotificationStatus":isExist?.pushNotificationStatus,
					"communityNotificationStatus": isExist?.communityNotificationStatus,
					"wishesNotificationStatus":isExist?.wishesNotificationStatus,
					"gratitudeNotificationStatus":isExist?.gratitudeNotificationStatus,
					"perferMedidation":isExist?.perferMedidation, 
					"preferPrayer":isExist?.preferPrayer,
					"locationSharing":isExist?.locationSharing, 
					"offerBlessing":isExist?.offerBlessing,
					"countryFlagCode":isExist?.countryFlagCode,
					"intension":isExist?.intension,
					"completeTooltip":isExist?.completeTooltip,
					"isEmailVerified":isExist?.isEmailVerified,
					"isPasswordSet":isExist?.isPasswordSet,
					"isProfileComplete": isExist?.isProfileComplete
				});
			}
		} catch (error) {
			// MongoDB transactions
			console.log(error);
			await session.abortTransaction();
			session.endSession();
			throw error;
		}
	}
	/**
	 * @function signUp
	 * @description signup of user using magic link
	 * @param params.email: user's email (required)
	 */
	async signUpV1(params: UserRequest.SignUp) {
		// MongoDB transactions
		const session = await mongoose.startSession();
		session.startTransaction();
		try {
			params.userType = USER_TYPE.USER 
			const isExist = await userDaoV1.isEmailExists(params); // to check is email already exists or not
			let step = await userDaoV1.deleteEmailCheck(params);
			if(step){
				if(step.status == STATUS.DELETED && step.isDeletedBy == USER_TYPE.ADMIN) return Promise.reject(MESSAGES.ERROR.USER_DELETED_BY_ADMIN);
				if(step.status == STATUS.BLOCKED) {
					let reason = step?.actionSummary.slice().reverse().find(obj => obj.status == STATUS.BLOCKED).reason.trim();
					reason = reason.length ? ` Reason: ${reason}` : "";
					return Promise.reject(MESSAGES.ERROR.USER_DEACTIVATED_BY_ADMIN(reason));
				}
			}
			let step1
			if(!isExist || (isExist && !(isExist.firstName || isExist.phoneNumber))){
				if(!isExist) step1 = await userDaoV1.signUp(params, session);
				const deeplink = `${SERVER.APP_URL}${SERVER.API_BASE_URL}/v1/common/deeplink?type=${DEEPLINK_TYPE.MAGIC_LINK}&email=${params.email}`;  // deeplinking
				// mailManager.magicLinkSignUp({
				// 	"email": params.email,
				// 	//"link": link,
				// 	"deeplink": deeplink,
				// });
				awsSQS.signupMagicLinkProducer({
					message: "Magic Link Signup",
					email: params.email,
					deeplink: deeplink,
					type: SQS_TYPES.MAGIC_LINK
				});
				if (SERVER.IS_REDIS_ENABLE)
				    redisClient.setExp(
				      params.email,
					  SERVER.TOKEN_INFO.EXPIRATION_TIME.MAGIC_LINK / 1000,
					  JSON.stringify({ email: params.email })
					);
				const salt = crypto.randomBytes(64).toString("hex");
				const tokenData = {
					"userId": step1? step1._id:isExist._id,
					"deviceId": params.deviceId,
					 "accessTokenKey": salt,
					"type": TOKEN_TYPE.USER_LOGIN,
					"userType": USER_TYPE.USER
				};
				//let accessToken = await createToken(tokenData);
				await baseDao.updateMany("login_histories", { 'deviceToken': params.deviceToken, 'isLogin': true }, { 'arn': "", isLogin:false }, {});
				const [step2, accessToken] = await promise.join(
					loginHistoryDao.createUserLoginHistory({ ...(step1? step1:isExist), ...params, salt }),
					createToken(tokenData)
				);
				if (SERVER.IS_REDIS_ENABLE) redisClient.setExp(`${(step1? step1._id:isExist._id).toString()}.${params.deviceId}`, Math.floor(SERVER.TOKEN_INFO.EXPIRATION_TIME[TOKEN_TYPE.USER_LOGIN] / 1000), JSON.stringify(buildToken({ ...step1, ...params, salt })));
				// MongoDB transactions
				await session.commitTransaction();
				session.endSession();
				await notificationManager.subscribeDeviceTokenViaAWS({token: params.deviceToken, userId: step1? step1._id:isExist._id});
				//check contact sync with deviceId
				let deviceContact = await baseDao.find("contacts",{userId: step1? step1._id:isExist._id,deviceId:params.deviceId},{_id:1,userId:1,deviceId:1},{});
				return MESSAGES.SUCCESS.SIGNUP({
					accessToken,
					_id: step1? step1._id:isExist._id, 
					email: step1? step1.email:isExist.email,
					"firstName": step1?step1.firstName:isExist.firstName,
					"lastName": step1?step1.lastName:isExist.lastName,
					//"isContactSynced": step1? step1.isContactSynced:isExist.isContactSynced,
					"isContactSynced":(deviceContact && deviceContact.length)?true:false,
					"loginType":LOGIN_TYPE.NORMAL,
					"profilePicture":step1?step1.profilePicture:isExist.profilePicture,
					"socialData":step1?step1.socialData:isExist.socialData,
					"pushNotificationStatus":step1?step1.pushNotificationStatus:isExist.pushNotificationStatus,
					"communityNotificationStatus": step1?step1.communityNotificationStatus:isExist.communityNotificationStatus,
					"wishesNotificationStatus":step1?step1.wishesNotificationStatus:isExist.wishesNotificationStatus,
					"gratitudeNotificationStatus":step1?step1.gratitudeNotificationStatus:isExist.gratitudeNotificationStatus,
					"perferMedidation":step1?step1.perferMedidation:isExist.perferMedidation, 
					"preferPrayer":step1?step1.preferPrayer:isExist.preferPrayer,
					"locationSharing":step1?step1.locationSharing:isExist.locationSharing,
					"offerBlessing":step1?step1.offerBlessing:isExist.offerBlessing,
					"countryFlagCode":step1?step1.countryFlagCode:isExist.countryFlagCode,
					"intension":step1?step1.intension:isExist.intension,
					"completeTooltip":step1?step1.completeTooltip:isExist.completeTooltip,
				});
			}else{
				await this.removeSession({ "userId": isExist._id, "deviceId": params.deviceId }, true);
				const salt = crypto.randomBytes(64).toString("hex");
				const tokenData = {
					"userId": isExist._id,
					"deviceId": params.deviceId,
					"accessTokenKey": salt,
					"type": TOKEN_TYPE.USER_LOGIN,
					"userType": USER_TYPE.USER
				};
				await baseDao.updateMany("login_histories", { 'deviceToken': params.deviceToken, 'isLogin': true }, { 'arn': "", isLogin:false }, {});
				const [step2, accessToken] = await promise.join(
					loginHistoryDao.createUserLoginHistory({ ...isExist, ...params, salt }),
					createToken(tokenData)
				);
				if (SERVER.IS_REDIS_ENABLE) redisClient.setExp(`${isExist._id.toString()}.${params.deviceId}`, Math.floor(SERVER.TOKEN_INFO.EXPIRATION_TIME[TOKEN_TYPE.USER_LOGIN] / 1000), JSON.stringify(buildToken({ ...isExist, ...params, salt })));
				const deeplink = `${SERVER.APP_URL}${SERVER.API_BASE_URL}/v1/common/deeplink?type=${DEEPLINK_TYPE.MAGIC_LINK}&email=${params.email}`;  // deeplinking
				// mailManager.magicLinkSignIn({
				// 	"email": params.email,
				// 	//"link": link,
				// 	"deeplink": deeplink,
				//   });
				awsSQS.signupMagicLinkProducer({
					message: "Magic Link Signup",
					email: params.email,
					deeplink: deeplink,
					type: SQS_TYPES.MAGIC_LINK
				});
				  // MongoDB transactions
				await session.commitTransaction();
				session.endSession();
				await notificationManager.subscribeDeviceTokenViaAWS({token: params.deviceToken, userId: step1? step1._id:isExist._id});
				//check contact sync with deviceId
				let deviceContact = await baseDao.find("contacts",{userId: step1? step1._id:isExist._id,deviceId:params.deviceId},{_id:1,userId:1,deviceId:1},{});
				
				return MESSAGES.SUCCESS.LOGIN({
					accessToken,
					_id: isExist._id,
					email: isExist.email,
					"firstName": isExist?.firstName,
					"lastName": isExist?.lastName,
					//"isContactSynced": isExist?.isContactSynced,
					"isContactSynced":(deviceContact && deviceContact.length)?true:false,
					"loginType":LOGIN_TYPE.NORMAL,
					"profilePicture":isExist?.profilePicture,
					"socialData":isExist?.socialData,
					"pushNotificationStatus":isExist?.pushNotificationStatus,
					"communityNotificationStatus": isExist?.communityNotificationStatus,
					"wishesNotificationStatus":isExist?.wishesNotificationStatus,
					"gratitudeNotificationStatus":isExist?.gratitudeNotificationStatus,
					"perferMedidation":isExist?.perferMedidation, 
					"preferPrayer":isExist?.preferPrayer,
					"locationSharing":isExist?.locationSharing, 
					"offerBlessing":isExist?.offerBlessing,
					"countryFlagCode":isExist?.countryFlagCode,
					"intension":isExist?.intension,
					"completeTooltip":isExist?.completeTooltip,
				});
			}
		} catch (error) {
			// MongoDB transactions
			console.log(error);
			await session.abortTransaction();
			session.endSession();
			throw error;
		}
	}
	/**
	* @function socialSignup
	* @description signup and login from user's social account (gmail or facebook)
	*/
	async socialSignup(params: UserRequest.SignUp) {
		try {
			//social id contain email from request or not
			//social id in DB + email contain with social id
			
			params.isEmailVerified = true;
			params.userType = USER_TYPE.USER;
			let isExist = false;
			let isEmailExist;
			let checkIfDeletedUser;
			let checkSocialIdExists;
			if(!params.email){
				if(params.loginType==LOGIN_TYPE.GOOGLE){
					checkSocialIdExists = await userDaoV1.findOne("users",{"googleSocialId":params.socialId, "status": STATUS.UN_BLOCKED})
				}
				if(params.loginType==LOGIN_TYPE.APPLE){
					checkSocialIdExists = await userDaoV1.findOne("users",{"appleSocialId":params.socialId, "status": STATUS.UN_BLOCKED})
				}
				if(!checkSocialIdExists || (checkSocialIdExists && (!checkSocialIdExists.email))) return Promise.reject(MESSAGES.ERROR.EMAIL_REQUIRED);
			}
			if(params.email) {
				isEmailExist = await userDaoV1.isEmailExists(params); // to check is email already exists or not
				if(isEmailExist) isExist = true;
				checkIfDeletedUser = await userDaoV1.deleteEmailCheck(params);
			} else {
				checkIfDeletedUser = await userDaoV1.deleteEmailCheckSocial(params);
			}
			if(checkIfDeletedUser){
				if(checkIfDeletedUser.status == STATUS.DELETED && checkIfDeletedUser.isDeletedBy == USER_TYPE.ADMIN) return Promise.reject(MESSAGES.ERROR.USER_DELETED_BY_ADMIN);
				if(checkIfDeletedUser.status == STATUS.BLOCKED) {
					let reason = checkIfDeletedUser?.actionSummary.slice().reverse().find(obj => obj.status == STATUS.BLOCKED).reason.trim();
					reason = reason.length ? ` Reason: ${reason}` : "";
					return Promise.reject(MESSAGES.ERROR.USER_DEACTIVATED_BY_ADMIN(reason));
				}
			}
			if (isExist) {
				let step1 = await userDaoV1.updateSocialData(params, isEmailExist);
				console.log(step1,'step1')
				const salt = crypto.randomBytes(64).toString("hex");
				const tokenData = {
					"userId": step1._id,
					"deviceId": params.deviceId,
					"accessTokenKey": salt,
					"type": TOKEN_TYPE.USER_LOGIN,
					"userType": USER_TYPE.USER
				};
				await this.removeSession({ "userId": step1._id, "deviceId": params.deviceId }, true);
				await baseDao.updateMany("login_histories", { 'deviceToken': params.deviceToken, 'isLogin': true }, { 'arn': "", isLogin:false }, {});
				const [step2, accessToken] = await promise.join(
					loginHistoryDao.createUserLoginHistory({ ...step1, ...params, salt }),
					createToken(tokenData)
				);
				console.log(`${step1._id.toString()}.${params.deviceId}`,'${step1._id.toString()}.${params.deviceId}')
				if (SERVER.IS_REDIS_ENABLE) redisClient.setExp(`${step1._id.toString()}.${params.deviceId}`, Math.floor(SERVER.TOKEN_INFO.EXPIRATION_TIME[TOKEN_TYPE.USER_LOGIN] / 1000), JSON.stringify(buildToken({ ...step1, ...params, salt })));
				await notificationManager.subscribeDeviceTokenViaAWS({token: params.deviceToken, userId: step1? step1._id:isEmailExist._id});
				//check contact sync with deviceId
				let deviceContact = await baseDao.find("contacts",{userId: step1? step1._id:isEmailExist._id,deviceId:params.deviceId},{_id:1,userId:1,deviceId:1},{});
				
				return MESSAGES.SUCCESS.LOGIN({
					accessToken,
					"_id": step1._id,
					"name":step1?step1.name:"",
					"firstName": step1?.firstName,
					"lastName": step1?.lastName,
					"email": step1.email,
					"profilePicture": step1?.profilePicture,
					"phoneNumber": step1?.phoneNumber,
					//"isContactSynced": step1.isContactSynced,
					"isContactSynced":(deviceContact && deviceContact.length)?true:false,
					"socialData": step1.socialData?step1.socialData:"",
					"loginType":params.loginType,
					"pushNotificationStatus":step1?.pushNotificationStatus,
					"communityNotificationStatus": step1?.communityNotificationStatus,
					"wishesNotificationStatus":step1?.wishesNotificationStatus,
					"gratitudeNotificationStatus":step1?.gratitudeNotificationStatus,
					"perferMedidation":step1?.perferMedidation, 
					"preferPrayer":step1?.preferPrayer,
					"locationSharing":step1?.locationSharing, 
					"offerBlessing":step1?.offerBlessing,
					"countryFlagCode":step1?.countryFlagCode,
					"intension":step1?.intension,
					"completeTooltip":step1?.completeTooltip,
					"isEmailVerified":step1?.isEmailVerified,
					"isPasswordSet":step1?.isPasswordSet,
					"isProfileComplete": step1?.isProfileComplete
				});
			}
			else {
				const isSocialIdExists = await userDaoV1.isSocialIdExists(params);
				let step1;
				if (isSocialIdExists) {
					// if(isSocialIdExists.email) {
						step1 = await userDaoV1.updateSocialData(params, isSocialIdExists); ///let it be
					// } else {
					// 	return Promise.reject(MESSAGES.ERROR.EMAIL_REQUIRED);
					// }
				}
				else {
					    const session = await mongoose.startSession();
    					session.startTransaction();				
	    				params['socialData.socialId'] = params.socialId;
		    	        params['socialData.email'] = params.email?params.email:"";
			    		if (params.name) params['socialData.name'] = params.name;
			            if (params.profilePicture) params['socialData.profilePicture'] = params.profilePicture;
						if (params.name) params['firstName'] = params.name;
						if(params.loginType==LOGIN_TYPE.APPLE) params['appleSocialId'] = params.socialId;
						if(params.loginType==LOGIN_TYPE.GOOGLE) params['googleSocialId'] = params.socialId;
		
    					let step1 = await userDaoV1.signUp(params, session);
    					const salt = crypto.randomBytes(64).toString("hex");
    				    const tokenData = {
    					    "userId": step1._id,
    	    				"deviceId": params.deviceId,
     		    			"accessTokenKey": salt,
    			    		"type": TOKEN_TYPE.USER_LOGIN,
    				    	"userType": USER_TYPE.USER
        				};
						await baseDao.updateMany("login_histories", { 'deviceToken': params.deviceToken, 'isLogin': true }, { 'arn': "", isLogin:false }, {});
		        		const [step2, accessToken] = await promise.join(
		    			loginHistoryDao.createUserLoginHistory({ ...step1, ...params, salt }),
		    			createToken(tokenData)
                        );
						if (SERVER.IS_REDIS_ENABLE) redisClient.setExp(`${step1._id.toString()}.${params.deviceId}`, Math.floor(SERVER.TOKEN_INFO.EXPIRATION_TIME[TOKEN_TYPE.USER_LOGIN] / 1000), JSON.stringify(buildToken({ ...step1, ...params, salt })));
	    				// MongoDB transactions
	    			    await session.commitTransaction();
	    			    session.endSession(); 
						await notificationManager.subscribeDeviceTokenViaAWS({token: params.deviceToken, userId: step1? step1._id:isSocialIdExists._id});
						//check contact sync with deviceId
				        let deviceContact = await baseDao.find("contacts",{userId: step1? step1._id:isSocialIdExists._id,deviceId:params.deviceId},{_id:1,userId:1,deviceId:1},{});
				
    					return MESSAGES.SUCCESS.SIGNUP({
	    					accessToken,
		    				"_id": step1._id,
							"name":step1.name?step1.name:"",
				        	"firstName": step1?.firstName,
					        "lastName": step1?.lastName,
					        "email": step1.email,
					        "profilePicture": step1?.profilePicture,
					        "phoneNumber": step1?.phoneNumber,
					       // "isContactSynced": step1.isContactSynced,
						   "isContactSynced":(deviceContact && deviceContact.length)?true:false,
					        "socialData": step1.socialData?step1.socialData:"",
							"loginType":params.loginType,
							"pushNotificationStatus":step1.pushNotificationStatus,
							"communityNotificationStatus": step1?.communityNotificationStatus,
							"wishesNotificationStatus":step1?.wishesNotificationStatus,
							"gratitudeNotificationStatus":step1?.gratitudeNotificationStatus,
							"perferMedidation":step1?.perferMedidation, 
							"preferPrayer":step1?.preferPrayer, 
							"locationSharing":step1?.locationSharing, 
							"offerBlessing":step1?.offerBlessing,
							"countryFlagCode":step1?.countryFlagCode,
							"intension":step1?.intension,
							"completeTooltip":step1?.completeTooltip,
							"isEmailVerified":step1?.isEmailVerified,
							"isPasswordSet":step1?.isPasswordSet,
							"isProfileComplete": step1?.isProfileComplete
				    	});
				}
				await this.removeSession({ "userId": step1._id, "deviceId": params.deviceId }, true);
				const salt = crypto.randomBytes(64).toString("hex");
				const tokenData = {
					"userId": step1._id,
					"deviceId": params.deviceId,
					"accessTokenKey": salt,
					"type": TOKEN_TYPE.USER_LOGIN,
					"userType": USER_TYPE.USER
				};
				await baseDao.updateMany("login_histories", { 'deviceToken': params.deviceToken, 'isLogin': true }, { 'arn': "", isLogin:false }, {});
				const [step2, accessToken] = await promise.join(
					loginHistoryDao.createUserLoginHistory({ ...step1, ...params, salt }),
					createToken(tokenData)
					);

				if (SERVER.IS_REDIS_ENABLE) redisClient.setExp(`${step1._id.toString()}.${params.deviceId}`, Math.floor(SERVER.TOKEN_INFO.EXPIRATION_TIME[TOKEN_TYPE.USER_LOGIN] / 1000), JSON.stringify(buildToken({ ...step1, ...params, salt })));
				await notificationManager.subscribeDeviceTokenViaAWS({token: params.deviceToken, userId: step1? step1._id:isSocialIdExists._id});
				//check contact sync with deviceId
				let deviceContact = await baseDao.find("contacts",{userId: step1? step1._id:isSocialIdExists._id,deviceId:params.deviceId},{_id:1,userId:1,deviceId:1},{});
				
				return MESSAGES.SUCCESS.LOGIN({
					accessToken,
					"_id": step1._id,
					"name":step1.name?step1.name:"",
					"firstName": step1?.firstName,
					"lastName": step1?.lastName,					
					"email": step1.email,
					"profilePicture": step1?.profilePicture,
					"phoneNumber": step1?.phoneNumber,
					//"isContactSynced": step1.isContactSynced,
					"isContactSynced":(deviceContact && deviceContact.length)?true:false,
					"pushNotificationStatus":step1?.pushNotificationStatus,
					"communityNotificationStatus": step1?.communityNotificationStatus,
					"wishesNotificationStatus":step1?.wishesNotificationStatus,
					"gratitudeNotificationStatus":step1?.gratitudeNotificationStatus,
					"perferMedidation":step1?.perferMedidation, 
					"preferPrayer":step1?.preferPrayer, 
					"locationSharing":step1?.locationSharing, 
					"offerBlessing":step1?.offerBlessing,
					"countryFlagCode":step1?.countryFlagCode,
					"intension":step1?.intension,
					"completeTooltip":step1?.completeTooltip,
					"loginType":params.loginType,
					"isEmailVerified":step1?.isEmailVerified,
					"isPasswordSet":step1?.isPasswordSet,
					"isProfileComplete": step1?.isProfileComplete
				});
			}
		} catch (error) {
			throw error;
		}
	}    
	/**
	 * @function registration
	 * @description firstname and lastname of user
	 */
	async registration(params: UserRequest.Registration,tokenData: TokenData) {
		try {
			const isExist = await userDaoV1.findUserById(tokenData.userId); 
			if (!isExist) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			const phoneExists = await baseDao.findOne("users",{_id:{"$ne":tokenData.userId},status:{"$ne":STATUS.DELETED},countryCode:params.countryCode,phoneNumber:params.phoneNumber});
			if (phoneExists) return Promise.reject(MESSAGES.ERROR.PHONE_NUMBER_ALREADY_EXISTS);tokenData.userId
			if(phoneExists && phoneExists.status == STATUS.BLOCKED) {
				let reason = phoneExists?.actionSummary.slice().reverse().find(obj => obj.status == STATUS.BLOCKED).reason.trim();
				reason = reason.length ? ` Reason: ${reason}` : "";
				return Promise.reject(MESSAGES.ERROR.USER_DEACTIVATED_BY_ADMIN(reason));
			}
			if(params.countryCode && params.phoneNumber) params.fullPhoneNumber = params.countryCode+params.phoneNumber;
			let step1 = await userDaoV1.registration(params, tokenData.userId);			
			if(!step1) return Promise.reject(MESSAGES.ERROR.SOMETHING_WENT_WRONG);

			// new number join app update contact table for synced one update userId and isappuser flag

		    // await baseDao.updateMany("contacts",{phoneNumber:{"$in":[params.countryCode+params.phoneNumber,params.phoneNumber]},isAppUser:false},{isAppUser:true,contactUserId:tokenData.userId},{});
			// await baseDao.updateMany("contacts",{phoneNumber:{"$regex": `${params.phoneNumber}$`},isAppUser:false},{isAppUser:true,contactUserId:tokenData.userId},{});

			// const prefixPhoneNumber = (!params.phoneNumber.startsWith('+') && params.phoneNumber.length === 9 && /^\d{9}$/.test(params.phoneNumber)) 
			// ? '0' + params.phoneNumber 
			// : params.phoneNumber;

			// const phoneCodes = await userDaoV1.getPhoneCodes();
			// const prefixPhoneNumber = this._processPhoneNumber(phoneCodes, params.countryCode + params.phoneNumber);
			// const prefixPhoneNumber2 = this._processPhoneNumber(phoneCodes, params.phoneNumber);

			await baseDao.updateMany(
				"contacts_v2",
				{
					"phoneNumbers.phoneNumber": { "$in": [params.countryCode + params.phoneNumber, params.phoneNumber] },
					// "phoneNumbers.connectedToAccount": { "$ne": true }
				},
				{
					$set: {
						"phoneNumbers.$[elem].connectedToAccount": true,
						"phoneNumbers.$[elem].contactUserId": toObjectId(tokenData.userId),
						isAppUser: true
					}
				},
				{
					arrayFilters: [{ "elem.phoneNumber": { "$in": [params.countryCode + params.phoneNumber, params.phoneNumber] } }]
				}
			);
	
			await baseDao.updateMany(
				"contacts_v2",
				{
					"phoneNumbers.phoneNumber": { "$regex": `${params.phoneNumber}$` },
					// "phoneNumbers.connectedToAccount": { "$ne": true }
				},
				{
					$set: {
						"phoneNumbers.$[elem].connectedToAccount": true,
						"phoneNumbers.$[elem].contactUserId": toObjectId(tokenData.userId),
						isAppUser: true
					}
				},
				{
					arrayFilters: [{ "elem.phoneNumber": { "$regex": `${params.phoneNumber}$` } }]
				}
			);

			// wish invite status check
			let wishInvited = await baseDao.distinct("tagwishes","wishId",{phoneNumber:params.phoneNumber,wishType:{ '$in': [WISH_TYPE.MYSELF,WISH_TYPE.OTHER]},type:WISH_TAG_TYPE.INVITED,status:STATUS.ACTIVE, $or: [
				{ communityId: { $exists: false } }, // communityId doesn't exist
				{ communityId: null } // communityId is null
			]}); // no countryCode check required
			let wishCreated = await baseDao.distinct("tagwishes","wishId",{phoneNumber:params.phoneNumber,wishType:WISH_TYPE.OTHER,type:WISH_TAG_TYPE.INVITED_HOST,status:STATUS.ACTIVE});

			// manage user invitation type HOST, TAG, COMMUNITY
			const tagWishInvitedHost = await baseDao.findOne("tagwishes", { $or: [{ phoneNumber: params.phoneNumber }, { phoneNumber:params.countryCode+params.phoneNumber }], wishType:{ '$in': [WISH_TYPE.MYSELF, WISH_TYPE.OTHER]}, type: { '$in': [WISH_TAG_TYPE.INVITED, WISH_TAG_TYPE.INVITED_HOST] }, status: STATUS.ACTIVE }, { wishId: 1, communityId: 1, type: 1, createdAt: 1 }, {}, { createdAt: -1, _id: -1 });
			if(tagWishInvitedHost) {
				const wishTagType = tagWishInvitedHost.type == WISH_TAG_TYPE.INVITED_HOST ? INVITATION_TYPE.HOST : tagWishInvitedHost.communityId ? INVITATION_TYPE.COMMUNITY : INVITATION_TYPE.TAG;
				await baseDao.updateOne("users", { _id: isExist._id }, { invitationType: wishTagType, invitationDate: tagWishInvitedHost.createdAt, invitationWishId: tagWishInvitedHost.wishId }, {});
			}

			// logic to handle non app tagged users that they joined the app
			// type INVITED update to CONTACTS + userId save
			await baseDao.updateMany("tagwishes",
            {
				"$or": [
					{ phoneNumber: params.phoneNumber },
					{ phoneNumber: params.countryCode+params.phoneNumber }  
				],
				type: WISH_TAG_TYPE.INVITED
		    },
			{ userId: tokenData.userId, type: WISH_TAG_TYPE.CONTACTS }, {});

			// login to handle non app tagged host that they joined the app
			// type INVITED_HOST remain as it is + userId save
			await baseDao.updateMany("tagwishes",
            {
				"$or": [
					{ phoneNumber: params.phoneNumber },
					{ phoneNumber: params.countryCode+params.phoneNumber }  
				],
				type: WISH_TAG_TYPE.INVITED_HOST
		    },
			{ userId: tokenData.userId }, {});

			// for updating community data
			await baseDao.updateMany("members",
            {
				"$or": [
					{ phoneNumber: params.phoneNumber },
					{ phoneNumber: params.countryCode+params.phoneNumber }  
				],
		    },
			{ userId: tokenData.userId, type: WISH_TAG_TYPE.CONTACTS }, {});

			if(wishCreated && wishCreated.length) {
				await baseDao.updateMany("wishes", { _id: { $in: wishCreated } }, { hostId: tokenData.userId }, {});
			}

			if (wishInvited && wishInvited.length > 0)
			step1.type = REGISTRATION_FLAG.WISH_INVITED;
			else if (wishCreated && wishCreated.length > 0)
			step1.type = REGISTRATION_FLAG.WISH_CREATED;
			else
			step1.type = REGISTRATION_FLAG.DEFAULT;
			step1.wishInvitedIds = (wishInvited && wishInvited.length) ? wishInvited : [];
			step1.wishCretaedIds = (wishCreated && wishCreated.length) ? wishCreated : [];

			// check contact sync with deviceId
			// let deviceContact = await baseDao.find("contacts", { userId: step1 ? step1._id : isExist._id, deviceId:params.deviceId },{ _id: 1, userId: 1, deviceId: 1}, {});
			let deviceContact = await baseDao.find("contacts_v2", { userId: step1 ? step1._id : isExist._id, deviceId:params.deviceId },{ _id: 1, userId: 1, deviceId: 1}, {});
			step1.isContactSynced = (deviceContact && deviceContact.length) ? true : false;

			const myContacts = await userDaoV1.mySyncedContactsV2(params);
			if(myContacts.length > 0) {
				await notificationManager.PublishContactNotifications(myContacts, step1.firstName);
			}
			await baseDao.updateOne("users", { _id: isExist._id }, { isProfileComplete: true }, {});
			return MESSAGES.SUCCESS.DETAILS(step1);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function contactSync
	 * @description firstname and lastname of user
	 */
	async contactSync(params: UserRequest.ContactSync,tokenData: TokenData) {
        try {
			const step1 = await userDaoV1.findUserById(tokenData.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
            const resultrepo = [];
            let matchedData = [], nonMatchedData = [], userIds = [];
            for (var i = 0; i < params.contacts.length; i++) {
                if (typeof params.contacts[i]['phoneNumber'] === 'string')
                    resultrepo.push(params.contacts[i]['phoneNumber'])
            }
            let registeredUsers = await userDaoV1.getUserMatchContacts(resultrepo);  
			if(registeredUsers && registeredUsers.length !== 0){
				for (let i = 0; params.contacts.length > i; i++) {  
					let isValue = false;
					registeredUsers.map(async function (element) {		    
						if((element.phoneNumber == params.contacts[i]['phoneNumber'])||(element.fullPhoneNumber == params.contacts[i]['phoneNumber'])){
							console.log(params.contacts[i]['phoneNumber'],element.phoneNumber)
							params.contacts[i]['contactUserId'] = element._id
							if (element.profilePicture) params.contacts[i]['profilePicture'] = element.profilePicture
							matchedData.push(params.contacts[i]);
							isValue=true;
						}
					});
					if(isValue == false){
						nonMatchedData.push(params.contacts[i]);
					}
				} 
			}else{
				nonMatchedData = params.contacts
			}
			let contactArray = [];
			if (matchedData && matchedData.length !== 0) {
                for (let i = 0; matchedData.length > i; i++) {
					let obj = {};
					obj['userId'] = toObjectId(params.userId);
					if(matchedData[i].name) obj['name'] = matchedData[i].name;
					if(matchedData[i].profilePicture) obj['profilePicture'] = matchedData[i].profilePicture;
					obj['phoneNumber'] = matchedData[i].phoneNumber;
					obj['isAppUser'] = true;
					obj['contactUserId'] = matchedData[i].contactUserId;
					obj['status'] = STATUS.UN_BLOCKED;
					obj['deviceId'] = params.deviceId;
                    contactArray.push(obj);  
                }
            }
			if (nonMatchedData && nonMatchedData.length !== 0) {
                for (let i = 0; nonMatchedData.length > i; i++) {
					let obj = {};
					obj['userId'] = toObjectId(params.userId);
					if(nonMatchedData[i].name) obj['name'] = nonMatchedData[i].name;
					if(nonMatchedData[i].profilePicture) obj['profilePicture'] = nonMatchedData[i].profilePicture;
					obj['phoneNumber'] = nonMatchedData[i].phoneNumber
					obj['isAppUser'] = false;
					obj['status'] = STATUS.UN_BLOCKED;
					obj['deviceId'] = params.deviceId;
                    contactArray.push(obj);  
                }
            }
			await baseDao.updateOne("users", { '_id': tokenData.userId }, {'isContactSynced': true}, {});
			await userDaoV1.addContactSync(contactArray);  
			return MESSAGES.SUCCESS.CONTACT_SYNC({
				'isContactSynced': true
			});
        } catch (error) {
            console.log('getUserSyncedContacts', error, false)
            return Promise.reject(error)
        }
    }

	// /**
	//  * @function contactSyncV2
	//  * @description Sync contacts for the user
	//  */
	// async contactSyncV2(params: UserRequest.ContactSyncV2, tokenData: TokenData) {
	// 	try {
	// 		const step1 = await userDaoV1.findUserById(tokenData.userId);
	// 		if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
		
	// 		const resultrepo = [];
	// 		let matchedData = [], nonMatchedData = [], userIds = [];

	// 		const phoneCodes = await userDaoV1.getPhoneCodes();
		
	// 		for (let i = 0; i < params.contacts.length; i++) {
	// 			const contact = params.contacts[i];
	// 			if (Array.isArray(contact.phoneNumbers)) {
	// 				for (let j = 0; j < contact.phoneNumbers.length; j++) {
	// 					const phoneNumberObj = contact.phoneNumbers[j];
	// 					if (typeof phoneNumberObj.phoneNumber === 'string') {
	// 						resultrepo.push(this._processPhoneNumber(phoneCodes, phoneNumberObj.phoneNumber));
	// 					}
	// 				}
	// 			}
	// 		}

	// 		console.log("CHECKPOINT 1");
	// 		let registeredUsers = await userDaoV1.getUserMatchContacts(resultrepo);
	// 		console.log("REGISTERED USERS = ", registeredUsers);
		
	// 		if (registeredUsers && registeredUsers.length !== 0) {
	// 			for (let i = 0; i < params.contacts.length; i++) {
	// 				let isValue = false;
			
	// 				params.contacts[i]['phoneNumbers'].forEach(phoneNumberObj => {
	// 					const prefixNumber = this._processPhoneNumber(phoneCodes, phoneNumberObj.phoneNumber)
	// 					registeredUsers.map(function (element) {
	// 					if (
	// 						element.phoneNumber === prefixNumber ||
	// 						element.fullPhoneNumber === prefixNumber
	// 					) {
	// 						console.log(prefixNumber, element.phoneNumber);
	// 						params.contacts[i]['contactUserId'] = element._id;
	// 						if (element.profilePicture) {
	// 							params.contacts[i]['profilePicture'] = element.profilePicture;
	// 						}
	// 						matchedData.push(params.contacts[i]);
	// 						isValue = true;
	// 					}
	// 					});
	// 				});
			
	// 				if (!isValue) {
	// 					nonMatchedData.push(params.contacts[i]);
	// 				}
	// 			}
	// 		} else {
	// 			nonMatchedData = params.contacts;
	// 		}
		
	// 		let contactArray = [];
		
	// 		// Use a Set to keep track of seen contactUserIds
	// 		let seenIds = new Set();
	// 		let filteredData = matchedData.filter(obj => {
	// 			if (seenIds.has(obj.contactUserId)) {
	// 				return false; // Duplicate found, skip this object
	// 			} else {
	// 				seenIds.add(obj.contactUserId); // Add contactUserId to the set
	// 				return true; // Unique contactUserId, keep this object
	// 			}
	// 		});
		
	// 		console.log("FILTERED DATA = ", filteredData);

	// 		// const phoneCodes = await userDaoV1.getPhoneCodes();
	// 		// console.log("PHONE CODES = ", phoneCodes);
		
	// 		for (let i = 0; i < filteredData.length; i++) {
	// 			let obj = {};
	// 			obj['userId'] = toObjectId(params.userId);
		
	// 			if (filteredData[i].name) obj['name'] = filteredData[i].name;
		
	// 			obj['phoneNumbers'] = filteredData[i].phoneNumbers.map(phone => {
	// 				const prefixPhoneNumber = this._processPhoneNumber(phoneCodes, phone.phoneNumber);
	// 				const connectedToAccount = registeredUsers.some(user => user.phoneNumber === prefixPhoneNumber || user.fullPhoneNumber === prefixPhoneNumber);
	// 				const registeredUser = connectedToAccount ? registeredUsers.find(user => user.phoneNumber === prefixPhoneNumber || user.fullPhoneNumber === prefixPhoneNumber) : null;
	// 				const profilePicture = registeredUser ? registeredUser.profilePicture : null;
	// 				const contactUserId = registeredUser ? registeredUser._id : null;
			
	// 				// const prefixPhoneNumber = (!phone.phoneNumber.startsWith('+') && phone.phoneNumber.length === 9 && /^\d{9}$/.test(phone.phoneNumber)) 
	// 				// 	? '0' + phone.phoneNumber 
	// 				// 	: phone.phoneNumber;			
			
	// 				return {
	// 					phoneNumber: prefixPhoneNumber,
	// 					originalPhoneNumber: phone.phoneNumber,
	// 					type: phone.type,
	// 					default: null,
	// 					connectedToAccount: connectedToAccount,
	// 					profilePicture: profilePicture,
	// 					contactUserId: contactUserId
	// 				};
	// 			});
		
	// 			obj['isAppUser'] = true;
	// 			obj['status'] = STATUS.UN_BLOCKED;
	// 			obj['deviceId'] = params.deviceId;
	// 			contactArray.push(obj);
	// 		}
		
	// 		for (let i = 0; nonMatchedData.length > i; i++) {
	// 			let obj = {};
	// 			obj['userId'] = toObjectId(params.userId);
	// 			if (nonMatchedData[i].name) obj['name'] = nonMatchedData[i].name;
	// 			if (nonMatchedData[i].profilePicture) obj['profilePicture'] = nonMatchedData[i].profilePicture;
		
	// 			if (!nonMatchedData[i].isAppUser) {
	// 				let defaultSet = false;
	// 				for (let j = 0; j < nonMatchedData[i].phoneNumbers.length; j++) {
	// 					if (nonMatchedData[i].phoneNumbers[j].type.toLowerCase() === 'mobile' && !defaultSet) {
	// 						nonMatchedData[i].phoneNumbers[j].default = true;
	// 						defaultSet = true;
	// 					} else {
	// 						nonMatchedData[i].phoneNumbers[j].default = false;
	// 					}
	// 				}
	// 				if (!defaultSet && nonMatchedData[i].phoneNumbers.length > 0) {
	// 					nonMatchedData[i].phoneNumbers[0].default = true;
	// 					for (let k = 1; k < nonMatchedData[i].phoneNumbers.length; k++) {
	// 						nonMatchedData[i].phoneNumbers[k].default = false;
	// 					}
	// 				}
	// 			}
		
	// 			obj['phoneNumbers'] = nonMatchedData[i].phoneNumbers.map(phone => {
	// 				// const prefixPhoneNumber = (!phone.phoneNumber.startsWith('+') && phone.phoneNumber.length === 9 && /^\d{9}$/.test(phone.phoneNumber)) 
	// 				// 	? '0' + phone.phoneNumber 
	// 				// 	: phone.phoneNumber;

	// 				const prefixPhoneNumber = this._processPhoneNumber(phoneCodes, phone.phoneNumber);
			
	// 				return {
	// 					phoneNumber: prefixPhoneNumber,
	// 					originalPhoneNumber: phone.phoneNumber,
	// 					type: phone.type,
	// 					default: phone.default,
	// 					connectedToAccount: null,
	// 					profilePicture: null,
	// 					contactUserId: null
	// 				};
	// 			});
		
	// 			obj['isAppUser'] = false;
	// 			obj['status'] = STATUS.UN_BLOCKED;
	// 			obj['deviceId'] = params.deviceId;
	// 			contactArray.push(obj);
	// 		}
		
	// 		console.log('CONTACTS ARRAY = ', contactArray);
		
	// 		for (const contact of contactArray) {
	// 			for (const phoneNumberObj of contact.phoneNumbers) {
	// 			const existingContact = await userDaoV1.findContactV2({
	// 				userId: contact.userId,
	// 				deviceId: contact.deviceId,
	// 				'phoneNumbers.phoneNumber': phoneNumberObj.phoneNumber
	// 			});
		
	// 			console.log("EXISTING CONTACT = ", existingContact);
		
	// 			if (existingContact) {
	// 				// Update existing contact by adding new phone numbers if they don't already exist
	// 				const existingPhoneNumbers = existingContact.phoneNumbers.map(phone => phone.phoneNumber);
	// 				const newPhoneNumbers = contact.phoneNumbers.filter(phone => !existingPhoneNumbers.includes(phone.phoneNumber));
		
	// 				if (newPhoneNumbers.length > 0) {
	// 					existingContact.phoneNumbers = [...existingContact.phoneNumbers, ...newPhoneNumbers];
			
	// 					// Check if any of the new phone numbers are connected to an account
	// 					const isAppUserUpdated = newPhoneNumbers.some(phone => phone.connectedToAccount);
			
	// 					if (isAppUserUpdated) {
	// 						existingContact.isAppUser = true;
	// 					}
			
	// 					await userDaoV1.updateContactV2(existingContact);
	// 				}
	// 			} else {
	// 				// Create a new contact document
	// 				await userDaoV1.createContactV2(contact);
	// 			}
	// 			}
	// 		}
	// 		await baseDao.updateOne("users", { '_id': tokenData.userId }, {'isContactSynced': true}, {});
	// 		return MESSAGES.SUCCESS.CONTACT_SYNC({
	// 			'isContactSynced': true
	// 		});
	// 	} catch (error) {
	// 		console.log('getUserSyncedContacts', error, false);
	// 		return Promise.reject(error);
	// 	}
	// }	

	/**
	 * @function contactSyncV2
	 * @description Sync contacts for the user
	 */
	async contactSyncV2(params: UserRequest.ContactSyncV2, tokenData: TokenData) {
		try {
			const step1 = await userDaoV1.findUserById(tokenData.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			const phoneCodes = await userDaoV1.getPhoneCodes();  // Fetch phoneCodes here
			const batchSize = 100; // Define the batch size
			const contacts = params.contacts;
			// Split contacts into batches
			const batches = [];
			for (let i = 0; i < contacts.length; i += batchSize) {
				const batch = contacts.slice(i, i + batchSize);
				batches.push(batch);
			}
			// Process all batches concurrently
			await Promise.all(batches.map(batch => this._processBatch(batch, phoneCodes, tokenData, params.deviceId)));
			await baseDao.updateOne("users", { '_id': tokenData.userId }, { 'isContactSynced': true }, {});
			return MESSAGES.SUCCESS.CONTACT_SYNC({
				'isContactSynced': true
			});
		} catch (error) {
			console.log('getUserSyncedContacts', error, false);
			return Promise.reject(error);
		}
	}

	/**
	 * @function processBatch
	 * @description Process a batch of contacts
	 */
	async _processBatch(batch, phoneCodes, tokenData, deviceId) {
		const resultrepo = [];
		let matchedData = [], nonMatchedData = [];
		for (let i = 0; i < batch.length; i++) {
			const contact = batch[i];
			if (Array.isArray(contact.phoneNumbers)) {
				for (let j = 0; j < contact.phoneNumbers.length; j++) {
					const phoneNumberObj = contact.phoneNumbers[j];
					if (typeof phoneNumberObj.phoneNumber === 'string') {
						resultrepo.push(this._processPhoneNumber(phoneCodes, phoneNumberObj.phoneNumber));
					}
				}
			}
		}
		let registeredUsers = await userDaoV1.getUserMatchContacts(resultrepo);
		if (registeredUsers && registeredUsers.length !== 0) {
			for (let i = 0; i < batch.length; i++) {
				let isValue = false;
				batch[i]['phoneNumbers'].forEach(phoneNumberObj => {
					const prefixNumber = this._processPhoneNumber(phoneCodes, phoneNumberObj.phoneNumber)
					registeredUsers.map(function (element) {
						if (
							element.phoneNumber === prefixNumber ||
							element.fullPhoneNumber === prefixNumber
						) {
							batch[i]['contactUserId'] = element._id;
							if (element.profilePicture) {
								batch[i]['profilePicture'] = element.profilePicture;
							}
							matchedData.push(batch[i]);
							isValue = true;
						}
					});
				});
				if (!isValue) {
					nonMatchedData.push(batch[i]);
				}
			}
		} else {
			nonMatchedData.push(...batch);
		}
		let contactArray = [];
		let seenIds = new Set();
		let filteredData = matchedData.filter(obj => {
			if (seenIds.has(obj.contactUserId)) {
				return false; // Duplicate found, skip this object
			} else {
				seenIds.add(obj.contactUserId); // Add contactUserId to the set
				return true; // Unique contactUserId, keep this object
			}
		});
		for (let i = 0; i < filteredData.length; i++) {
			let obj = {};
			obj['userId'] = toObjectId(tokenData.userId);
			if (filteredData[i].name) obj['name'] = filteredData[i].name;
			obj['phoneNumbers'] = filteredData[i].phoneNumbers.map(phone => {
				const prefixPhoneNumber = this._processPhoneNumber(phoneCodes, phone.phoneNumber);
				const connectedToAccount = registeredUsers.some(user => user.phoneNumber === prefixPhoneNumber || user.fullPhoneNumber === prefixPhoneNumber);
				const registeredUser = connectedToAccount ? registeredUsers.find(user => user.phoneNumber === prefixPhoneNumber || user.fullPhoneNumber === prefixPhoneNumber) : null;
				const profilePicture = registeredUser ? registeredUser.profilePicture : null;
				const contactUserId = registeredUser ? registeredUser._id : null;
				return {
					phoneNumber: prefixPhoneNumber,
					originalPhoneNumber: phone.phoneNumber,
					type: phone.type,
					default: null,
					connectedToAccount: connectedToAccount,
					profilePicture: profilePicture,
					contactUserId: contactUserId
				};
			});
			obj['isAppUser'] = true;
			obj['status'] = STATUS.UN_BLOCKED;
			obj['deviceId'] = deviceId;
			contactArray.push(obj);
		}
		for (let i = 0; nonMatchedData.length > i; i++) {
			let obj = {};
			obj['userId'] = toObjectId(tokenData.userId);
			if (nonMatchedData[i].name) obj['name'] = nonMatchedData[i].name;
			if (nonMatchedData[i].profilePicture) obj['profilePicture'] = nonMatchedData[i].profilePicture;
			if (!nonMatchedData[i].isAppUser) {
				let defaultSet = false;
				for (let j = 0; j < nonMatchedData[i].phoneNumbers.length; j++) {
					if (nonMatchedData[i].phoneNumbers[j].type.toLowerCase() === 'mobile' && !defaultSet) {
						nonMatchedData[i].phoneNumbers[j].default = true;
						defaultSet = true;
					} else {
						nonMatchedData[i].phoneNumbers[j].default = false;
					}
				}
				if (!defaultSet && nonMatchedData[i].phoneNumbers.length > 0) {
					nonMatchedData[i].phoneNumbers[0].default = true;
					for (let k = 1; k < nonMatchedData[i].phoneNumbers.length; k++) {
						nonMatchedData[i].phoneNumbers[k].default = false;
					}
				}
			}
			obj['phoneNumbers'] = nonMatchedData[i].phoneNumbers.map(phone => {
				const prefixPhoneNumber = this._processPhoneNumber(phoneCodes, phone.phoneNumber);
				return {
					phoneNumber: prefixPhoneNumber,
					originalPhoneNumber: phone.phoneNumber,
					type: phone.type,
					default: phone.default,
					connectedToAccount: null,
					profilePicture: null,
					contactUserId: null
				};
			});
			obj['isAppUser'] = false;
			obj['status'] = STATUS.UN_BLOCKED;
			obj['deviceId'] = deviceId;
			contactArray.push(obj);
		}
		for (const contact of contactArray) {
			for (const phoneNumberObj of contact.phoneNumbers) {
				const existingContact = await userDaoV1.findContactV2({
					userId: contact.userId,
					deviceId: contact.deviceId,
					'phoneNumbers.phoneNumber': phoneNumberObj.phoneNumber
				});
				if (existingContact) {
					const existingPhoneNumbers = existingContact.phoneNumbers.map(phone => phone.phoneNumber);
					const newPhoneNumbers = contact.phoneNumbers.filter(phone => !existingPhoneNumbers.includes(phone.phoneNumber));
					if (newPhoneNumbers.length > 0) {
						existingContact.phoneNumbers = [...existingContact.phoneNumbers, ...newPhoneNumbers];
						const isAppUserUpdated = newPhoneNumbers.some(phone => phone.connectedToAccount);
						if (isAppUserUpdated) {
							existingContact.isAppUser = true;
						}
						await userDaoV1.updateContactV2(existingContact);
					}
				} else {
					await userDaoV1.createContactV2(contact);
				}
			}
		}
	}

	/**
	 * @function logout
	 */
	async logout(tokenData: TokenData) {
		try {
			await this.removeSession(tokenData, SERVER.IS_SINGLE_DEVICE_LOGIN[USER_TYPE.USER]);
			return MESSAGES.SUCCESS.USER_LOGOUT;
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function profile
	 */
	async profile(params: UserId, tokenData: TokenData) {
		try {
			const step1 = await userDaoV1.profileDetail(params.userId || tokenData.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			// update device token
			if(params.deviceToken) {
				await notificationManager.subscribeDeviceTokenViaAWS({token: params.deviceToken, userId: step1._id});
			}
			//community request counter- get data
			let communityRequestCount = await baseDao.countDocuments("members",{userId: toObjectId(tokenData.userId), status: STATUS.PENDING})
			step1.communityRequestCount = communityRequestCount;
			//total bless receivied on my wishes
			let dateFilter;
			let receiverIdBless = await wishesDaoV1.totalBlessReceived(step1._id);
			if(receiverIdBless && receiverIdBless.length)
				step1.receiverIdBlessCount = receiverIdBless[0].totalWishbless;
			else
				step1.receiverIdBlessCount =0;
			//total listen for bless in seconds
			let listenCount = await blessingDaoV1.totalBlessListen(step1._id,dateFilter=false, params?.offset, false);
			if(listenCount && listenCount.length)
				step1.totalListenBless = listenCount[0].totalListenBlessTime;
			else
				step1.totalListenBless =0;
			//home total listen duration
			let totalListenDurationHome = await blessingDaoV1.totalBlessListen(step1._id,dateFilter=true, params?.offset, true);
			if(totalListenDurationHome && totalListenDurationHome.length){
				step1.homeTotalDuration = totalListenDurationHome[0].totalListenBlessTime;
				step1.homeTotalTodayBless = totalListenDurationHome[0].totalDocuments;
			}
			else{
				step1.homeTotalDuration =0;
				step1.homeTotalTodayBless =0;
			}
			//get total grtiude on my bless received gratitude count
			let allBless = await baseDao.distinct("perform_blessings","_id",{userId:step1._id})
			let gratitudesCount =0;
			gratitudesCount = await baseDao.countDocuments("gratitudes",{blessingId:{"$in":allBless}});
			step1.gratitudesCount = step1.globalGratRecieveCount ? gratitudesCount+step1.globalGratRecieveCount : gratitudesCount;  //received gratitude count
			let deviceContact = await baseDao.find("contacts",{userId: step1._id,deviceId:params.deviceId},{_id:1,userId:1,deviceId:1},{});
			step1.isContactSynced = (deviceContact && deviceContact.length)?true:false;
			const thankWWTooltip = await baseDao.findOne("contents", { type: CONTENT_TYPE.THANK_WISHWELL_TOOLTIP }, { _id: 0, data: 1 });
			step1.thankTooltipMessage = thankWWTooltip?thankWWTooltip.data:"";
			const totalPendingRequest = await baseDao.countDocuments("members", { userId: toObjectId(tokenData.userId), status: STATUS.PENDING });
			step1.totalPendingRequest = totalPendingRequest;
			step1.shareLink = `${SERVER.APP_URL}${SERVER.API_BASE_URL}/v1/common/deeplink?type=${DEEPLINK_TYPE.SMS_INVITE}`;
			// UPDATE OFFSET FOR CHECKING PURPOSE
			await baseDao.updateOne("users", { _id: step1._id }, { profileOffset: params.offset }, {});
			return MESSAGES.SUCCESS.DETAILS(step1);
		} catch (error) {
			throw error;
		}
	}
	
	/**
	 * @function settings
	 */
	async settings(params: UserRequest.Settings, tokenData: TokenData) {
		try {
			const step1 = await userDaoV1.findUserById(tokenData.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
            await baseDao.updateOne("users",{_id:tokenData.userId},params,{});
			//login history notification flag update
			let loginUpdate = {};
			loginUpdate['userId.pushNotificationStatus'] = params.pushNotificationStatus;
			loginUpdate['userId.communityNotificationStatus'] = params.communityNotificationStatus;
			loginUpdate['userId.wishesNotificationStatus'] = params.wishesNotificationStatus;
			loginUpdate['userId.gratitudeNotificationStatus'] = params.gratitudeNotificationStatus;
			await baseDao.updateOne("login_histories",{"userId._id":tokenData.userId,"isLogin":true},loginUpdate,{})
			return MESSAGES.SUCCESS.PROFILE_SETTINGS(params);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function notificationStatus
	 */
	async notificationStatus(params: UserRequest.NotificationStatus, tokenData: TokenData) {
		try {
			let notificationData = baseDao.findOne("notifications", { _id: params.notificationId }, { _id: 1 })
			if (notificationData) {
				await baseDao.updateOne("notifications", { "_id": params.notificationId }, { "$set": { isRead: params.isRead } }, {});

			}
			return MESSAGES.SUCCESS.DEFAULT;
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function NotificationList   
	 */
	async NotificationList(params: UserRequest.NotificationList, tokenData: TokenData) {
		try {
			const data = await userDaoV1.notificationList(params, tokenData);
			return MESSAGES.SUCCESS.LIST(data);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function deleteSyncContact
	 * @description delete contact 
	 */
	async deleteSyncContact(params: UserRequest.DeleteSyncContact,tokenData: TokenData) {
        try {
			const step1 = await userDaoV1.findUserById(tokenData.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			
            const resultrepo = [];
            for (var i = 0; i < params.contacts.length; i++) {
                if (typeof params.contacts[i]['phoneNumber'] === 'string')
                    resultrepo.push(params.contacts[i]['phoneNumber'])
            }
            let contacts = await userDaoV1.getContacts(resultrepo,tokenData.userId,params);   
			await baseDao.updateMany("contacts", { '_id': { '$in': contacts },'deviceId':params.deviceId }, {status: STATUS.DELETED}, {});
			return MESSAGES.SUCCESS.DELETE_SYNC_CONTACT;
        } catch (error) {
            console.log('deleteUserSyncedContacts', error, false)
            return Promise.reject(error)
        }
    }

	/**
	 * @function deleteSyncContactV2
	 * @description Delete contact phone numbers and update status if necessary
	 */
	async deleteSyncContactV2(params: UserRequest.DeleteSyncContactV2, tokenData: TokenData) {
		try {
			const step1 = await userDaoV1.findUserById(tokenData.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);

			const phoneCodes = await userDaoV1.getPhoneCodes()

			const resultrepo = params.contacts.map(contact => {
				return this._processPhoneNumber(phoneCodes, contact.phoneNumber);
			});

			// Fetch contacts matching the phone numbers and user ID
			const contacts = await userDaoV1.getContactsV2(resultrepo, tokenData.userId, params);

			const promises = contacts.map(async (contact) => {
				// const initialPhoneNumbersCount = contact.phoneNumbers.length;
				const phoneNumbersToRemove = params.contacts.map(c => {
					return this._processPhoneNumber(phoneCodes, c.phoneNumber);
				});

				// Update operation for each contact
				const updateOperations = phoneNumbersToRemove.map(phoneNumber => {
					return baseDao.updateOne(
						"contacts_v2",
						{ _id: contact._id, deviceId: params.deviceId },
						{
							$pull: {
								phoneNumbers: {
									phoneNumber: phoneNumber
								}
							}
						},
						{}
					);
				});

				// Execute all update operations for the current contact
				await Promise.all(updateOperations);

				// Reload the contact after updates
				const updatedContact = await userDaoV1.findContactById(contact._id);

				// Check if the contact has no phone numbers left after the update
				if (updatedContact.phoneNumbers.length === 0) {
					await userDaoV1.deleteContactByIdV2(contact._id);
				} else if (!updatedContact.isAppUser) {
					// Non-app user logic
					let defaultSet = false;

					// Find the index of the deleted phone number
					const deletedIndex = contact.phoneNumbers.findIndex(phone => phoneNumbersToRemove.includes(phone.phoneNumber));
					
					// Check if the deleted phone number was the default
					if (contact.phoneNumbers[deletedIndex].default) {
						// // Loop through remaining phone numbers to set a new default
						// for (let i = 0; i < updatedContact.phoneNumbers.length; i++) {
						// 	if (updatedContact.phoneNumbers[i].type.toLowerCase() === 'mobile') {
						// 		updatedContact.phoneNumbers[i].default = true;
						// 		defaultSet = true;
						// 		break;
						// 	}
						// }

						// // If no 'mobile' type numbers were found, set the first number as default
						// if (!defaultSet && updatedContact.phoneNumbers.length > 0) {
						// 	updatedContact.phoneNumbers[0].default = true;
						// }

						// // Update the contact with the new default setting
						// await baseDao.updateOne(
						// 	"contacts_v2",
						// 	{ _id: updatedContact._id, deviceId: params.deviceId },
						// 	{ $set: { phoneNumbers: updatedContact.phoneNumbers } },
						// 	{}
						// );
						await userDaoV1.deleteContactByIdV2(updatedContact._id);
					}
				}
				// Check if the contact is an app user
				else if (updatedContact.isAppUser) {
					const hasConnectedNumber = updatedContact.phoneNumbers.some(phone => phone.connectedToAccount);
					if (!hasConnectedNumber) {
						// Set isAppUser to false if no connected number left
						await baseDao.updateOne(
							"contacts_v2",
							{ _id: contact._id, deviceId: params.deviceId },
							{ $set: { isAppUser: false } },
							{}
						);
					}

					// Set default number logic
					let defaultSet = false;
					for (let phone of updatedContact.phoneNumbers) {
						if (phone.type.toLowerCase() === 'mobile' && !defaultSet) {
							await baseDao.updateOne(
								"contacts_v2",
								{ _id: contact._id, deviceId: params.deviceId, "phoneNumbers.phoneNumber": phone.phoneNumber },
								{ $set: { "phoneNumbers.$.default": true } },
								{}
							);
							defaultSet = true;
						} else {
							await baseDao.updateOne(
								"contacts_v2",
								{ _id: contact._id, deviceId: params.deviceId, "phoneNumbers.phoneNumber": phone.phoneNumber },
								{ $set: { "phoneNumbers.$.default": false } },
								{}
							);
						}
					}

					// If no mobile number was found, set the first number as default
					if (!defaultSet && updatedContact.phoneNumbers.length > 0) {
						await baseDao.updateOne(
							"contacts_v2",
							{ _id: contact._id, deviceId: params.deviceId, "phoneNumbers.phoneNumber": updatedContact.phoneNumbers[0].phoneNumber },
							{ $set: { "phoneNumbers.$.default": true } },
							{}
						);
					}
				}
			});

			await Promise.all(promises);

			return MESSAGES.SUCCESS.DELETE_SYNC_CONTACT;
		} catch (error) {
			console.log('deleteSyncContactV2 Error:', error);
			throw error;
		}
	}

	/**
	 * @function favorites
	 * @description forvourites wishes of user
	 */
	async favourites(params: UserRequest.Favourites,tokenData: TokenData) {
		try {
			let step1 = await userDaoV1.findUserById(tokenData.userId); 
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			let step2 = await wishesDaoV1.findWishById(params.wishId); 
			if (!step2) return Promise.reject(MESSAGES.ERROR.WISH_NOT_FOUND);
			params.userId =  tokenData.userId
			let step4;
			if(params.type == ACTION_TYPE.ADD){
				let step3 = await baseDao.findOne("favourites", { userId: params.userId, wishId: params.wishId, status:{'$ne':STATUS.DELETED}  });
			    if(step3) return Promise.reject(MESSAGES.ERROR.WISH_ALREADY_ADDED);
				step4 = await userDaoV1.addFavourites(params); // add-favourite			
			}else{
				step4 = await userDaoV1.removeFavourites(params); //remove-favorites
				if(!step4) return Promise.reject(MESSAGES.ERROR.WISH_DOES_NOT_EXIST);

			}
			return MESSAGES.SUCCESS.DETAILS(step4);
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function getFavourites
	 * @description forvourites list of wishes
	 */
	async getFavourites(params: UserRequest.FavouritesList,tokenData: TokenData) {
		try {
			let step1 = await userDaoV1.findUserById(tokenData.userId); 
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			// let step2 = await wishesDaoV1.findWishById(params.wishId); 
			// if (!step2) return Promise.reject(MESSAGES.ERROR.WISH_NOT_FOUND);
			let step2 = await reportDaoV1.reportedWishes(REPORT_TYPE.WISH,tokenData.userId)
			let step3 = await userDaoV1.getFavourite(params,tokenData.userId,step2); // get-favourite	
			await step3.data.map(obj=>{
				let encodedWishId = Buffer.from((obj.wishId).toString()).toString('base64');
				let encodedUserId = Buffer.from((tokenData.userId).toString()).toString('base64');
				obj["wishDetail"]["shareLink"] =  `${SERVER.APP_URL}${SERVER.API_BASE_URL}/v1/common/deeplink?type=${DEEPLINK_TYPE.WISH_SHARE}&wid=${encodedWishId}&uid=${encodedUserId}`
			})		
			return MESSAGES.SUCCESS.WISH_LIST(step3);
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function getList
	 * @description pin wishes list on home page
	 */
	async getList(params: UserRequest.Home,tokenData: TokenData) {
		try {
			let step1 = await userDaoV1.findUserById(tokenData.userId); 
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			let step2;
			params.limit = 8;
			if(params.isInclude){
				step2 = await userDaoV1.favourite(tokenData.userId,params);
				params.limit = 8-step2.total;
				let step3;
				if(params.type == HOME_LIST.SUGGESTED){
					step3 = await userDaoV1.suggestedRequested(tokenData.userId,params); // get-least-blessed-wishes		
					let step4 = await userDaoV1.suggestedReceived(tokenData.userId,params); // get-least-blessed-wishes	
					step3.data = step3.data.concat(step4.data);
					step3.data = step3.data.splice(0, params.limit);
				}else{
					step3 = await userDaoV1.leastBlessedRequested(tokenData.userId,params); // get-least-blessed-wishes		
					let step4 = await userDaoV1.leastBlessedReceived(tokenData.userId,params); // get-least-blessed-wishes	
					step3.data = step3.data.concat(step4.data);
					let sortedData = step3.data.sort((a, b) => a.wishDetail.totalBlessings - b.wishDetail.totalBlessings);
					step3.data = sortedData.splice(0, params.limit);
				}
				await baseDao.updateMany("pinned_wishes", { "userId": tokenData.userId }, { "$set": { status: STATUS.DELETED } }, {});
				let array=[];
				step2.data.forEach(element => {
					let obj ={}
					obj['subjectId']=toObjectId(element.wishDetail._id);  
					obj['userId']=toObjectId(tokenData.userId);
					obj['status']=STATUS.ACTIVE
					array.push(obj);  
				});
				step3.data.forEach(element => {
					let obj ={}
					obj['subjectId']=toObjectId(element.wishDetail._id);  
					obj['userId']=toObjectId(tokenData.userId);
					obj['status']=STATUS.ACTIVE
					array.push(obj);  
				});
				await baseDao.insertMany("pinned_wishes",array,{});

			}else{
				if(params.type == HOME_LIST.FAVOURITE){
					step2 = await userDaoV1.favourite(tokenData.userId,params); // get-favourite-wishes		
					params.limit = 8-step2.total
					if(params.limit > 0){
				    let suggest1 = await userDaoV1.suggestedRequested(tokenData.userId,params);
			    	let suggest2 = await userDaoV1.suggestedReceived(tokenData.userId,params);
		     		step2['suggestedCount'] = suggest1.data.length + suggest2.data.length > params.limit ? params.limit : suggest1.data.length + suggest2.data.length;
	    			let leastBless1 = await userDaoV1.leastBlessedRequested(tokenData.userId,params);
    				let leastBless2 = await userDaoV1.leastBlessedReceived(tokenData.userId,params);
			    	step2['leastBlessedCount'] = leastBless1.data.length + leastBless2.data.length > params.limit ? params.limit : leastBless1.data.length + leastBless2.data.length;
					}
			
				}else if(params.type == HOME_LIST.SUGGESTED){
					step2 = await userDaoV1.suggestedRequested(tokenData.userId,params); // get-least-blessed-wishes		
					let step3 = await userDaoV1.suggestedReceived(tokenData.userId,params); // get-least-blessed-wishes	
					step2.data = step2.data.concat(step3.data);
					step2.data = step2.data.splice(0, 8);

				}else{
					step2 = await userDaoV1.leastBlessedRequested(tokenData.userId,params); // get-least-blessed-wishes		
					let step3 = await userDaoV1.leastBlessedReceived(tokenData.userId,params); // get-least-blessed-wishes	
					step2.data = step2.data.concat(step3.data);
					let sortedData = step2.data.sort((a, b) => a.wishDetail.totalBlessings - b.wishDetail.totalBlessings);
					step2.data = sortedData.splice(0, 8);
				}
				await baseDao.updateMany("pinned_wishes", { "userId": tokenData.userId }, { "$set": { status: STATUS.DELETED } }, {});
				let array=[];
				step2.data.forEach(element => {
					let obj ={}
					obj['subjectId']=toObjectId(element.wishDetail._id);  
					obj['userId']=toObjectId(tokenData.userId);
					obj['status']=STATUS.ACTIVE
					array.push(obj);  
				});
				await baseDao.insertMany("pinned_wishes",array,{});
		    }
			if(!step2) step2 =  await wishesDaoV1.pinnedGloablWish();

			return MESSAGES.SUCCESS.WISH_LIST(step2);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function getList2
	 * @description pin wishes list on home page
	 */
	async getListV2(params: UserRequest.Home,tokenData: TokenData) {
		try {
			let step1 = await userDaoV1.findUserById(tokenData.userId); 
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			let step2,data;
			params.limit = 8;
			let array=[],array2=[];
			if(params.isInclude){
				step2 = await userDaoV1.favourite(tokenData.userId,params);
				params.limit = 8-step2.data.length;
				let step3;
				if(params.type == HOME_LIST.SUGGESTED){
					step3 = await userDaoV1.suggestedRequested(tokenData.userId,params); // get-least-blessed-wishes	
					let step4 = await userDaoV1.suggestedReceived(tokenData.userId,params); // get-least-blessed-wishes	
					step3.data = step3.data.concat(step4.data);
					let subArray = await this.randomArray(step3.data,params.limit)
					step3.data = subArray;
				}else{
					step3 = await userDaoV1.leastBlessedRequested(tokenData.userId,params); // get-least-blessed-wishes		
					let step4 = await userDaoV1.leastBlessedReceived(tokenData.userId,params); // get-least-blessed-wishes	
					let step5 =	await wishesDaoV1.pinnedGloablWish();
					step3.data = step3.data.concat(step4.data);
					step3.data = step3.data.concat(step5);
					let sortedData = step3.data.sort((a, b) => a.wishDetail.totalBlessings - b.wishDetail.totalBlessings);
					step3.data = sortedData.splice(0, params.limit);
				}
				// await baseDao.updateMany("pinned_wishes", { "userId": tokenData.userId }, { "$set": { status: STATUS.DELETED } }, {});
				await baseDao.deleteMany("pinned_wishes", { "userId": tokenData.userId }); // New Query (removed completely from DB)
				if(!step2 || step2.total !== 0){
					step2.data.forEach(element => {
						let obj ={}
						obj['subjectId']=toObjectId(element.wishDetail._id);  
						obj['userId']=toObjectId(tokenData.userId);
						obj['status']=STATUS.ACTIVE;
						obj['created']=Date.now();
						obj['createdAt']=new Date();
						array.push(obj);  
						array2.push(element.wishDetail._id);
					});
			    }
				if(!step3 || step3.total !== 0){
					step3.data.forEach(element => {
						let obj ={}
						obj['subjectId']=toObjectId(element.wishDetail._id);  
						obj['userId']=toObjectId(tokenData.userId);
						obj['status']=STATUS.ACTIVE;
						obj['created']=Date.now();
						obj['createdAt']=new Date();
						array.push(obj); 
						array2.push(element.wishDetail._id);
					});
				}
				if(array.length<8 && params.type !== HOME_LIST.FAVOURITE){
					let limit = 8- array.length;
					let globalStep; 
					if(params.type == HOME_LIST.LEAST_BLESSED) globalStep = await wishesDaoV1.pinnedGloablWish(array2,limit,HOME_LIST.LEAST_BLESSED);
					else globalStep = await wishesDaoV1.pinnedGloablWish(array2,limit);
					if(globalStep.total !== 0){
					    globalStep.forEach(element => {
						    let obj ={}
		    				obj['subjectId']=toObjectId(element.wishDetail._id);  
			    			obj['userId']=toObjectId(tokenData.userId);
				    		obj['status']=STATUS.ACTIVE;
							obj['created']=Date.now();
							obj['createdAt']=new Date();
					    	array.push(obj);  
					    });
				    }
				}
				await baseDao.insertMany("pinned_wishes",array,{});

			}else{
				if(params.type == HOME_LIST.FAVOURITE){
					step2 = await userDaoV1.favourite(tokenData.userId,params); // get-favourite-wishes		
					params.limit = 8-step2.total
					if(params.limit > 0){
				    let suggest1 = await userDaoV1.suggestedRequested(tokenData.userId,params);
			    	let suggest2 = await userDaoV1.suggestedReceived(tokenData.userId,params);
		     		step2['suggestedCount'] = suggest1.data.length + suggest2.data.length > params.limit ? params.limit : suggest1.data.length + suggest2.data.length;
	    			let leastBless1 = await userDaoV1.leastBlessedRequested(tokenData.userId,params);
    				let leastBless2 = await userDaoV1.leastBlessedReceived(tokenData.userId,params);
			    	step2['leastBlessedCount'] = leastBless1.data.length + leastBless2.data.length > params.limit ? params.limit : leastBless1.data.length + leastBless2.data.length;
					if(step2['suggestedCount'] == 0){
						step2['suggestedCount'] = await baseDao.countDocuments("wishes", { "isGlobalWish":true, status :{'$eq': STATUS.ACTIVE} });
						if(step2['suggestedCount']>8) step2['suggestedCount'] = 8;
					}else if(step2['suggestedCount'] < 8){
						let count = await baseDao.countDocuments("wishes", { "isGlobalWish":true, status :{'$eq': STATUS.ACTIVE} });
						step2['suggestedCount'] = (step2['suggestedCount']+count)<8?step2['suggestedCount']+count:8
					}
					if(step2['leastBlessedCount'] == 0){
						step2['leastBlessedCount'] = await baseDao.countDocuments("wishes", { "isGlobalWish":true, status :{'$eq': STATUS.ACTIVE} });
						if(step2['leastBlessedCount']>8) step2['leastBlessedCount'] = 8;
					}else if(step2['leastBlessedCount'] < 8){
						let count = await baseDao.countDocuments("wishes", { "isGlobalWish":true, status :{'$eq': STATUS.ACTIVE} });
						step2['leastBlessedCount'] = (step2['leastBlessedCount']+count)<8?step2['leastBlessedCount']+count:8
					}
					}
			
				}else if(params.type == HOME_LIST.SUGGESTED){
					params.limit=100;
					step2 = await userDaoV1.suggestedRequested(tokenData.userId,params); // get-least-blessed-wishes	
					let step3 = await userDaoV1.suggestedReceived(tokenData.userId,params); // get-least-blessed-wishes	
					step2.data = step2.data.concat(step3.data);
					let subArray = await this.randomArray(step2.data)
					//step2.data = step2.data.splice(0, 8);
					step2.data = subArray;
					step2.total = step2.data.length;
				}else{
					params.limit = 100;
					step2 = await userDaoV1.leastBlessedRequested(tokenData.userId,params); // get-least-blessed-wishes		
					let step3 = await userDaoV1.leastBlessedReceived(tokenData.userId,params); // get-least-blessed-wishes	
					let step4 =	await wishesDaoV1.pinnedGloablWish();
					step2.data = step2.data.concat(step3.data);
					step2.data = step2.data.concat(step4);
					let sortedData = step2.data.sort((a, b) => a.wishDetail.totalBlessings - b.wishDetail.totalBlessings);
					step2.data = sortedData.splice(0, 8);
					step2.total = step2.data.length;
				}
				// await baseDao.updateMany("pinned_wishes", { "userId": tokenData.userId }, { "$set": { status: STATUS.DELETED } }, {});
				await baseDao.deleteMany("pinned_wishes", { "userId": tokenData.userId }); // New Query (removed completely from DB)
				if(step2.total !== 0){
					step2.data.forEach(element => {
						let obj ={}
						obj['subjectId']=toObjectId(element.wishDetail._id);  
						obj['userId']=toObjectId(tokenData.userId);
						obj['status']=STATUS.ACTIVE;
						obj['created']=Date.now();
						obj['createdAt']=new Date();
						array.push(obj); 
						array2.push(element.wishDetail._id);
					});
			    }		
				if(array.length<8 && params.type !== HOME_LIST.FAVOURITE){
					let limit = 8- array.length;
					let globalStep;
					if(params.type == HOME_LIST.LEAST_BLESSED) globalStep = await wishesDaoV1.pinnedGloablWish(array2,limit,HOME_LIST.LEAST_BLESSED);
					else globalStep = await wishesDaoV1.pinnedGloablWish(array2,limit);
					step2.data = step2.data.concat(globalStep);
					step2.total = step2.data.length;
					globalStep.forEach(element => {
						let obj ={}
						obj['subjectId']=toObjectId(element.wishDetail._id);  
						obj['userId']=toObjectId(tokenData.userId);
						obj['status']=STATUS.ACTIVE;
						obj['created']=Date.now();
						obj['createdAt']=new Date();
						array.push(obj); 
					});
				}
				data =await baseDao.insertMany("pinned_wishes",array,{});
		    }
			if((step2.total == 0) && (params.type !== HOME_LIST.FAVOURITE)) step2 =  await wishesDaoV1.pinnedGloablWish();
			return MESSAGES.SUCCESS.WISH_LIST(step2);
		} catch (error) {
			throw error;
		}
	}

	/**
   	* @function editProfile
   	* <AUTHOR>
	   async editUserProfile( params: UserRequest.EditProfile, tokenData: TokenData) {
		try {
			let step1 = await userDaoV1.findUserById(tokenData.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			if(params.phoneNumber){
				let step2 = await userDaoV1.isMobileExists(params,tokenData.userId)
				if(step2) return Promise.reject(MESSAGES.ERROR.MOBILE_NO_ALREADY_EXIST);
			}
			let data = await userDaoV1.editUserProfile(params, tokenData.userId);
			let deviceContact = await baseDao.find("contacts",{userId: step1._id,deviceId:params.deviceId},{_id:1,userId:1,deviceId:1},{});
			data.isContactSynced = (deviceContact && deviceContact.length)?true:false;
			// total bless receivied on my wishes
			let receiverIdBless = await wishesDaoV1.totalBlessReceived(step1._id);
			if (receiverIdBless && receiverIdBless.length) data.receiverIdBlessCount = receiverIdBless[0].totalWishbless;
			else data.receiverIdBlessCount = 0;
			// total listen for bless in seconds
			let listenCount = await blessingDaoV1.totalBlessListen(step1._id, false, params?.offset, false);
			if (listenCount && listenCount.length) data.totalListenBless = listenCount[0].totalListenBlessTime;
			else data.totalListenBless = 0;
			// get total grtiude on my bless
			let allBless = await baseDao.distinct("perform_blessings","_id", { userId:step1._id });
			let gratitudesCount = 0;
			gratitudesCount = await baseDao.countDocuments("gratitudes", { blessingId: { "$in": allBless } });
			data.gratitudesCount = gratitudesCount;
			return MESSAGES.SUCCESS.USER_PROFILE_UPDATED(data);
		} catch (error) {
			throw error;
		}
	}

	/**
   	* @function deleteAccount
   	* <AUTHOR> record status update with delete status for this user
   	*/
	   async deleteAccount(tokenData: TokenData) {
		try {
		let step1 = await userDaoV1.findUserById(tokenData.userId); 
		if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
		if(tokenData.deleteReason)step1.deleteReason = tokenData.deleteReason;
		if (step1) {
			if (step1.status == "UN_BLOCKED") {
				commonControllerV1.deleteAccountScript(step1,USER_TYPE.USER);
				await this.removeSession(tokenData, SERVER.IS_SINGLE_DEVICE_LOGIN[USER_TYPE.USER]);
				return MESSAGES.SUCCESS.DELETE_USER
			}
		  } else {
			return MESSAGES.ERROR.USER_NOT_FOUND;
		  }
		
		
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function wishwellThanks
	 * @description thanks Wishwell by  user
	 */
	async wishwellThanks(params: UserRequest.WishwellThanks,tokenData: TokenData) {
		try {
			let step1 = await userDaoV1.findUserById(tokenData.userId); 
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			//payment status
			let paymentIntentDetail;
			if(params.paymentIntentid){
			 paymentIntentDetail = await stripe.paymentIntents.retrieve(params.paymentIntentid);
			 await baseDao.updateOne("payment_intents",{_id:params.intentId,paymentIntentid:params.paymentIntentid},{status:paymentIntentDetail.status},{});
		    }
			let data = await commonDao.wishwellThanksSave(params,tokenData,paymentIntentDetail);
			//sent mail to admin
			if(params.amount>0){
				// mailManager.thanksWishwellMail(step1,params);
				awsSQS.signupMagicLinkProducer({  data:step1, params: params, type:SQS_TYPES.WISHWELL_THANKS});

			}
			if(!data)return MESSAGES.ERROR.SOMETHING_WENT_WRONG;
			return MESSAGES.SUCCESS.THANKS_WISHWELL;
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function thankWishwellListing
	 * @description admin thank wishwell listing
	 */
	async thankWishwellListing(params: UserRequest.ThankWishwellListing) {
		try {
			const step1 = await commonDao.thankWishwellListing(params);
			if(params.userId && params.type == THANK_WISHWELL_LISTING_TYPE.USER) {
				const step2 = await userDaoV1.findUserById(params.userId, { _id: 1, firstName: 1, lastName: 1 });
				step1["userDetails"] = step2;
			}
			return MESSAGES.SUCCESS.THANKS_WISHWELL_LISTING(step1);
		} catch (error) {
			throw error;
		}
	}


	/**
   	* @function randomArray
   	* <AUTHOR>
	   async randomArray( array, limit?) {
		try {
			let subArray = [];
			let subArrayLength = limit?limit:7;
			let index=[];
			while (subArray.length <= subArrayLength) {
				if(subArray.length == array.length) break;
				let randomIndex = Math.floor(Math.random() * array.length);
				if(!index.includes(randomIndex)){
					index.push(randomIndex);
					subArray.push(array[randomIndex]);
				}
			  }
			return subArray;
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function forgotPassword
	 */
	async forgotPassword(params: UserRequest.ForgotPassword) {
		try {
			const isExist = await userDaoV1.isEmailExists(params); // check is email exist if not then restrict to send forgot password mail
			if (!isExist) return Promise.reject(MESSAGES.ERROR.USER_EMAIL_NOT_REGISTERED);
			let step = await userDaoV1.deleteEmailCheck(params);
			if(step){
				if(step.status == STATUS.DELETED && step.isDeletedBy == USER_TYPE.ADMIN) return Promise.reject(MESSAGES.ERROR.USER_DELETED_BY_ADMIN);
				if(step.status == STATUS.BLOCKED) {
					let reason = step?.actionSummary.slice().reverse().find(obj => obj.status == STATUS.BLOCKED).reason.trim();
					reason = reason.length ? ` Reason: ${reason}` : "";
					return Promise.reject(MESSAGES.ERROR.USER_DEACTIVATED_BY_ADMIN(reason));
				}
			}
			if(isExist.isEmailVerified == false) {
				return Promise.reject(MESSAGES.ERROR.EMAIL_NOT_VERIFIED_YET);
			}
			if(isExist.isPasswordSet == false) {
				return Promise.reject(MESSAGES.ERROR.PASSWORD_NOT_SET_YET);
			}
			const randomString = genRandomString(64);
			const resetLink = `${SERVER.APP_URL}${SERVER.API_BASE_URL}/v1/common/deeplink?type=${DEEPLINK_TYPE.FORGOT_PASSWORD}&token=${randomString}`;  // deeplinking
			if (SERVER.IS_REDIS_ENABLE) redisClient.setExp(params.email+"_"+DEEPLINK_TYPE.FORGOT_PASSWORD, (SERVER.TOKEN_INFO.EXPIRATION_TIME.VERIFY_EMAIL / 1000), JSON.stringify({ "email": params.email, "randomString": randomString }));
			await baseDao.updateOne("users", { _id: isExist._id }, { $set: { passwordResetToken: randomString } }, {});
			mailManager.forgotPasswordMail({ "email": params.email, "name": `${isExist.firstName == undefined ? "" : isExist.firstName} ${isExist.lastName == undefined ? "" : isExist.lastName}`, "url": resetLink });
			return MESSAGES.SUCCESS.FORGOT_PASSWORD_EMAIL_SENT({
				isEmailVerified: isExist.isEmailVerified,
				isPasswordSet: isExist.isPasswordSet,
				isProfileComplete: isExist.isProfileComplete
			});
		} catch (error) {
			throw error;
		}
	}


	/**
	 * @function resetPassword
	 */
	async resetPassword(params: UserRequest.ChangeForgotPassword) {
		try {
			const step1 = await userDaoV1.findUserByHash(params.resetToken);
			if (!step1) return Promise.reject(MESSAGES.ERROR.INVALID_RESET_PASSWORD_TOKEN);
			if (params.newPassword !== params.confirmPassword) return Promise.reject(MESSAGES.ERROR.NEW_CONFIRM_PASSWORD);
			const salt = await genRandomString(SERVER.SALT_ROUNDS);
			params.hash = await encryptHashPassword(params.newPassword, salt);
			params.salt = salt;
			await userDaoV1.changePassword(params);
			await baseDao.updateOne("users", { _id: step1._id }, { $unset: { passwordResetToken: "" } }, {});
			return MESSAGES.SUCCESS.RESET_PASSWORD;
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function changePassword
	 */
	async changePassword(params: UserRequest.ChangeForgotPassword, tokenData: TokenData) {
		try {
			const step1 = await userDaoV1.findUserById(tokenData.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			if (params.newPassword !== params.confirmPassword) return Promise.reject(MESSAGES.ERROR.NEW_CONFIRM_PASSWORD);
			const checkPassword = await matchPassword(params.newPassword, step1.hash, step1.salt);
			if(checkPassword == true) return Promise.reject(MESSAGES.ERROR.SAME_OLD_PASSWORD);
			const salt = await genRandomString(SERVER.SALT_ROUNDS);
			params.hash = await encryptHashPassword(params.newPassword, salt);
			params.salt = salt;
			await userDaoV1.changePassword(params, tokenData);
			return MESSAGES.SUCCESS.CHANGE_PASSWORD_USER;
		} catch (error) {
			throw error;
		}
	}


	// * @function setPassword
	// * @description set user password after signup & verify email
	// * @param params.password: user's password (required)
   async setPassword(params: UserRequest.SetPassword, tokenData: TokenData) {
	   try {
		   const isExists = await userDaoV1.findUserById(tokenData.userId); 
		   if (!isExists) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
		   if(isExists.isPasswordSet && isExists.isPasswordSet == true) {
			return Promise.reject(MESSAGES.ERROR.PASSWORD_ALREADY_SET);
		   }
		   const salt = genRandomString(SERVER.SALT_ROUNDS);
		   params.hash = encryptHashPassword(params.password, salt);
		   params.salt = salt;
		   const step1 = await userDaoV1.setPassword(params, tokenData);
		   let isContactSynced = false;
		   if(params.deviceId) {
			const deviceContact = await baseDao.find("contacts",{userId: isExists._id,deviceId:params.deviceId},{_id:1,userId:1,deviceId:1},{});
			if (deviceContact && deviceContact.length) isContactSynced = true;
		   }
		   return MESSAGES.SUCCESS.USER_PASSWORD_SET({
				_id: isExists._id,
				email: isExists.email,
				"firstName": isExists?.firstName,
				"lastName": isExists?.lastName,
				//"isContactSynced": isExist?.isContactSynced,
				"isContactSynced":isContactSynced,
				"loginType":isExists?.loginType,
				"profilePicture":isExists?.profilePicture,
				"socialData":isExists?.socialData,
				"pushNotificationStatus":isExists?.pushNotificationStatus,
				"communityNotificationStatus": isExists?.communityNotificationStatus,
				"wishesNotificationStatus":isExists?.wishesNotificationStatus,
				"gratitudeNotificationStatus":isExists?.gratitudeNotificationStatus,
				"perferMedidation":isExists?.perferMedidation, 
				"preferPrayer":isExists?.preferPrayer,
				"locationSharing":isExists?.locationSharing, 
				"offerBlessing":isExists?.offerBlessing,
				"countryFlagCode":isExists?.countryFlagCode,
				"intension":isExists?.intension,
				"completeTooltip":isExists?.completeTooltip,
				"isEmailVerified":isExists?.isEmailVerified,
				"isPasswordSet":isExists?.isPasswordSet,
				"isProfileComplete": isExists?.isProfileComplete
		   });
	   } catch (error) {
		   throw error;
	   }
	}


	/**
	 * @function matchPassword
	 */
	async matchPassword(params: UserRequest.matchPassword, tokenData: TokenData) {
		try {
			const isExist = await userDaoV1.findUserById(tokenData.userId); 
			if (!isExist) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			const isPasswordMatched = await matchPassword(params.password, isExist.hash, isExist.salt);
			if(isPasswordMatched){
				return MESSAGES.SUCCESS.MATCH_PASSWORD;
			} else {
				return Promise.reject(MESSAGES.ERROR.INCORRECT_PASSWORD_USER);
			}
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function verifyLoginEmail
	 * @description verify login email
	 * @param params.email: user's email (required)
	 */
	async verifyLoginEmail(params: UserRequest.Login) {
		try {
			const isExists = await userDaoV1.isEmailExists(params);
			if(!isExists) return Promise.reject(MESSAGES.ERROR.USER_EMAIL_NOT_REGISTERED);
			let step = await userDaoV1.deleteEmailCheck(params);
			if(step){
				if(step.status == STATUS.DELETED && step.isDeletedBy == USER_TYPE.ADMIN) return Promise.reject(MESSAGES.ERROR.USER_DELETED_BY_ADMIN);
				if(step.status == STATUS.BLOCKED) {
					let reason = step?.actionSummary.slice().reverse().find(obj => obj.status == STATUS.BLOCKED).reason.trim();
					reason = reason.length ? ` Reason: ${reason}` : "";
					return Promise.reject(MESSAGES.ERROR.USER_DEACTIVATED_BY_ADMIN(reason));
				}
			}
			if(isExists.loginType == LOGIN_TYPE.NORMAL) {
				if(isExists.isEmailVerified == false) {
					return Promise.reject(MESSAGES.ERROR.EMAIL_NOT_VERIFIED_YET);
				}
				if(isExists.isPasswordSet == false) {
					return Promise.reject(MESSAGES.ERROR.PASSWORD_NOT_SET_YET);
				}
				return MESSAGES.SUCCESS.EMAIL_EXISTS({
					loginType: isExists.loginType,
					isEmailVerified: isExists.isEmailVerified,
					isPasswordSet: isExists.isPasswordSet,
					isProfileComplete: isExists.isProfileComplete
				});
			} else {
				if(isExists.isPasswordSet == false) {
					params.userType = USER_TYPE.USER;
					await this.removeSession({ "userId": isExists._id, "deviceId": params.deviceId }, true);
					const salt = crypto.randomBytes(64).toString("hex");
					const tokenData = {
						"userId": isExists._id,
						"deviceId": params.deviceId,
						"accessTokenKey": salt,
						"type": TOKEN_TYPE.USER_LOGIN,
						"userType": params.userType
					};
					await baseDao.updateMany("login_histories", { 'deviceToken': params.deviceToken, 'isLogin': true }, { 'arn': "", isLogin:false }, {});
					const [step2, accessToken] = await promise.join(
						loginHistoryDao.createUserLoginHistory({ ...isExists, ...params, salt }),
						createToken(tokenData)
					);
					if (SERVER.IS_REDIS_ENABLE) redisClient.setExp(`${isExists._id.toString()}.${params.deviceId}`, Math.floor(SERVER.TOKEN_INFO.EXPIRATION_TIME[TOKEN_TYPE.USER_LOGIN] / 1000), JSON.stringify(buildToken({ ...isExists, ...params, salt })));
					await notificationManager.subscribeDeviceTokenViaAWS({token: params.deviceToken, userId: isExists._id});
					let deviceContact = await baseDao.find("contacts",{userId: isExists._id,deviceId:params.deviceId},{_id:1,userId:1,deviceId:1},{});
					return MESSAGES.SUCCESS.DETAILS({
						accessToken,
						_id: isExists._id,
						email: isExists.email,
						"firstName": isExists?.firstName,
						"lastName": isExists?.lastName,
						//"isContactSynced": isExist?.isContactSynced,
						"isContactSynced":(deviceContact && deviceContact.length)?true:false,
						"loginType":isExists?.loginType,
						"profilePicture":isExists?.profilePicture,
						"socialData":isExists?.socialData,
						"pushNotificationStatus":isExists?.pushNotificationStatus,
						"communityNotificationStatus": isExists?.communityNotificationStatus,
						"wishesNotificationStatus":isExists?.wishesNotificationStatus,
						"gratitudeNotificationStatus":isExists?.gratitudeNotificationStatus,
						"perferMedidation":isExists?.perferMedidation, 
						"preferPrayer":isExists?.preferPrayer,
						"locationSharing":isExists?.locationSharing, 
						"offerBlessing":isExists?.offerBlessing,
						"countryFlagCode":isExists?.countryFlagCode,
						"intension":isExists?.intension,
						"completeTooltip":isExists?.completeTooltip,
						"isEmailVerified":isExists?.isEmailVerified,
						"isPasswordSet":isExists?.isPasswordSet,
						"isProfileComplete": isExists?.isProfileComplete
					});
				} else {
					return MESSAGES.SUCCESS.EMAIL_EXISTS({
						loginType: isExists.loginType,
						isEmailVerified: isExists.isEmailVerified,
						isPasswordSet: isExists.isPasswordSet,
						isProfileComplete: isExists.isProfileComplete
					});
				}
			}
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function login
	 * @description user login
	 * @param params.email: user's email (required)
	 */
	async login(params: UserRequest.Login) {
		try {
			const isExist = await userDaoV1.isEmailExists(params);
			if(!isExist) return Promise.reject(MESSAGES.ERROR.USER_EMAIL_NOT_REGISTERED);
			let step = await userDaoV1.deleteEmailCheck(params);
			if(step){
				if(step.status == STATUS.DELETED && step.isDeletedBy == USER_TYPE.ADMIN) return Promise.reject(MESSAGES.ERROR.USER_DELETED_BY_ADMIN);
				if(step.status == STATUS.BLOCKED) {
					let reason = step?.actionSummary.slice().reverse().find(obj => obj.status == STATUS.BLOCKED).reason.trim();
					reason = reason.length ? ` Reason: ${reason}` : "";
					return Promise.reject(MESSAGES.ERROR.USER_DEACTIVATED_BY_ADMIN(reason));
				}
			}
			const checkPassword = await matchPassword(params.password, isExist.hash, isExist.salt);
			if(checkPassword) {
				params.userType = USER_TYPE.USER;
				await this.removeSession({ "userId": isExist._id, "deviceId": params.deviceId }, true);
				const salt = crypto.randomBytes(64).toString("hex");
				const tokenData = {
					"userId": isExist._id,
					"deviceId": params.deviceId,
					"accessTokenKey": salt,
					"type": TOKEN_TYPE.USER_LOGIN,
					"userType": params.userType
				};
				await baseDao.updateMany("login_histories", { 'deviceToken': params.deviceToken, 'isLogin': true }, { 'arn': "", isLogin:false }, {});

				const [step2, accessToken] = await promise.join(
					loginHistoryDao.createUserLoginHistory({ ...isExist, ...params, salt }),
					createToken(tokenData)
				);
				if (SERVER.IS_REDIS_ENABLE) redisClient.setExp(`${isExist._id.toString()}.${params.deviceId}`, Math.floor(SERVER.TOKEN_INFO.EXPIRATION_TIME[TOKEN_TYPE.USER_LOGIN] / 1000), JSON.stringify(buildToken({ ...isExist, ...params, salt })));
				await notificationManager.subscribeDeviceTokenViaAWS({token: params.deviceToken, userId: isExist._id});
				//check contact sync with deviceId
				let deviceContact = await baseDao.find("contacts",{userId: isExist._id,deviceId:params.deviceId},{_id:1,userId:1,deviceId:1},{});
				await baseDao.updateOne("users", { _id: isExist._id }, { loginType: LOGIN_TYPE.NORMAL }, {});
				return MESSAGES.SUCCESS.LOGIN({
					accessToken,
					_id: isExist._id,
					email: isExist.email,
					"firstName": isExist?.firstName,
					"lastName": isExist?.lastName,
					//"isContactSynced": isExist?.isContactSynced,
					"isContactSynced":(deviceContact && deviceContact.length)?true:false,
					"loginType":LOGIN_TYPE.NORMAL,
					"profilePicture":isExist?.profilePicture,
					"socialData":isExist?.socialData,
					"pushNotificationStatus":isExist?.pushNotificationStatus,
					"communityNotificationStatus": isExist?.communityNotificationStatus,
					"wishesNotificationStatus":isExist?.wishesNotificationStatus,
					"gratitudeNotificationStatus":isExist?.gratitudeNotificationStatus,
					"perferMedidation":isExist?.perferMedidation, 
					"preferPrayer":isExist?.preferPrayer,
					"locationSharing":isExist?.locationSharing, 
					"offerBlessing":isExist?.offerBlessing,
					"countryFlagCode":isExist?.countryFlagCode,
					"intension":isExist?.intension,
					"completeTooltip":isExist?.completeTooltip,
					"isEmailVerified":isExist?.isEmailVerified,
					"isPasswordSet":isExist?.isPasswordSet,
					"isProfileComplete": isExist?.isProfileComplete
				});
			} else {
				return Promise.reject(MESSAGES.ERROR.INCORRECT_PASSWORD_USER);
			}
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function signUpWithoutVerification
	 * @description Sign up a user without email verification (V3)
	 */
	async signUpWithoutVerification(params: UserRequest.SignUp) {
		const session = await mongoose.startSession();
		session.startTransaction();

		try {
			params.userType = USER_TYPE.USER;

			// Check if email already exists
			const isExist = await userDaoV1.isEmailExists(params);
			const deletedOrBlocked = await userDaoV1.deleteEmailCheck(params);

			if (deletedOrBlocked) {
				if (deletedOrBlocked.status === STATUS.DELETED && deletedOrBlocked.isDeletedBy === USER_TYPE.ADMIN)
					return Promise.reject(MESSAGES.ERROR.USER_DELETED_BY_ADMIN);

				if (deletedOrBlocked.status === STATUS.BLOCKED) {
					let reason = deletedOrBlocked?.actionSummary
						.slice()
						.reverse()
						.find(obj => obj.status === STATUS.BLOCKED)?.reason?.trim() || "";
					return Promise.reject(MESSAGES.ERROR.USER_DEACTIVATED_BY_ADMIN(reason ? ` Reason: ${reason}` : ""));
				}
			}

			if (isExist && isExist.isPasswordSet && isExist.loginType === LOGIN_TYPE.NORMAL) {
				return Promise.reject(MESSAGES.ERROR.EMAIL_ALREADY_EXIST);
			}

			let step1 = isExist;
			if (!isExist) {
				step1 = await userDaoV1.signUp(params, session);
				await userDaoV1.updateOne("users", { _id: step1._id }, {
					isEmailVerified: true,
					isPasswordSet: true,
				}, {});
			} else {
				await userDaoV1.updateOne("users", { _id: isExist._id }, {
					isEmailVerified: true,
					isPasswordSet: true,
				}, {});
			}

			// Generate salt and access token
			const salt = crypto.randomBytes(64).toString("hex");
			const tokenData = {
				userId: step1._id,
				deviceId: params.deviceId,
				accessTokenKey: salt,
				type: TOKEN_TYPE.USER_LOGIN,
				userType: USER_TYPE.USER
			};

			await baseDao.updateMany("login_histories", { deviceToken: params.deviceToken, isLogin: true }, { arn: "", isLogin: false }, {});

			const [_, accessToken] = await promise.join(
				loginHistoryDao.createUserLoginHistory({ ...step1, ...params, salt }),
				createToken(tokenData)
			);

			if (SERVER.IS_REDIS_ENABLE) {
				await redisClient.setExp(`${step1._id.toString()}.${params.deviceId}`,
					Math.floor(SERVER.TOKEN_INFO.EXPIRATION_TIME[TOKEN_TYPE.USER_LOGIN] / 1000),
					JSON.stringify(buildToken({ ...step1, ...params, salt }))
				);
			}

			await session.commitTransaction();
			session.endSession();

			await notificationManager.subscribeDeviceTokenViaAWS({ token: params.deviceToken, userId: step1._id });

			return MESSAGES.SUCCESS.SIGNUP({
				accessToken,
				_id: step1._id,
				email: step1.email,
				firstName: step1?.firstName,
				lastName: step1?.lastName,
				isContactSynced: false,
				loginType: LOGIN_TYPE.NORMAL,
				profilePicture: step1?.profilePicture,
				socialData: step1?.socialData,
				pushNotificationStatus: step1?.pushNotificationStatus,
				communityNotificationStatus: step1?.communityNotificationStatus,
				wishesNotificationStatus: step1?.wishesNotificationStatus,
				gratitudeNotificationStatus: step1?.gratitudeNotificationStatus,
				perferMedidation: step1?.perferMedidation,
				preferPrayer: step1?.preferPrayer,
				locationSharing: step1?.locationSharing,
				offerBlessing: step1?.offerBlessing,
				countryFlagCode: step1?.countryFlagCode,
				intension: step1?.intension,
				completeTooltip: step1?.completeTooltip,
				isEmailVerified: step1?.isEmailVerified,
				isPasswordSet: step1?.isPasswordSet,
				isProfileComplete: step1?.isProfileComplete
			});

		} catch (error) {
			await session.abortTransaction();
			session.endSession();
			throw error;
		}
	}

	// _formatNumber (dialCode = null, phoneNumber) {
	// 	try {
	// 		const DEFAULT_REGION = "US";
	
	// 		// Ensure phoneNumber is a string
	// 		phoneNumber = phoneNumber.toString();
	
	// 		// Remove any existing prefixes (leading '0's)
	// 		phoneNumber = phoneNumber.replace(/^0+/, '');
	
	// 		let regionCode;
	// 		if (dialCode) {
	// 			// Remove the '+' from the dialing code if present
	// 			const cleanedDialCode = dialCode.replace(/\D/g, '');
	
	// 			// Get the ISO 3166-1 alpha-2 country code from the dialing code
	// 			regionCode = phoneUtil.getRegionCodeForCountryCode(parseInt(cleanedDialCode));
	// 			console.log(`Dialing code: ${dialCode}, Region code: ${regionCode}`);
	// 		}
	
	// 		let formattedNumber;
	// 		if (regionCode) {
	// 			// Parse the phone number with the given country code (ISO 3166-1 alpha-2)
	// 			const number = phoneUtil.parseAndKeepRawInput(phoneNumber, regionCode);
	// 			console.log(`Parsed number: ${JSON.stringify(number)}`);
	// 			formattedNumber = phoneUtil.format(number, PNF.INTERNATIONAL);
	// 		} else {
	// 			// Parse the phone number with the default region (ISO 3166-1 alpha-2 country code)
	// 			const number = phoneUtil.parseAndKeepRawInput(phoneNumber, DEFAULT_REGION);
	// 			console.log(`Parsed number with default region: ${JSON.stringify(number)}`);
	// 			formattedNumber = phoneUtil.format(number, PNF.INTERNATIONAL);
	// 		}
	
	// 		console.log(`Formatted number: ${formattedNumber}`);
	// 		return formattedNumber;
	// 	} catch (error) {
	// 		console.error("Error parsing phone number:", error);
	// 		return phoneNumber; // Return the original phone number in case of an error
	// 	}
	// }

	_processPhoneNumber(phoneCodes, phoneNumber) {
		// Iterate over each phone code in the array
		for (let codeObj of phoneCodes) {
			let code = codeObj.phone_code;
			
			// Normalize the code by removing spaces and dashes
			let normalizedCode = code.replace(/[\s-]/g, '');

			// If the normalized code is a blank string, continue to the next loop iteration
			if (normalizedCode === '') continue;
			
			// Check if the phone number starts with the current phone code
			if (phoneNumber.startsWith(normalizedCode)) {
				// Extract the remaining part of the phone number
				let remainingNumber = phoneNumber.slice(normalizedCode.length);
				// Check if the remaining part is exactly 9 digits
				if (/^\d{9}$/.test(remainingNumber)) {
					// Append 0 to the remaining number and recombine with the phone code
					return normalizedCode + '0' + remainingNumber;
				} else if (/^\d{10,}$/.test(remainingNumber)) {
					// If the remaining number is more than 9 digits, return the phone number as is
					return phoneNumber;
				}
			}
		}
		
		// If no matching phone code is found, check the length of the phone number
		if (/^\d{9}$/.test(phoneNumber)) {
			// If the phone number is 9 digits, append 0 to it
			return '0' + phoneNumber;
		} else if (/^\d{10,}$/.test(phoneNumber)) {
			// If the phone number is more than 9 digits, return it as is
			return phoneNumber;
		}
		
		// If the phone number does not match any of the above criteria, return phone number as is
		return phoneNumber;
	}

	_formatNumber(_, phoneNumber) {
		try {
			// Ensure phoneNumber is a string
			phoneNumber = phoneNumber.toString();
	
			// Remove any existing prefixes (leading '0's)
			phoneNumber = phoneNumber.replace(/^0+/, '');
	
			let number;
			if (phoneNumber.startsWith('+')) {
				// The phone number includes a dialing code
				number = phoneUtil.parseAndKeepRawInput(phoneNumber, null);
			} else {
				// Try to parse the number with an empty region code, which will use heuristics
				number = phoneUtil.parseAndKeepRawInput(phoneNumber, '');
			}
	
			console.log(`Parsed number: ${JSON.stringify(number)}`);
	
			const formattedNumber = phoneUtil.format(number, PNF.INTERNATIONAL);
			console.log(`Formatted number: ${formattedNumber}`);
			return formattedNumber;
	
		} catch (error) {
			console.error("Error parsing phone number:", error);
			return phoneNumber; // Return the original phone number in case of an error
		}
	}
	

}

export const userController = new UserController();