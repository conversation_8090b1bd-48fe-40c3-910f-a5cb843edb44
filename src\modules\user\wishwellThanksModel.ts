"use strict";
const mongoose = require("mongoose");
import { Document, model, Model, Schema } from "mongoose";
import {
	DB_MODEL_REF,
	STATUS,
    BLESSING_PERFORM_TYPE
} from "@config/index";
export interface IWishwellThanks extends Document {   
	serialNumber: number;
    userId:string;
	note?:string;
    audio?:string;
    isAllowShare:boolean;
    amount?:number;
	status: string;
	created: number;
}
const wishwellThanksSchema: Schema = new mongoose.Schema({
	_id: { type: Schema.Types.ObjectId, required: true, auto: true },
	serialNumber: { type: Number, required: false },
	userId: { type: Schema.Types.ObjectId, required: true },
    type:{type:String,required:true,enum:[BLESSING_PERFORM_TYPE.AUDIO, BLESSING_PERFORM_TYPE.TEXT]},
	note: { type: String, trim: true, required: false }, 
	audio: { type: String, trim: true, required: false }, 
    isAllowShare:{type:Boolean,required:false},
    amount:{type:Number,required:false},
	paymentStatus:{type:String,required:false},
	intentId:{type:Schema.Types.ObjectId,required: false},//payment_intent model _id 
	status: {
		type: String,
		enum: [STATUS.BLOCKED, STATUS.UN_BLOCKED, STATUS.DELETED],
		default: STATUS.UN_BLOCKED,
		required: true
	},
	created: { type: Number, default: Date.now }
}, {
	versionKey: false,
	timestamps: true
});
wishwellThanksSchema.index({ status: 1 });
wishwellThanksSchema.index({ created: -1 });

wishwellThanksSchema.pre<IWishwellThanks>('save', async function (next) {
    if (this.isNew) {
        const lastEntry = await wishwell_thanks.findOne({}, {}, { sort: { createdAt: -1 } });
        const lastKeyValue = lastEntry ? (lastEntry.serialNumber == undefined) ? 0 : lastEntry.serialNumber : 0;
        this.serialNumber = lastKeyValue + 1;
    }
    next();
});

// Export user
export const wishwell_thanks: Model<IWishwellThanks> = model<IWishwellThanks>(DB_MODEL_REF.WISHWELL_THANKS, wishwellThanksSchema);