declare interface UserId {
	userId: string;
	deviceId?:string;
	deviceToken?: string;
	offset?: number;
}

declare interface Device {
	platform?: string;
	deviceId?: string;
	deviceToken?: string;
	accessToken?: string;
	remoteAddress?: string;
	salt?: string;
	timezone?: number;
	language?: string;
}

declare interface JwtPayload {
	iss: string;
	aud: string;
	sub: string;
	deviceId?: string;
	iat: number;
	exp: number;
	prm: string;
}

declare interface TokenData extends Device, UserId {
	name?: string;
	firstName?: string;
	lastName?: string;
	email?: string;
	countryCode?: string;
	mobileNo?: string;
	userType?: string;
	// status?: number;
	isApproved?: boolean;
	profileSteps?: string[];
	profilePicture?: string;
	created?: number;
	deleteReason?:string;
}

declare interface ChangePasswordRequest {
	password: string;
	oldPassword: string;
	salt?: string;
	hash?: string;
}

declare interface ComposeMail {
	email: string;
	subject: string;
	message: string;
	name: string;
}

declare interface Pagination {
	pageNo?: number;
	limit?: number;
}

declare interface Filter {
	searchKey: string;
	sortBy: string;
	sortOrder: number | string;
	status?: string;
	fromDate?: number | Date;
	toDate?: number | Date;
	type?: string;
}

declare interface ListingRequest extends Pagination, Filter {
	timezone?: string;
	type?:string
	wishId?:string;
	deviceId?:string;
	skip?: number;
	limit?: number;
}

declare interface BlockRequest {
	status: string;
	userId: string;
	reason?: string;
}

declare interface DeeplinkRequest {
	android?: string;
	ios?: string;
	fallback?: string;
	token: string;
	name: string;
	type?: string;
	userType?: string;
	jwt?: string;
	id?:string;
	wid?:string;
	hid?:string;
	uid?:string;
	email?:string;
}

declare interface UpdateAppNotification {
	type: number;
}

declare interface GeoLocation {
	type: string;
	address: string;
	coordinates: number[];
}

declare interface VerifyOTP {
	email: string;
	otp: string;
}

interface Categories {
	_id: string;
	name: string;
}

interface Interests {
	_id: string;
	name: string;
	image: string;
}

interface PeopleInvolved {
	_id: string;
	name: string;
	profilePicture?: string;
	userType: string;
}

interface Witnesses {
	name: string;
	mobileNo?: string;
	email?: string;
}

interface Attendees {
	_id: string;
	name: string;
	profilePicture?: string;
	userType: string;
	status?: string;
}

interface Categories {
	_id: string;
	name: string;
}

interface Notes {
	_id?: string;
	userId: string;
	name: string;
	profilePicture?: string;
	userType: string;
	mediaType: string;
	text?: string;
	audioUrl?: string;
	declinedReason?: string;
	status: string;
	created: number;
}

interface Dropdown extends ListingRequest{
	type: string;
	countryId?: string;
	stateId?: string;

}

// Model Type For DAO manager
declare type ModelNames =
	"friends" |
	"admins" |
	"categories" |
	"contents" |
	"login_histories" |
	"notifications" |
	"ratings" |
	"users" |
	"versions" |
	"supportchats" |
	"admin_notifications"|
	"admin_banners"|
	"roles"|
	"wishes"|
	"blessing_library" |
	"contacts"|
	"contacts_v2"|
	"tagwishes"|
	"favourites"|
	"pinned_wishes"|
	"reports"|
	"countries"|
	"cities"|
	"states"|
	"communities"|
	"members"|
	"perform_blessings"|
	"well_logs"|
	"gratitudes"|
	"wishwell_thanks" |
	"payment_intents" |
	"payment_webhooks"|
	"locations"|
	"most_used_locations" |
	"timezones" |
	"reminders" |
	"disaster_categories"
