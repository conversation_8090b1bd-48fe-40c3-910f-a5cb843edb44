"use strict";

import * as mongoose from "mongoose";
import { Schema, Model, Document } from "mongoose";

import {  DB_MODEL_REF } from "@config/index";

export interface ICity extends Document {
    city_code: string,
    country_code: string,
    state_code :string,
    city_name : string,
    timezoneid : string
}

const citySchema = new Schema({
    _id: { type: mongoose.Schema.Types.ObjectId, required: true, auto: true },
    city_code: { type: String },
    state_code :{type: String, default:'', index: true},
    city_name :{type: String, default:''},
    country_code :{type: String, default:'', index: true},
    timezoneid : {type: String, default:''}
}, {
	versionKey: false,
	timestamps: true,
	
});

// Export cities
export const cities: Model<ICity> = mongoose.model<ICity>(DB_MODEL_REF.CITIES, citySchema);