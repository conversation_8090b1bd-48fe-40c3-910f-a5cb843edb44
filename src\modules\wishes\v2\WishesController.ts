"use strict";
import { MESSAGES, STATUS, WISH_TAG_TYPE, WISH_TYPE, REPORT_TYPE, GEO_LOCATION_TYPE, NOTIFICATION_TYPE, MESSAGES_NOTIFICATION, TYPE_NOTIFICATION, TITLE_NOTIFICATION } from "@config/constant";
import { SERVER, DEEPLINK_TYPE } from "@config/index";
import { tagwishes, wishesDaoV2, wishesDaoV1 } from "@modules/wishes/index";
import { userDaoV1 } from "@modules/user/index";
import { reportDaoV1 } from "@modules/reports/index";
import { toObjectId } from "@utils/appUtils";
import * as mongoose from "mongoose";
import { baseDao } from "@modules/baseDao/index";
import { redisClient } from "@lib/redis/RedisClient";
import { notificationManager } from "../../../utils/NotificationManager"
import { awsSQS } from "@lib/AwsSqs";
import { commonControllerV1 } from "@modules/common/index";
import { remindersDao } from "@modules/wishesReminder";
const fetch = require('node-fetch');

export class WishesController {
	/**
	 * @function addWishes
	 * @description here are adding the wishes.
	 */
	async addWishes(params: WishesRequest.Add, tokenData: TokenData) {
		const session = await mongoose.startSession();
		session.startTransaction();
		try {
			if (params.wishType == WISH_TYPE.MYSELF) {
				await this.addWishMyself(params, tokenData);
			} else {
				await this.addWishOther(params, tokenData);
			}
			await session.commitTransaction();
			session.endSession();
			return MESSAGES.SUCCESS.WISH_CREATED;
		} catch (error) {
			await session.abortTransaction();
			session.endSession();
			throw error;
		}
	}

	/**
	 * @function addWishMyself
	 * @description adding the wishes for myself.
	 */
	async addWishMyself(params: WishesRequest.Add, tokenData: TokenData) {
		try {
			const step1 = await userDaoV1.findUserById(tokenData.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			if (!step1.isProfileComplete) {
				if (params.visibility !== "PUBLIC") {
					return Promise.reject(MESSAGES.ERROR.REGISTRATION_PENDING);
				}
			}
			if (!params.communityId && !params.tagContacts) return Promise.reject(MESSAGES.ERROR.ADD_WISH_ATLEAST_ONE_CONTACT_OR_COMMUNITY);
			if (params.tagContacts && params.tagContacts.length == 0 && params.communityId && params.communityId.length == 0) return Promise.reject(MESSAGES.ERROR.ADD_WISH_ATLEAST_ONE_CONTACT_OR_COMMUNITY);
			if (!params.tagContacts && params.communityId && params.communityId.length == 0) return Promise.reject(MESSAGES.ERROR.ADD_WISH_ATLEAST_ONE_COMMUNITY);
			if (!params.communityId && params.tagContacts && params.tagContacts.length == 0) return Promise.reject(MESSAGES.ERROR.ADD_WISH_ATLEAST_ONE_CONTACT);
			step1.name = step1.name ? step1.name : "wishwell";
			if (params.wishType == WISH_TYPE.OTHER) {
				params.status = STATUS.PENDING;
			}
			params.userId = tokenData.userId;
			let obj = {};
			obj['firstName'] = step1.firstName;
			obj['lastName'] = step1.lastName;
			obj['profilePicture'] = step1.profilePicture;
			params.userDetail = obj;
			let registeredUser = [], nonRegisteredUser = [];
			let userLocationData = await this.getLocation(tokenData.userId, params);
			params["location"] = userLocationData;
			let step2 = await wishesDaoV2.addWishes(params);
			if (params.location) {
				await commonControllerV1.saveLocation(params, GEO_LOCATION_TYPE.WISH, step2._id, tokenData.userId);
			}
			let communityUsers, duplicateData = [];
			if (params.communityId) {
				// communityUsers = await baseDao.find("members", { 'communityId': { '$in': params.communityId }, status: { '$nin': [STATUS.DELETED, STATUS.REJECTED] }, userId: { $exists: true } }, {});
				communityUsers = await baseDao.find("members", { 'communityId': { '$in': params.communityId }, status: { '$nin': [STATUS.DELETED, STATUS.REJECTED] }}, {});
				if (communityUsers) {
					communityUsers = await this.removeDuplicateV2(communityUsers);
					if (tokenData.userId) {
						let foundObject = communityUsers.find(obj => obj.userId.toString() === tokenData.userId.toString());
						let index = communityUsers.findIndex(obj => obj.userId.toString() === tokenData.userId.toString());
						if (foundObject) {
							let removedValue = communityUsers.splice(index, 1); // Removes 1 element at the specified index - user from community which is host 
						}
					}
					if (params.hostId) {
						let foundObject = communityUsers.find(obj => obj.userId.toString() === params.hostId.toString());
						let index = communityUsers.findIndex(obj => obj.userId.toString() === params.hostId.toString());
						if (foundObject) {
							let removedValue = communityUsers.splice(index, 1); // Removes 1 element at the specified index - user from community which is host 
						}
					}
					if (params.tagContacts) {
						for (const obj1 of params.tagContacts) {
							for (const obj2 of communityUsers) {
								if (obj1['isAppUser']) {
									if (obj1['userId'].toString() === obj2['userId'].toString()) {
										obj1['isDuplicate'] = false;
										obj1['isDuplicateExist'] = true;
										obj2['isDuplicate'] = true;
										obj2['isDuplicateExist'] = true;
									}
								}
							}
						}
					}
					if (params.cohosts) {
						for (const obj1 of params.cohosts) {
							for (const obj2 of communityUsers) {
								if (obj1.toString() === obj2['userId'].toString()) {
									//obj1['isDuplicate'] = true;
									obj2['isDuplicate'] = true;
									obj2['isDuplicateExist'] = true;
									duplicateData.push(obj1);
								}
							}
						}
					}
				}
			}
			if (!params.tagContacts) {
				params.tagContacts = [];
			}
			if (communityUsers && communityUsers.length !== 0) {
				for (let i = 0; communityUsers.length > i; i++) {
					let object = {};
					if (communityUsers[i].type == WISH_TAG_TYPE.CONTACTS || communityUsers[i].type == WISH_TAG_TYPE.CREATOR) {
						object['userId'] = communityUsers[i].userId.toString();
						object['communityId'] = communityUsers[i].communityId;
						object['isAppUser'] = true;
						object['status'] = communityUsers[i].status;
						object['created'] = Date.now;
						object['createdAt'] = new Date(Date.now());
						object['communityIdList'] = communityUsers[i].communityIdList;
						if (communityUsers[i].isDuplicateExist) {
							object['isDuplicate'] = communityUsers[i].isDuplicate;
							object['isDuplicateExist'] = communityUsers[i].isDuplicateExist;
						} else {
							object['isDuplicate'] = false;
							object['isDuplicateExist'] = false;
						}
						params.tagContacts.push(object);
					} else {
						object['phoneNumber'] = communityUsers[i].phoneNumber;
						object['countryCode'] = communityUsers[i].countryCode;
						object['communityId'] = communityUsers[i].communityId;
						if (communityUsers[i].contactId) object['contactId'] = communityUsers[i].contactId;
						object['isAppUser'] = false;
						object['status'] = communityUsers[i].status;
						object['created'] = Date.now;
						object['createdAt'] = new Date(Date.now());
						object['communityIdList'] = communityUsers[i].communityIdList;
						params.tagContacts.push(object);
					}
				}
			}
			if (params.tagContacts) {
				for (let i = 0; params.tagContacts.length > i; i++) {
					if (params.tagContacts[i]['isAppUser']) {
						registeredUser.push(params.tagContacts[i]);
					} else {
						nonRegisteredUser.push(params.tagContacts[i]);
					}
				}
			}
			// const deeplink = `${SERVER.APP_URL}${SERVER.API_BASE_URL}/v1/common/deeplink?type=${DEEPLINK_TYPE.SMS_INVITE}`;  // deeplinking
			let contactArray = [];
			let pushArray = [];
			// let hostIdArr = [];
			obj = {};
			// if (params.hostDetail) {
			// 	if (params.hostDetail['userId']) {
			// 		obj['userId'] = toObjectId(params.hostDetail['userId'])
			// 		obj['wishId'] = step2._id
			// 		obj['type'] = WISH_TAG_TYPE.HOST;
			// 		obj['status'] = STATUS.ACTIVE;
			// 		obj['wishType'] = params.wishType
			// 		obj['created'] = Date.now;
			// 		obj['createdAt'] = new Date(Date.now());
			// 	} else {
			// 		obj['countryCode'] = params.hostDetail['countryCode'];
			// 		obj['phoneNumber'] = params.hostDetail['phoneNumber'];
			// 		obj['wishId'] = step2._id
			// 		obj['type'] = WISH_TAG_TYPE.INVITED_HOST;
			// 		obj['status'] = STATUS.ACTIVE;
			// 		obj['wishType'] = params.wishType
			// 		obj['created'] = Date.now;
			// 		obj['createdAt'] = new Date(Date.now());
			// 		// REMOVED FIRST SMS & REMINDERS
			// 	}
			// 	contactArray.push(obj);
			// 	hostIdArr.push(obj)
			// }
			if (registeredUser && registeredUser.length !== 0) {
				for (let i = 0; registeredUser.length > i; i++) {
					let obj = {};
					obj['userId'] = toObjectId(registeredUser[i]['userId']);
					obj['wishId'] = step2._id;
					obj['type'] = WISH_TAG_TYPE.CONTACTS;
					if (registeredUser[i].status) {
						obj['status'] = registeredUser[i].status;
					} else {
						obj['status'] = STATUS.ACTIVE;
					}
					obj['wishType'] = params.wishType;
					obj['created'] = Date.now;
					obj['createdAt'] = new Date(Date.now());
					if (registeredUser[i].communityIdList) obj['communityIdList'] = registeredUser[i].communityIdList;
					if (registeredUser[i].isDuplicateExist) {
						obj['isDuplicate'] = registeredUser[i].isDuplicate;
						obj['isDuplicateExist'] = registeredUser[i].isDuplicateExist;
					} else {
						obj['isDuplicate'] = false;
						obj['isDuplicateExist'] = false;
					}
					if (registeredUser[i]['communityId']) obj['communityId'] = toObjectId(registeredUser[i]['communityId']);
					contactArray.push(obj);
					if (registeredUser[i].communityId) {
						if (registeredUser[i].status && registeredUser[i].status !== STATUS.PENDING) {
							if (params.wishType == WISH_TYPE.MYSELF) {
								pushArray.push(obj);
							}
						}
					} else {
						if (params.wishType == WISH_TYPE.MYSELF) {
							pushArray.push(obj);
						}
					}
				}
			}
			if (nonRegisteredUser && nonRegisteredUser.length !== 0) {
				for (let i = 0; nonRegisteredUser.length > i; i++) {
					let obj = {};
					obj['wishId'] = step2._id;
					obj['type'] = WISH_TAG_TYPE.INVITED;
					obj['countryCode'] = nonRegisteredUser[i].countryCode;
					obj['phoneNumber'] = nonRegisteredUser[i].phoneNumber;
					if (nonRegisteredUser[i].status) {
						obj['status'] = nonRegisteredUser[i].status;
					} else {
						obj['status'] = STATUS.ACTIVE;
					}
					obj['wishType'] = params.wishType;
					obj['created'] = Date.now;
					obj['createdAt'] = new Date(Date.now());
					if (nonRegisteredUser[i].isDuplicateExist) {
						obj['isDuplicate'] = nonRegisteredUser[i].isDuplicate;
						obj['isDuplicateExist'] = nonRegisteredUser[i].isDuplicateExist;
					} else {
						obj['isDuplicate'] = false;
						obj['isDuplicateExist'] = false;
					}
					if (nonRegisteredUser[i].communityId) obj['communityId'] = nonRegisteredUser[i].communityId;
					if (nonRegisteredUser[i].communityIdList) obj['communityIdList'] = nonRegisteredUser[i].communityIdList;
					if (nonRegisteredUser[i].contactId) obj['contactId'] = toObjectId(nonRegisteredUser[i].contactId);
					contactArray.push(obj);
					// let host;
					// if (step2.wishType == WISH_TYPE.MYSELF) {
					// 	// REMOVED FIRST SMS & REMINDERS
					// } else {
					// 	// REMOVED FIRST SMS & REMINDERS (if & else case)
					// }
				}
			}
			if (params.cohosts && params.cohosts.length !== 0) {
				let cohostsDetails = await wishesDaoV2.getCohosts(params.cohosts);
				if (duplicateData && duplicateData.length !== 0) {
					for (const obj1 of cohostsDetails) {
						for (const obj2 of duplicateData) {
							if (obj1['_id'].toString() === obj2.toString()) {
								obj1['isDuplicate'] = false;  // original data            
								obj1['isDuplicateExist'] = true; // duplicate of this data exist  

							}
						}
					}
				}
				for (let i = 0; cohostsDetails.length > i; i++) {
					let obj = {};
					obj['userId'] = toObjectId(cohostsDetails[i]._id);
					obj['wishId'] = step2._id
					obj['type'] = WISH_TAG_TYPE.COHOST;
					obj['status'] = STATUS.ACTIVE;
					obj['wishType'] = params.wishType;
					obj['created'] = Date.now;
					obj['createdAt'] = new Date(Date.now());
					if (cohostsDetails[i].isDuplicateExist) {
						obj['isDuplicate'] = cohostsDetails[i].isDuplicate;
						obj['isDuplicateExist'] = cohostsDetails[i].isDuplicateExist;
					} else {
						obj['isDuplicate'] = false;
						obj['isDuplicateExist'] = false;
					}
					contactArray.push(obj);
					pushArray.push(obj);
				}
			}
			const tagWishes = await wishesDaoV2.addTagWishes(contactArray);
			await this.wellLogs(step2, tokenData, contactArray, params, false);


			// Creating reminder if they are non app-users
			if(tagWishes.ops) {
				for(const tagWish of tagWishes.ops) {
					// const { countryCode = "", phoneNumber = "" } = tagWish;
					// const user = await baseDao.findOne('users', { countryCode, phoneNumber });
					if(!tagWish.userId) {
						const reminderObj = {
							tagWishId: tagWish._id,
							wishId: tagWish.wishId,
							wishType: tagWish.wishType,
							countryCode: tagWish.countryCode,
							phoneNumber: tagWish.phoneNumber,
							isCommunityInvite: false,
							userId: step2.userId,
						};
						await remindersDao.addReminders(reminderObj);
					}
				}
			}

			// if (params.hostId) {
			// 	let pushParamsHost = { "message": MESSAGES_NOTIFICATION.WISH_CREATED, "title": TITLE_NOTIFICATION.WISH_HOST_CREATED, "type": TYPE_NOTIFICATION.WISH_CREATE_HOST }
			// 	await notificationManager.CommonublishNotification([params.hostId], step2._id, pushParamsHost);
			// }
			params.intension = params.intension.toLowerCase(); // to lower case intension text in notification message
			if (step2.wishType == WISH_TYPE.MYSELF) {
				let pushParamsContact = { "message": MESSAGES_NOTIFICATION.WISH_CREATED1 + step1.name + MESSAGES_NOTIFICATION.WISH_CREATED2 + params.intension + MESSAGES_NOTIFICATION.WISH_CREATED3, "title": TITLE_NOTIFICATION.WISH_CREATED, "type": TYPE_NOTIFICATION.WISH_CREATE }
				await notificationManager.PublishNotification(pushArray, step2._id, pushParamsContact);
			} else if (params.hostId) {
				// let host = await baseDao.findOne("users", { _id: params.hostId, status: { '$ne': STATUS.DELETED } });
				// let pushParamsContact = { "message": MESSAGES_NOTIFICATION.WISH_CREATED1 + host.name + MESSAGES_NOTIFICATION.WISH_CREATED2 + params.intension + MESSAGES_NOTIFICATION.WISH_CREATED3, "title": TITLE_NOTIFICATION.WISH_CREATED, "type": TYPE_NOTIFICATION.WISH_CREATE }
				// if (pushArray.length) {
				// 	await notificationManager.PublishNotification(pushArray, step2._id, pushParamsContact);
				// }
			} else {
				// let pushParamsContact = { "message": MESSAGES_NOTIFICATION.WISH_CREATED1 + step1.name + MESSAGES_NOTIFICATION.WISH_CREATED2 + params.intension + MESSAGES_NOTIFICATION.WISH_CREATED3, "title": TITLE_NOTIFICATION.WISH_CREATED, "type": TYPE_NOTIFICATION.WISH_CREATE }
				// if (pushArray.length) {
				// 	await notificationManager.PublishNotification(pushArray, step2._id, pushParamsContact);
				// }
			}
			return true;
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function addWishOther
	 * @description adding the wishes for other.
	 */
	async addWishOther(params: WishesRequest.Add, tokenData: TokenData) {
		try {
			const step1 = await userDaoV1.findUserById(tokenData.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			if (!step1.isProfileComplete) {
				if (params.visibility !== "PUBLIC") {
					return Promise.reject(MESSAGES.ERROR.REGISTRATION_PENDING);
				}
			}
			step1.name = step1.name ? step1.name : "wishwell";
			if (params.wishType == WISH_TYPE.OTHER) {
				params.status = STATUS.PENDING;
			}
			params.userId = tokenData.userId;
			let obj = {};
			obj['firstName'] = step1.firstName;
			obj['lastName'] = step1.lastName;
			obj['profilePicture'] = step1.profilePicture;
			params.userDetail = obj;
			let userLocationData = await this.getLocation(tokenData.userId, params);
			params["location"] = userLocationData;
			let step2 = await wishesDaoV2.addWishes(params);
			if (params.location) {
				await commonControllerV1.saveLocation(params, GEO_LOCATION_TYPE.WISH, step2._id, tokenData.userId);
			}
			let contactArray = [];
			obj = {};
			if (params.hostDetail) {
				if (params.hostDetail['userId']) {
					obj['userId'] = toObjectId(params.hostDetail['userId']);
					obj['wishId'] = step2._id;
					obj['type'] = WISH_TAG_TYPE.HOST;
					obj['status'] = STATUS.ACTIVE;
					obj['wishType'] = params.wishType;
					obj['created'] = Date.now;
					obj['createdAt'] = new Date(Date.now());
				} else {
					obj['countryCode'] = params.hostDetail['countryCode'];
					obj['phoneNumber'] = params.hostDetail['phoneNumber'];
					obj['wishId'] = step2._id;
					obj['type'] = WISH_TAG_TYPE.INVITED_HOST;
					obj['status'] = STATUS.ACTIVE;
					obj['wishType'] = params.wishType;
					obj['created'] = Date.now;
					obj['createdAt'] = new Date(Date.now());
				}
				contactArray.push(obj);
			}
			const tagWishes = await wishesDaoV2.addTagWishes(contactArray);
			await this.wellLogs(step2, tokenData, contactArray, params, false);

			// Creating reminder if they are non app-users
			if(tagWishes.ops) {
					for(const tagWish of tagWishes.ops) {
						// const { countryCode = "", phoneNumber = "" } = tagWish;
						// const user = await baseDao.findOne('users', { countryCode, phoneNumber });
						if(!tagWish.userId) {
							const reminderObj = {
								tagWishId: tagWish._id,
								wishId: tagWish.wishId,
								wishType: tagWish.wishType,
								countryCode: tagWish.countryCode,
								phoneNumber: tagWish.phoneNumber,
								isCommunityInvite: false,
								userId: step2.userId,
							};
							await remindersDao.addReminders(reminderObj);
						}
					}
			}
			
			if (params.hostId) {
				let pushParamsHost = { "message": MESSAGES_NOTIFICATION.WISH_CREATED, "title": TITLE_NOTIFICATION.WISH_HOST_CREATED, "type": TYPE_NOTIFICATION.WISH_CREATE_HOST }
				await notificationManager.CommonublishNotification([params.hostId], step2._id, pushParamsHost);
			}
			return true;
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function editWish
	 * @description here are editing the wishes.
	 */
	async editWish(params: WishesRequest.EditWish, tokenData: TokenData) {
		try {
			let step1 = await userDaoV1.findUserById(tokenData.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			let step2 = await wishesDaoV2.findWishById(params.wishId);
			if (!step2) {
				return Promise.reject(MESSAGES.ERROR.WISH_NOT_FOUND);
			}
			let step3 = await baseDao.findOne("tagwishes", { userId: toObjectId(tokenData.userId), wishId: toObjectId(params.wishId), type: WISH_TAG_TYPE.COHOST });
			if ((step2.userId == tokenData.userId) || (step2.hostId == tokenData.userId) || (step3)) {
				let obj = {};
				if (params.title) obj['title'] = params.title;
				if (params.description) obj['description'] = params.description;
				if (params.image) obj['image'] = params.image;
				if (params.intension) obj['intension'] = params.intension;
				if (params.removeCommunityId) {
					await baseDao.updateOne("wishes", { _id: toObjectId(params.wishId) }, { "$pull": { communityId: { '$in': params.removeCommunityId } }, '$set': obj }, {});
				} else {
					await baseDao.updateOne("wishes", { _id: toObjectId(params.wishId) }, { '$set': obj }, {});
				}
				let removeCommunityUsers;
				if (params.removeCommunityId) {
					const removeCommunityId = params.removeCommunityId.map(id => toObjectId(id));
					removeCommunityUsers = await baseDao.find("tagwishes", { 'wishId': toObjectId(params.wishId), 'communityIdList': { '$all': removeCommunityId }, status: { '$nin': [STATUS.DELETED, STATUS.REJECTED] } }, { _id: 1 });
					for (let id of removeCommunityUsers) {
						const pullCommunity = await baseDao.findOneAndUpdate("tagwishes", { _id: id._id }, { $pull: { communityIdList: { $in: removeCommunityId } } }, { returnOriginal: false });
						if (pullCommunity && pullCommunity.communityIdList.length == 0) {
							await baseDao.updateMany("tagwishes", { _id: pullCommunity._id }, { status: STATUS.DELETED }, {});
						}
					}
					let communityUsers = await baseDao.find("tagwishes", { 'wishId': toObjectId(params.wishId), 'communityIdList': { '$all': removeCommunityId }, status: { '$nin': [STATUS.DELETED, STATUS.REJECTED] } }, { _id: 1, wishId: 1, userId: 1, isDuplicate: 1, isDuplicateExist: 1 });
					for (const obj1 of communityUsers) {
						if (obj1['isDuplicateExist']) {
							let data = await baseDao.find("tagwishes", { userId: toObjectId(obj1['userId']), 'wishId': toObjectId(obj1['wishId']), status: { '$nin': [STATUS.DELETED, STATUS.REJECTED] } }, { _id: 1 });
							let updatedData = await baseDao.updateMany("tagwishes", { _id: { '$in': data } }, { '$set': { isDuplicateExist: false, isDuplicate: false } }, {});
						}
					}
				}
				let registeredUser = [], nonRegisteredUser = [];
				if (params.removeTagContacts && params.removeTagContacts.length !== 0) {
					for (const obj1 of params.removeTagContacts) {
						if (obj1['userId']) {
							let mainData = await baseDao.findOne("tagwishes", { userId: toObjectId(obj1['userId']), 'wishId': toObjectId(params.wishId), "type": WISH_TAG_TYPE.CONTACTS, status: { '$nin': [STATUS.DELETED, STATUS.REJECTED] }, communityId: { '$exists': false } }, {});
							if (mainData['isDuplicateExist']) {
								let data = await baseDao.find("tagwishes", { userId: toObjectId(mainData['userId']), 'wishId': toObjectId(params.wishId), status: { '$nin': [STATUS.DELETED, STATUS.REJECTED] } }, { _id: 1 });
								let updatedData = await baseDao.updateMany("tagwishes", { _id: { '$in': data } }, { '$set': { isDuplicateExist: false, isDuplicate: false } }, {});
							}
						}
					}
					params.removeTagContacts.forEach(element => {
						if (element['isAppUser']) {
							registeredUser.push(element['userId'])
						} else {
							nonRegisteredUser.push(element['phoneNumber'])
						}
					});
				}
				let users1 = await baseDao.find("tagwishes", { wishId: params.wishId, userId: { '$in': registeredUser }, type: WISH_TAG_TYPE.CONTACTS }, { _id: 1 });
				let users2 = await baseDao.find("tagwishes", { wishId: params.wishId, phoneNumber: { '$in': nonRegisteredUser }, type: WISH_TAG_TYPE.INVITED }, { _id: 1 });
				let users3 = await baseDao.find("tagwishes", { wishId: params.wishId, userId: { '$in': params.removeCohosts }, type: WISH_TAG_TYPE.COHOST }, { _id: 1 });
				if (params.removeCohosts && params.removeCohosts.length !== 0) {
					for (let obj of params.removeCohosts) {
						let mainData = await baseDao.findOne("tagwishes", { userId: toObjectId(obj.toString()), 'wishId': toObjectId(params.wishId), "type": WISH_TAG_TYPE.COHOST, status: { '$nin': [STATUS.DELETED, STATUS.REJECTED] } }, {});
						if (mainData['isDuplicateExist']) {
							let data = await baseDao.find("tagwishes", { userId: toObjectId(mainData['userId']), 'wishId': toObjectId(params.wishId), status: { '$nin': [STATUS.DELETED, STATUS.REJECTED] } }, { _id: 1 });
							let updatedData = await baseDao.updateMany("tagwishes", { _id: { '$in': data } }, { '$set': { isDuplicateExist: false, isDuplicate: false } }, {});
						}
					}
				}
				let resultRepo = users1.concat(users2, users3)
				await baseDao.updateMany("tagwishes", { _id: { '$in': resultRepo }, communityId: { '$exists': false } }, { '$set': { status: STATUS.DELETED } }, {});
				if(params.addTagContacts) {
					for(const newTagContact of params.addTagContacts) {
						let isUser;
						if(newTagContact.isAppUser) {
							isUser = await userDaoV1.findOne("users", {_id: newTagContact.userId});
						}
						// else if(newTagContact.contactId) {
						// 	const contact = await baseDao.findOne("contacts", {_id: newTagContact.contactId});
						// 	const {userId} = contact;
						// 	isUser = await userDaoV1.findOne("users", { _id: userId });
						// } 
						else {
							isUser = await userDaoV1.findOne("users", { countryCode: newTagContact.countryCode, phoneNumber: newTagContact.phoneNumber });
						}
						if(isUser) {
							// const result = await baseDao.findOne("tagwishes", { wishId: step2._id, userId: isUser._id, status: STATUS.ACTIVE });
							const result = await baseDao.findOne("tagwishes", {
								wishId: step2._id,
								userId: isUser._id,
								status: STATUS.ACTIVE,
								$or: [
									{ communityId: { $exists: false } },  
									{ communityId: null },                
									{ communityIdList: { $exists: false } },  
									{ communityIdList: { $eq: [] } },         
									{ communityIdList: { $size: 0 } }         
								]
							});	
							
							if(result) {
								return Promise.reject(MESSAGES.ERROR.EDIT_WISH_DUPLICATE_CONTACT);
							}
						} else {
							const result = await baseDao.findOne("tagwishes", { 
								wishId: step2._id, 
								countryCode: newTagContact.countryCode, 
								phoneNumber: newTagContact.phoneNumber, 
								status: STATUS.ACTIVE, 
								$or: [
									{ communityId: { $exists: false } },  
									{ communityId: null },                
									{ communityIdList: { $exists: false } },  
									{ communityIdList: { $eq: [] } },         
									{ communityIdList: { $size: 0 } }         
								]
							});
							if(result) {
								return Promise.reject(MESSAGES.ERROR.EDIT_WISH_DUPLICATE_CONTACT);
							}
						}
					}
				}
				if(params.addCohosts) {
					for(const Cohost of params.addCohosts) {
						const result = await baseDao.findOne("tagwishes", { 
							userId: Cohost, 
							status: STATUS.ACTIVE, 
							wishId: step2._id,
							$or: [
								{ communityId: { $exists: false } },  
								{ communityId: null },                
								{ communityIdList: { $exists: false } },  
								{ communityIdList: { $eq: [] } },         
								{ communityIdList: { $size: 0 } }         
							]
						});
							if(result) {
								return Promise.reject(MESSAGES.ERROR.EDIT_WISH_DUPLICATE_COHOST);
							}
					}
				}
				let communityUsers, duplicateData = [];
				if (params.addCommunityId) {
					await baseDao.updateOne("wishes", { _id: toObjectId(params.wishId) }, { "$push": { communityId: params.addCommunityId } }, {});
					communityUsers = await baseDao.find("members", { 'communityId': { '$in': params.addCommunityId }, status: { '$in': [STATUS.ACTIVE, STATUS.PENDING] }, userId: { $exists: true } }, {});
					if (communityUsers) {
						let tagCommunityContact = await baseDao.find("tagwishes", { 'wishId': toObjectId(params.wishId), status: { '$nin': [STATUS.DELETED, STATUS.REJECTED] }, communityId: { '$exists': true } }, {});
						let i = 0;
						let updatedArray = [], noDuplicates = [];
						while (i < communityUsers.length) {
							let currentItem = communityUsers[i];
							let isDuplicate = false;
							for (const obj2 of tagCommunityContact) {
								if (obj2['userId']) { // 8 NOV UPDATE USER ID CHECK LAST TIME OF BUILD DEPLOY
									if (currentItem['userId'].toString() === obj2['userId'].toString()) {
										isDuplicate = true;
										let obj = {};
										obj2.communityIdList.push(currentItem['communityId']);
										obj['_id'] = obj2['_id'];
										obj['communityIdList'] = obj2['communityIdList'];
										updatedArray.push(obj);
										break;
									}
								}
							}
							if (!isDuplicate) noDuplicates.push(currentItem);
							i++;
						}
						if (updatedArray) {
							for (let i = 0; i < updatedArray.length; i++) {
								await baseDao.updateOne("tagwishes", { _id: toObjectId(updatedArray[i]['_id']) }, { '$set': { communityIdList: updatedArray[i]['communityIdList'] } }, {});
							}
						}
						communityUsers = noDuplicates;
						if (communityUsers.length > 0) {
							const uniqueArray = [];
							i = 0;
							communityUsers = await this.removeDuplicateV2(communityUsers);
							if (step2.userId) {
								let foundObject = communityUsers.find(obj => obj.userId.toString() === step2.userId.toString());
								let index = communityUsers.findIndex(obj => obj.userId.toString() === step2.userId.toString());
								if (foundObject) {
									let removedValue = communityUsers.splice(index, 1); 
								}
							}
							if (step2.hostId) {
								let foundObject = communityUsers.find(obj => obj.userId.toString() === step2.hostId.toString());
								let index = communityUsers.findIndex(obj => obj.userId.toString() === step2.hostId.toString());
								if (foundObject) {
									let removedValue = communityUsers.splice(index, 1); 
								}
							}
							if (params.addTagContacts) {
								for (const obj1 of params.addTagContacts) {
									for (const obj2 of communityUsers) {
										if (obj1['isAppUser']) {
											if (obj1['userId'].toString() === obj2['userId'].toString()) {
												obj1['isDuplicate'] = false;
												obj1['isDuplicateExist'] = true;
												obj2['isDuplicate'] = true;
												obj2['isDuplicateExist'] = true;
											}
										}
									}
								}
							}
						}
						let tagContactExist = await baseDao.find("tagwishes", { 'wishId': toObjectId(params.wishId), status: { '$nin': [STATUS.DELETED, STATUS.REJECTED] } }, {});
						if (tagContactExist) {
							for (const obj1 of tagContactExist) {
								for (const obj2 of communityUsers) {
									if (obj1['userId']) { // 8 NOV UPDATE USER ID CHECK LAST TIME OF BUILD DEPLOY
										if (obj1['userId'].toString() === obj2['userId'].toString()) {
											obj2['isDuplicate'] = true;
											obj2['isDuplicateExist'] = true;
										}
									}

								}
							}
						}
						if (params.addCohosts) {
							for (const obj1 of params.addCohosts) {
								for (const obj2 of communityUsers) {
									if (obj1.toString() === obj2['userId'].toString()) {
										obj2['isDuplicate'] = true;
										obj2['isDuplicateExist'] = true;
										duplicateData.push(obj1);
									}
								}
							}
						}
					}
				}

				if (params.addTagContacts) {
					let data = await baseDao.find("tagwishes", { 'wishId': toObjectId(params.wishId), status: { '$nin': [STATUS.DELETED, STATUS.REJECTED] }, communityId: { '$exists': true } }, {});
					let communityData = [];
					if (data) {
						for (const obj1 of params.addTagContacts) {
							for (const obj2 of data) {
								if (obj2['userId'] && obj1['isAppUser']) { // 8 NOV UPDATE USER ID CHECK LAST TIME OF BUILD DEPLOY
									if (obj1['userId'].toString() === obj2['userId'].toString()) {
										obj1['isDuplicate'] = false;
										obj1['isDuplicateExist'] = true;
										communityData.push(obj2['_id'])
									}
								}
							}
						}
						if (communityData) await baseDao.updateMany("tagwishes", { _id: { '$in': communityData } }, { '$set': { isDuplicate: true, isDuplicateExist: true } }, {});
					}
				}
				if (params.addCohosts) {
					let data = await baseDao.find("tagwishes", { 'wishId': toObjectId(params.wishId), status: { '$nin': [STATUS.DELETED, STATUS.REJECTED] }, communityId: { '$exists': true } }, {});
					let communityData = [];
					if (data) {
						for (const obj1 of params.addCohosts) {
							for (const obj2 of data) {
								if (obj2['userId']) { // 8 NOV UPDATE USER ID CHECK LAST TIME OF BUILD DEPLOY
									if (obj1.toString() === obj2['userId'].toString()) {
										duplicateData.push(obj1);
										communityData.push(obj2['_id'])
									}
								}
							}
						}
						if (communityData) await baseDao.updateMany("tagwishes", { _id: { '$in': communityData } }, { '$set': { isDuplicate: true, isDuplicateExist: true } }, {});
					}
				}

				if (!params.addTagContacts) {
					params.addTagContacts = []
				}
				let contactArray = [];
				let pushArray = [];
				if (communityUsers && communityUsers.length !== 0) {
					for (let i = 0; communityUsers.length > i; i++) {
						let object = {};
						if (communityUsers[i].type == WISH_TAG_TYPE.CONTACTS || communityUsers[i].type == WISH_TAG_TYPE.CREATOR) {
							object['userId'] = communityUsers[i].userId.toString();
							object['communityId'] = communityUsers[i].communityId;
							object['isAppUser'] = true;
							object['status'] = communityUsers[i].status;
							object['created'] = Date.now;
							object['createdAt'] = new Date(Date.now());
							object['communityIdList'] = communityUsers[i].communityIdList;
							if (communityUsers[i].isDuplicateExist) {
								object['isDuplicate'] = communityUsers[i].isDuplicate;
								object['isDuplicateExist'] = communityUsers[i].isDuplicateExist;
							} else {
								object['isDuplicate'] = false;
								object['isDuplicateExist'] = false;
							}
							params.addTagContacts.push(object);
						} else {
							object['phoneNumber'] = communityUsers[i].phoneNumber;
							object['countryCode'] = communityUsers[i].countryCode;
							object['communityId'] = communityUsers[i].communityId;
							if (communityUsers[i].contactId) object['contactId'] = communityUsers[i].contactId;
							object['isAppUser'] = false;
							object['status'] = communityUsers[i].status;
							object['created'] = Date.now;
							object['createdAt'] = new Date(Date.now());
							object['communityIdList'] = communityUsers[i].communityIdList;
							params.addTagContacts.push(object);
						}
					}
				}
				registeredUser = [], nonRegisteredUser = [];
				if (params.addTagContacts && params.addTagContacts.length !== 0) {
					params.addTagContacts.forEach(element => {
						if (element['isAppUser']) {
							registeredUser.push(element);
						} else {
							nonRegisteredUser.push(element);
						}
					});
					if (registeredUser && registeredUser.length !== 0) {
						for (let i = 0; registeredUser.length > i; i++) {
							let obj = {};
							obj['userId'] = toObjectId(registeredUser[i].userId);
							obj['wishId'] = step2._id;
							obj['wishType'] = step2.wishType;
							obj['type'] = WISH_TAG_TYPE.CONTACTS;
							obj['status'] = STATUS.ACTIVE;
							obj['created'] = Date.now;
							obj['createdAt'] = new Date(Date.now());
							if (registeredUser[i].status) {
								obj['status'] = registeredUser[i].status;
							} else {
								obj['status'] = STATUS.ACTIVE;
							}
							obj['created'] = Date.now;
							if (registeredUser[i].communityIdList) obj['communityIdList'] = registeredUser[i].communityIdList;
							if (registeredUser[i].isDuplicateExist) {
								obj['isDuplicate'] = registeredUser[i].isDuplicate;
								obj['isDuplicateExist'] = registeredUser[i].isDuplicateExist;
							} else {
								obj['isDuplicate'] = false;
								obj['isDuplicateExist'] = false;
							}
							if (registeredUser[i].communityId) obj['communityId'] = registeredUser[i].communityId;
							contactArray.push(obj);
							if (registeredUser[i].communityId) {
								if (registeredUser[i].status && registeredUser[i].status !== STATUS.PENDING) {
									pushArray.push(obj);
								}
							} else {
								pushArray.push(obj);
							}
						}
					}
					if (nonRegisteredUser && nonRegisteredUser.length !== 0) {
						for (let i = 0; nonRegisteredUser.length > i; i++) {
							let obj = {};
							obj['wishId'] = step2._id;
							obj['wishType'] = step2.wishType;
							obj['type'] = WISH_TAG_TYPE.INVITED;
							obj['countryCode'] = nonRegisteredUser[i].countryCode;
							obj['phoneNumber'] = nonRegisteredUser[i].phoneNumber;
							obj['status'] = STATUS.ACTIVE;
							obj['created'] = Date.now;
							obj['createdAt'] = new Date(Date.now());
							if (nonRegisteredUser[i].status) {
								obj['status'] = registeredUser[i].status;
							} else {
								obj['status'] = STATUS.ACTIVE;
							}
							if (nonRegisteredUser[i].isDuplicateExist) {
								obj['isDuplicate'] = nonRegisteredUser[i].isDuplicate;
								obj['isDuplicateExist'] = nonRegisteredUser[i].isDuplicateExist;
							} else {
								obj['isDuplicate'] = false;
								obj['isDuplicateExist'] = false;
							}
							if (nonRegisteredUser[i].communityId) obj['communityId'] = nonRegisteredUser[i].communityId;
							if (nonRegisteredUser[i].contactId) obj['contactId'] = toObjectId(nonRegisteredUser[i].contactId);
							contactArray.push(obj);
							// const deeplink = `${SERVER.APP_URL}${SERVER.API_BASE_URL}/v1/common/deeplink?type=${DEEPLINK_TYPE.SMS_INVITE}`;
							// let host;
							if (step2.wishType == WISH_TYPE.MYSELF) {
								// REMOVED FIRST SMS & REMINDERS
							} else {
								// REMOVED FIRST SMS & REMINDERS (if & else case)
							}
						}
					}
				}
				if (params.addCohosts && params.addCohosts.length !== 0) {
					let cohostsDetails = await wishesDaoV2.getCohosts(params.addCohosts);
					if (duplicateData && duplicateData.length !== 0) {
						for (const obj1 of cohostsDetails) {
							for (const obj2 of duplicateData) {
								if (obj1['_id'].toString() === obj2.toString()) {
									obj1['isDuplicate'] = false;  // original data            
									obj1['isDuplicateExist'] = true; // duplicate of this data exist  

								}
							}
						}
					}
					for (let i = 0; cohostsDetails.length > i; i++) {
						let obj = {};
						obj['userId'] = toObjectId(cohostsDetails[i]._id)
						obj['wishId'] = step2._id
						obj['wishType'] = step2.wishType
						obj['type'] = WISH_TAG_TYPE.COHOST;
						obj['status'] = STATUS.ACTIVE
						obj['created'] = Date.now
						obj['createdAt'] = new Date(Date.now())
						if (cohostsDetails[i].isDuplicateExist) {
							obj['isDuplicate'] = cohostsDetails[i].isDuplicate;
							obj['isDuplicateExist'] = cohostsDetails[i].isDuplicateExist;
						} else {
							obj['isDuplicate'] = false;
							obj['isDuplicateExist'] = false;
						}
						contactArray.push(obj);
						pushArray.push(obj);
					}
				}
				if (contactArray) {
					const tagWishes = await wishesDaoV2.addTagWishes(contactArray);
					// Creating reminder if they are non app-users
					if(tagWishes.ops) {
						for(const tagWish of tagWishes.ops) {
							// const { countryCode = "", phoneNumber = "" } = tagWish;
							// const user = await baseDao.findOne('users', { countryCode, phoneNumber });
							if(!tagWish.userId) {
								const reminderObj = {
									tagWishId: tagWish._id,
									wishId: tagWish.wishId,
									wishType: tagWish.wishType,
									countryCode: tagWish.countryCode,
									phoneNumber: tagWish.phoneNumber,
									isCommunityInvite: false,
									userId: step2.userId,
								};
								await remindersDao.addReminders(reminderObj);
							}
						}
					}
				} 
				step2.description = params?.description ? params.description : step2.description;
				params.image = params?.image ? params.image : step2.image;
				// await this.wellLogs(step2, tokenData, contactArray, params, true);
				if (pushArray.length) {
					const updatedIntension = params?.intension ? params.intension : step2.intension;
					let pushParamsContact = { "message": MESSAGES_NOTIFICATION.WISH_CREATED1 + step1.name + MESSAGES_NOTIFICATION.WISH_CREATED2 + updatedIntension + MESSAGES_NOTIFICATION.WISH_CREATED3, "title": TITLE_NOTIFICATION.WISH_CREATED, "type": TYPE_NOTIFICATION.WISH_CREATE }
					await notificationManager.PublishNotification(pushArray, step2._id, pushParamsContact);
				}
			} else {
				return Promise.reject(MESSAGES.ERROR.SOMETHING_WENT_WRONG);
			}
			return MESSAGES.SUCCESS.EDIT_WISH;
		} catch (error) {
			throw error;
		}

	}

	/**
	 * @function getPublicWishes
	 * @description Get public wishes excluding reported ones
	 */
	async getPublicWishes(query: WishesRequest.PublicWishList, tokenData: TokenData) {
		try {
			// 1. Validate user exists
			const verifyUser = await userDaoV1.findUserById(tokenData.userId);
			if (!verifyUser) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
	
			// 2. Get reported wish IDs
			const reportedWishIds = await reportDaoV1.reportedWishes(REPORT_TYPE.WISH, tokenData.userId);
	
			// 3. Fetch public wishes through DAO
			const wishesList = await wishesDaoV2.getPublicWishes(query, reportedWishIds);
	
			// 4. Decorate shareLinks
			wishesList.data.forEach((wish) => {
				const encodedWishId = Buffer.from((wish._id).toString()).toString('base64');
				const encodedUserId = Buffer.from((tokenData.userId).toString()).toString('base64');
				wish.shareLink = `${SERVER.APP_URL}${SERVER.API_BASE_URL}/v1/common/deeplink?type=WISH_SHARE&wid=${encodedWishId}&uid=${encodedUserId}`;
			});
	
			// 5. Return result
			return wishesList;
	
		} catch (error) {
			console.error("❌ Error in getPublicWishes:", error);
			throw error;
		}
	}

/**
 * @function publicWishDetail
 * @description Fetch wish detail for public access (no login required)
 */
/**
 * @function publicWishDetail
 * @description Fetch public wish detail without requiring authentication, but still shows user info.
 */
async publicWishDetail(params: WishesRequest.WishDetail) {
    try {
        // 1. Find the wish
        const wishRecord = await wishesDaoV1.findWishById(params.wishId);

        if (!wishRecord) {
            throw MESSAGES.ERROR.WISH_NOT_FOUND;
        }

        // 2. Ensure it is PUBLIC and ACTIVE
        if (wishRecord.visibility !== "PUBLIC" || wishRecord.status !== STATUS.ACTIVE) {
            throw MESSAGES.ERROR.UNAUTHORIZED_ACCESS;
        }

        // 3. Pull full wish details (userDetail and hostDetail etc.)
        const [wishDetail] = await wishesDaoV1.wishDetail(params, null, wishRecord);

        if (!wishDetail) {
            throw MESSAGES.ERROR.WISH_NOT_FOUND;
        }

        // 4. Add a shareable link (only wishId encoded)
        const encodedWishId = Buffer.from((wishDetail._id).toString()).toString('base64');
        wishDetail.shareLink = `${SERVER.APP_URL}${SERVER.API_BASE_URL}/v2/wishes/public-wish-detail?wid=${encodedWishId}`;

        // 5. (Optional) bless counts could be added, but no personal bless data

        return MESSAGES.SUCCESS.WISH_DETAIL(wishDetail);
    } catch (error) {
        console.error("❌ Error in publicWishDetail:", error);
        throw error;
    }
}



	/**
	 * @function getLocation
	 * @description get location
	 */
	async getLocation(userId, params) {
		try {
			let address;
			let city;
			let state;
			let country;

			if (params.location) {
				if (params.location.city) { address = params.location.city; }
				if (params.location.state)
					address = address + "," + params.location.state;
				city = params.location.city;
				state = params.location.state;
				country = params.location.country;
			} else {
				const userProfileAddress = await userDaoV1.findOne("users", { _id: toObjectId(userId) }, {});
				if (userProfileAddress.city) {
					address = userProfileAddress.city;
					city = userProfileAddress.city;
				} else {
					return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
				}
				if (userProfileAddress.state) {
					address = address + "," + userProfileAddress.state;
					state = userProfileAddress.state;
					country = userProfileAddress.country;
				}
			}

			const apiUrl = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(address)}&key=${process.env.GOOGLE_API_KEY}`;
			return new Promise((resolve, reject) => {
				// Make the API request
				fetch(apiUrl)
					.then((response) => response.json())
					.then((data) => {
						// Extract latitude and longitude from the response
						if (data.results.length > 0) {
							if (!data.results[0].geometry.location) {
								reject(MESSAGES.ERROR.LOCATION_NOT_FOUND);
								//return  Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
							}
							const location = data.results[0].geometry.location;
							const latitude = location.lat;
							const longitude = location.lng;
							let retData = {
								address: city,
								coordinates: [longitude, latitude],
								city: city,
								state: state,
								country: country
							};
							resolve(retData);

						} else {
							console.error("Location not found.222");
							reject(MESSAGES.ERROR.LOCATION_NOT_FOUND);
							//return  Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
						}
					})
					.catch((error) => {
						console.error("Error:", error);
						reject(MESSAGES.ERROR.LOCATION_NOT_FOUND);
					});
			});
		} catch (err) {
			return Promise.reject(MESSAGES.ERROR.SOMETHING_WENT_WRONG);
		}
	}
	/**
	 * @function wellLogs
	 * @description logs maintain for well section with wish creation
	 */
	async wellLogs(data, tokenData, invitedUser, params, isEdit) {
		try {
			let wellLogSave = {};
			let ids = [];
			let saveArr = [];
			if (invitedUser && invitedUser.length) {
				invitedUser.filter(invite => {
					if (!invite.communityId) {
						if (invite.userId) ids.push(invite.userId);
						if (invite.contactId) ids.push(invite.contactId);
					}
				});
			}
			//receiver individual data save
			if (ids && ids.length > 0) {
				//ids.forEach(element => {
				for await (let element of ids) {
					wellLogSave = {};
					wellLogSave['senderId'] = element;  //sender id means user if who will get the list 
					wellLogSave['taggedUser'] = [toObjectId(tokenData.userId)];
					if (params.hostId) wellLogSave['hostId'] = toObjectId(params.hostId);
					wellLogSave['wishId'] = data._id ? data._id : "";
					wellLogSave['subjectId'] = data._id; //wish create
					wellLogSave['subjectDetail'] = {
						'notes': data.description ? data.description : "", //description of wish,
						'type': data.wishType ? data.wishType : "", //wish type
						'wishImage': params.image ? params.image : ""
					}
					wellLogSave['type'] = NOTIFICATION_TYPE.TAG_WISHES;
					wellLogSave['created'] = Date.now();
					wellLogSave['createdAt'] = new Date(Date.now());
					saveArr.push(wellLogSave);
				}
			}
			//for community well log maintain
			// if (isEdit == false) {
				if (params.communityId && params.communityId.length) {
					params.communityId.filter(commiunity => {
						ids.push(toObjectId(commiunity));
					});
				}
				wellLogSave = {};
				//sender object data save
				wellLogSave['senderId'] = toObjectId(tokenData.userId); //sender id means user if who will get the list  
				// if(ids)wellLogSave['receiverId'] = ids;
				if (ids) wellLogSave['taggedUser'] = ids;
				if (params.hostId) wellLogSave['hostId'] = toObjectId(params.hostId);
				wellLogSave['wishId'] = data._id ? data._id : "";
				wellLogSave['subjectId'] = data._id; //wish create
				wellLogSave['subjectDetail'] = {
					'notes': data.description ? data.description : "", //description of wish,
					'type': data.wishType ? data.wishType : "",      //wish type
					'wishImage': params.image ? params.image : ""
				}
				wellLogSave['type'] = NOTIFICATION_TYPE.CREATE_WISHES;
				wellLogSave['created'] = Date.now();
				wellLogSave['createdAt'] = new Date(Date.now());
				// wellLogSave['isEdited'] = isEdit;
				saveArr.push(wellLogSave);
			// }
			try {
				await wishesDaoV2.wellLogSave(saveArr);
			} catch (err) {
				console.log(err);
			}
			return true;
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function removeDuplicateV2
	 * @description function to remove duplicates
	 */
	async removeDuplicateV2(communityUsers) {
		const groupedData = {};

		communityUsers.sort((a, b) => {
			if (a.status > b.status) {
				return -1;
			} else if (a.status < b.status) {
				return 1;
			}
			return 0;
		});

		communityUsers.forEach((item) => {
			const { _id, userId, communityId, status, creatorId, type, createdAt, phoneNumber, countryCode } = item;

			if (!groupedData[userId ? userId : countryCode + phoneNumber]) {
				groupedData[userId ? userId : countryCode + phoneNumber] = { communityIdList: [] };
			}

			groupedData[userId ? userId : countryCode + phoneNumber].communityIdList.push(communityId);
			groupedData[userId ? userId : countryCode + phoneNumber]._id = _id;
			groupedData[userId ? userId : countryCode + phoneNumber].communityId = communityId;
			groupedData[userId ? userId : countryCode + phoneNumber].status = status;
			groupedData[userId ? userId : countryCode + phoneNumber].creatorId = creatorId;
			groupedData[userId ? userId : countryCode + phoneNumber].type = type;
			groupedData[userId ? userId : countryCode + phoneNumber].createdAt = createdAt;
			groupedData[userId ? userId : countryCode + phoneNumber].phoneNumber = phoneNumber;
			groupedData[userId ? userId : countryCode + phoneNumber].countryCode = countryCode;
		});

		const result = Object.keys(groupedData).map((userId) => ({
			_id: groupedData[userId]._id,
			userId,
			communityId: groupedData[userId].communityId,
			status: groupedData[userId].status,
			creatorId: groupedData[userId].creatorId,
			type: groupedData[userId].type,
			createdAt: groupedData[userId].createdAt,
			communityIdList: groupedData[userId].communityIdList,
			phoneNumber: groupedData[userId].phoneNumber,
			countryCode: groupedData[userId].countryCode,
		}));

		return result;
	}
}
export const wishesController = new WishesController();