"use strict";

import * as nodemailer from "nodemailer";
import { timeConversion } from "@utils/appUtils";
import { TEMPLATES, SERVER,MAIL_SENDING_TYPE,REPORT_TYPE,ADMIN_REPORT_REDIRECT} from "@config/index";
import { TemplateUtil } from "@utils/TemplateUtil";
import { sendMessageToFlock } from "@utils/FlockUtils";
import * as sgTransport from "nodemailer-sendgrid-transport";
import * as ses from "nodemailer-ses-transport";
// using smtp
const transporter = nodemailer.createTransport({
	host: SERVER.MAIL.SMTP.HOST,
	port: SERVER.MAIL.SMTP.PORT,
	secure: false, // use SSL
	//	requireTLS: true,
	auth: {
		user: SERVER.MAIL.SMTP.USER,
		pass: SERVER.MAIL.SMTP.PASSWORD
	}
});
// using ses
// create transporter by nodemailer
const sesTransporter = nodemailer.createTransport(ses({
	accessKeyId: SERVER.MAIL.SES.SES_ACCESS_KEY_ID,//"YOUR_AMAZON_KEY",
	secretAccessKey: SERVER.MAIL.SES.SES_SECRETE_KEY_ID,
	region:SERVER.MAIL.SES.REGION

}));
// using sendgrid
const options = {
	auth: {
		// api_user: SERVER.MAIL.SENDGRID.API_USER,
		api_key: SERVER.MAIL.SENDGRID.API_KEY
	}
};
const client = nodemailer.createTransport(sgTransport(options));
export class MailManager {
	private fromEmail: string = SERVER.MAIL.SMTP.FROM_MAIL;
	private SesfromEmail: string = SERVER.MAIL.SES.SES_FROM_MAIL;
	private sendgridFromEmail: string = SERVER.MAIL.SENDGRID.SENDGRID_FROM_MAIL;
	async sendMailViaSmtp(params) {
		console.log(this.fromEmail,'222222222222222222222222222222222')
		const mailOptions = {
			from: `${SERVER.APP_NAME} <${this.fromEmail}>`, // sender email
			to: params.email, // list of receivers
			subject: params.subject, // Subject line
			html: params.content,
			headers: {url:SERVER.APP_URL}
		};
		return new Promise(function (resolve, reject) {
			return transporter.sendMail(mailOptions, function (error, info) {
				if (error) {
					console.log(SERVER.MAIL.SMTP.FROM_MAIL,"FROM EMAIL ========================>>>>>>>>>>>")
					console.error("sendMail==============>", error);
					sendMessageToFlock({ "title": "sendMail", "error": error });
					resolve(SERVER.ENVIRONMENT !== "production" ? true : false);
				} else {
					console.log("Message sent: " + info.response);
					resolve(true);
				}
			});
		});
	}


	/**
	 * @function sendMail
	 * @description use to choose the   mail sending type
	 * @param params 
	 * @returns return the send mail function
	 */
	async sendMail(params) {
		if (SERVER.MAIL_TYPE === MAIL_SENDING_TYPE.SES) {
			console.log(this.SesfromEmail,"check here")
			return this.sendMailViaAmazonSes(params);
		} 
		if(SERVER.MAIL_TYPE === MAIL_SENDING_TYPE.SENDGRID){
			return this.sendMailViaSendgrid(params);
		}
		if(SERVER.MAIL_TYPE === MAIL_SENDING_TYPE.SMTP) {
			console.log(this.fromEmail,'111111111111111111111111111111')
			return this.sendMailViaSmtp(params);
		}
	}
	/**
	 * @function sendMailViaAmazonSes
	 * @description use to send mail by amazon ses
	 * @param params 
	 * @returns 
	 */
	async sendMailViaAmazonSes(params) {
		try {
			sesTransporter.sendMail({
				from: `${SERVER.APP_NAME}<${this.SesfromEmail}>`,
				to: params.email,
				subject: params.subject,
				html: params.content,
				// attachments:params.attachments,
			});
		} catch (err) {
			console.log(err)
		}

		return {};
	}
	/**
	 * @function sendMailViaSendgrid
	 * @description use to send the mail by sendgrid
	 * @param params 
	 * @returns 
	 */
	async sendMailViaSendgrid(params) {
		try {
			const mailOptions = {
				from: `${SERVER.APP_NAME} <${this.sendgridFromEmail}>`, // sender email
				to: params.email, // list of receivers
				subject: params.subject, // Subject line
				html: params.content,
				// attachments:params.attachments,
			};
			await client.sendMail(mailOptions);
		} catch (error) {
			console.log(error);
		}
		return {};
	}
	async forgotPasswordMail(params) {
		const mailContent = await (new TemplateUtil(SERVER.TEMPLATE_PATH + "forgot-password-user.html"))
			.compileFile({
				"url": params.url,
				"name": params.name,
				"validity": timeConversion(SERVER.TOKEN_INFO.EXPIRATION_TIME.FORGOT_PASSWORD),
				"apiUrl": process.env["APP_URL"]
			});

		return await this.sendMail({
			"email": params.email,
			"subject": TEMPLATES.EMAIL.SUBJECT.FORGOT_PASSWORD,
			"content": mailContent
		});
	}
	async investorInvitationMail(params) {
		const mailContent = await (new TemplateUtil(SERVER.TEMPLATE_PATH + "investor-invitation.html"))
			.compileFile({
				"name": params.name,
				"email": params.email,
				"randomString": params.randomString,
				"link": process.env["ADMIN_URL"],
				"apiUrl": process.env["APP_URL"]
			});

		return await this.sendMail({
			"email": params.email,
			"subject": TEMPLATES.EMAIL.SUBJECT.INVESTOR_INVITATION,
			"content": mailContent
		});
	}
	
	async incidenReportdMail(params) {
		let endPoint;
		let url;
		if(params.type==REPORT_TYPE.BLESSING) endPoint = ADMIN_REPORT_REDIRECT.BLESSING_REPORT_ENDPOINT+btoa(params.subjectId)
		if(params.type==REPORT_TYPE.GRATITUDE) endPoint = ADMIN_REPORT_REDIRECT.GRATITUDE_REPORT_ENDPOINT+btoa(params.subjectId)
		if(params.type==REPORT_TYPE.WISH) endPoint = ADMIN_REPORT_REDIRECT.WISH_REPORT_ENDPOINT+btoa(params.subjectId)
		if(params.type==REPORT_TYPE.COMMUNITY) endPoint = ADMIN_REPORT_REDIRECT.COMMUNITY_REPORT_ENDPOINT+btoa(params.subjectId)
		 url =SERVER.ADMIN_URL+endPoint;
		const mailContent = await (new TemplateUtil(SERVER.TEMPLATE_PATH + "incident-report.html"))
			.compileFile({
				"link": url,
				"email":SERVER.MAIL.SMTP.FROM_MAIL,
				"apiUrl": process.env["APP_URL"]
			});
		return await this.sendMail({
			"email": SERVER.ADMIN_CREDENTIALS.EMAIL,
			"subject": TEMPLATES.EMAIL.SUBJECT.INCIDENT_REPORT,
			"content": mailContent
		});
	}
	/**
	 * @function accountBlocked
	 * @description user account have been blocked
	 */
	async accountBlocked(payload) {
		let mailContent = await (new TemplateUtil(SERVER.TEMPLATE_PATH + "account-blocked.html"))
			.compileFile({
				"name": payload?.name,
				"reason": payload.reason
			});

		return await this.sendMail({
			"email": payload.email,
			"subject": TEMPLATES.EMAIL.SUBJECT.ACCOUNT_BLOCKED,
			"content": mailContent
		});
	}
	/**
	 * @function welcomeEmail
	 * @description send welcome email to user after profile completion
	 * @param params.email: user's email
	 * @param params.name: user's name
	 */
	async welcomeEmail(params) {
		const mailContent = await (new TemplateUtil(SERVER.TEMPLATE_PATH + "welcome-email.html"))
			.compileFile({
				"name": params.name
			}); 
		return await this.sendMail({
			"email": params.email,
			"subject": TEMPLATES.EMAIL.SUBJECT.WELCOME,
			"content": mailContent
		});
	}
	
	/**
	 * @function magicLinkSignUp
	 * @description user account have been rejected
	 */
	async magicLinkSignUp(payload) {
		let mailContent = await (new TemplateUtil(SERVER.TEMPLATE_PATH + "magic-link-signup.html"))
			.compileFile({
				"email": payload.email,
	 			"validity": timeConversion(SERVER.TOKEN_INFO.EXPIRATION_TIME.MAGIC_LINK),
				"deeplink": payload.deeplink,
				"apiUrl": process.env["APP_URL"]
			});
		return await this.sendMail({
			"email": payload.email,
			"subject": TEMPLATES.EMAIL.SUBJECT.MAGIC_LINK,
			"content": mailContent
		});
	}

	/**
	 * @function verifyUserEmail
	 * @description verify user email after signup
	 */
	async verifyUserEmail(payload) {
		let mailContent = await (new TemplateUtil(SERVER.TEMPLATE_PATH + "user-verify-email.html"))
			.compileFile({
				"email": payload.email,
	 			"validity": timeConversion(SERVER.TOKEN_INFO.EXPIRATION_TIME.VERIFY_USER_EMAIL),
				"deeplink": payload.deeplink,
				"apiUrl": process.env["APP_URL"]
			});
		return await this.sendMail({
			"email": payload.email,
			"subject": TEMPLATES.EMAIL.SUBJECT.VERIFY_USER_EMAIL,
			"content": mailContent
		});
	}

	/**
	 * @function magicLinkSignIn
	 * @description user account have been rejected
	 */
	async magicLinkSignIn(payload) {
		let mailContent = await (new TemplateUtil(SERVER.TEMPLATE_PATH + "magic-link-signin.html"))
			.compileFile({
				"email": payload.email,
	 			"validity": timeConversion(SERVER.TOKEN_INFO.EXPIRATION_TIME.MAGIC_LINK),
				"deeplink": payload.deeplink,
				"apiUrl": process.env["APP_URL"]
			});
		return await this.sendMail({
			"email": payload.email,
			"subject": TEMPLATES.EMAIL.SUBJECT.MAGIC_LINK,
			"content": mailContent
		});
	}

	/**
	 * @function thanksWishwellMail
	 * @description tirgger mail after user perform thank wishwell
	 */
	async thanksWishwellMail(data,params) {
		let mailContent = await (new TemplateUtil(SERVER.TEMPLATE_PATH + "thanks-wishwell.html"))
			.compileFile({
				"name": data.name,
				"note":params.notes?params.note:"",
				"amount":params.amount,
				"apiUrl": process.env["APP_URL"]
	 			
			});
		return await this.sendMail({
			"email": SERVER.ADMIN_CREDENTIALS.EMAIL,
			"subject": TEMPLATES.EMAIL.SUBJECT.THANKS_WISHWELL,
			"content": mailContent
		});
	}

	
	
}
export const mailManager = new MailManager();