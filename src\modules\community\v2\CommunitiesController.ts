"use strict";
import { MESSAGES, STATUS, WISH_TAG_TYPE, SMS_CONTENT, MESSAGES_NOTIFICATION, TITLE_NOTIFICATION, TYPE_NOTIFICATION, SQS_TYPES } from "@config/constant";
import { SERVER, DEEPLINK_TYPE } from "@config/index";
import { communitiesDaoV2 } from "@modules/community/index";
import { userDaoV1 } from "@modules/user/index";
import { toObjectId } from "@utils/appUtils";
import { baseDao } from "@modules/baseDao/index";
import { notificationManager } from "@utils/NotificationManager";
import { awsSQS } from "@lib/AwsSqs";
import { remindersDao } from "@modules/wishesReminder";

export class CommunitiesController {
	/**
	 * @function addCommunities
	 * @description here are adding the community.
	 */
	async addCommunities(params: CommunitiesRequest.Add, tokenData: TokenData) {
		try {
			const step1 = await userDaoV1.findUserById(tokenData.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			params.status = STATUS.ACTIVE;
			params.userId = tokenData.userId
			let step2 = await communitiesDaoV2.addCommunities(params);
			let registeredUser = [], nonRegisteredUser = [];
			if (params.members && params.members.length !== 0) {
				for (let i = 0; params.members.length > i; i++) {
					if (params.members[i]['isAppUser']) {
						registeredUser.push(params.members[i])
					} else {
						nonRegisteredUser.push(params.members[i])
					}
				}
			}
			let contactArray = [];
			let pushArray = [];
			let obj = {};
			obj['userId'] = toObjectId(tokenData.userId);
			obj['communityId'] = step2._id;
			obj['status'] = STATUS.ACTIVE;
			obj['creatorId'] = toObjectId(tokenData.userId);
			obj['type'] = WISH_TAG_TYPE.CREATOR;
			obj['createdAt'] = new Date();
			obj['created'] = new Date().getTime();
			contactArray.push(obj);
			if (registeredUser && registeredUser.length !== 0) {
				for (let i = 0; registeredUser.length > i; i++) {
					let obj = {};
					obj['userId'] = toObjectId(registeredUser[i]['userId']);
					obj['communityId'] = step2._id;
					obj['status'] = STATUS.PENDING;
					obj['creatorId'] = toObjectId(tokenData.userId);
					obj['type'] = WISH_TAG_TYPE.CONTACTS
					obj['createdAt'] = new Date();
					obj['created'] = new Date().getTime();
					contactArray.push(obj);
					pushArray.push(obj);
				}
			}
			if (nonRegisteredUser && nonRegisteredUser.length !== 0) {
				for (let i = 0; nonRegisteredUser.length > i; i++) {
					let obj = {};
					obj['communityId'] = step2._id;
					obj['countryCode'] = nonRegisteredUser[i].countryCode;
					obj['phoneNumber'] = nonRegisteredUser[i].phoneNumber;
					obj['status'] = STATUS.PENDING;
					obj['type'] = WISH_TAG_TYPE.INVITED
					obj['contactId'] = toObjectId(nonRegisteredUser[i]['contactId']);
					obj['createdAt'] = new Date();
					obj['created'] = new Date().getTime();
					contactArray.push(obj);

					// REMOVED SMS

				}
			}
			console.log("contactArray : ", contactArray);
			console.log("pushArray : ", pushArray);
			const members = await communitiesDaoV2.addMember(contactArray);

			if(members.ops) {
				for(const member of members.ops) {
					if(member.type === WISH_TAG_TYPE.INVITED) {
						const reminderObj = {
							memberId: member._id,
							communityId: member.communityId,
							communityName: step2.name,
							countryCode: member.countryCode,
							phoneNumber: member.phoneNumber,
							isCommunityInvite: true,
							userId: step2.userId,
						};
						const reminder = await remindersDao.addReminders(reminderObj);
						console.log('REMINDER', reminder);
					}
				}
			}

			const communityName = step2.name ? step2.name : "";
			const creatorName = step1.name ? step1.name : "";
			let pushParams = { "message": MESSAGES_NOTIFICATION.COMMUNITY_INVITE3.replace("[CREATOR_NAME]", creatorName).replace('[COMMUNITY_NAME]', params.name), "title": TITLE_NOTIFICATION.COMMUNITY_INVITE.replace("[NAME]", communityName), "type": TYPE_NOTIFICATION.COMMUNITY_INVITE }
			await notificationManager.PublishNotification(pushArray, step2._id, pushParams);
			return MESSAGES.SUCCESS.COMMUNITY_CREATED;
		} catch (error) {
			console.log(error);
			throw error;
		}
	}
	/**
	 * @function editCommunity
	 * @description here are editing the community.
	 */
	async editCommunity(params: CommunitiesRequest.EditCommunity, tokenData: TokenData) {
		try {
			let step1 = await userDaoV1.findUserById(tokenData.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			let step2 = await communitiesDaoV2.findCommunityById(params.communityId);
			if (!step2) {
				return Promise.reject(MESSAGES.ERROR.COMMUNITY_NOT_FOUND);
			} else if ((step2.userId == tokenData.userId)) {
				//remove members
				let obj = {}
				if (params.name) obj['name'] = params.name
				if (params.image) obj['image'] = params.image
				if (params.purpose) obj['purpose'] = params.purpose
				await baseDao.updateOne("communities", { _id: toObjectId(params.communityId) }, { '$set': obj }, {});
				let registeredUser = [], nonRegisteredUser = [];
				if (params.removeMembers && params.removeMembers.length !== 0) {
					params.removeMembers.forEach(element => {
						if (element['isAppUser']) {
							registeredUser.push(element['userId'])
						} else {
							nonRegisteredUser.push(element['phoneNumber'])
						}
					});
				}
				let users1 = await baseDao.find("members", { communityId: params.communityId, userId: { '$in': registeredUser } }, { _id: 1 });
				let users2 = await baseDao.find("members", { communityId: params.communityId, phoneNumber: { '$in': nonRegisteredUser } }, { _id: 1 });
				let resultRepo = users1.concat(users2)
				await baseDao.updateMany("members", { _id: { '$in': resultRepo } }, { '$set': { status: STATUS.DELETED } }, {});
				await baseDao.updateMany("tagwishes", { communityId: params.communityId, userId: { '$in': registeredUser } }, { '$set': { status: STATUS.DELETED } }, {}); // remove community member tagged on wish

				//add members
				let contactArray = [];
				let pushArray = [];
				registeredUser = [], nonRegisteredUser = [];
				let communityName = params.name ? params.name : step2.name;
				if (params.addMembers && params.addMembers.length !== 0) {
					params.addMembers.forEach(element => {
						if (element['isAppUser']) {
							registeredUser.push(element['userId'])
						} else {
							nonRegisteredUser.push({ phoneNumber: element['phoneNumber'], countryCode: element['countryCode'], contactId: element['contactId'] })
						}
					});
					if (registeredUser && registeredUser.length !== 0) {
						for (let i = 0; registeredUser.length > i; i++) {
							let obj = {};
							obj['userId'] = toObjectId(registeredUser[i]);
							obj['communityId'] = step2._id;
							obj['type'] = WISH_TAG_TYPE.CONTACTS;
							obj['status'] = STATUS.PENDING;
							obj['createdAt'] = new Date();
							obj['created'] = Date.now();
							contactArray.push(obj);
							pushArray.push(obj);
						}
					}
					if (nonRegisteredUser && nonRegisteredUser.length !== 0) {
						for (let i = 0; nonRegisteredUser.length > i; i++) {
							let obj = {
								communityId: step2._id,
								type: WISH_TAG_TYPE.INVITED,
								countryCode: nonRegisteredUser[i].countryCode,
								phoneNumber: nonRegisteredUser[i].phoneNumber,
								status: STATUS.PENDING,
								contactId: nonRegisteredUser[i].contactId,
								createdAt: new Date(),
								created: Date.now(),
								notificationReminder24Hours: new Date(Date.now() + 1 * 60 * 1000),
								notificationReminder72Hours: new Date(Date.now() + 2 * 60 * 1000),
								notificationReminder120Hours: new Date(Date.now() + 3 * 60 * 1000),
								notificationReminder24HoursStatus: false,
								notificationReminder72HoursStatus: false,
								notificationReminder120HoursStatus: false
							};
							// obj['communityId'] = step2._id;
							// obj['type'] = WISH_TAG_TYPE.INVITED;
							// obj['countryCode'] = nonRegisteredUser[i].countryCode;
							// obj['phoneNumber'] = nonRegisteredUser[i].phoneNumber;
							// obj['status'] = STATUS.PENDING;
							// obj['contactId'] = nonRegisteredUser[i].contactId;
							// obj['createdAt'] = new Date();
							// obj['created'] = Date.now();

							// obj['notificationReminder24Hours'] = new Date(Date.now() + 1 * 60 * 1000);
							// obj['notificationReminder72Hours'] = new Date(Date.now() + 2 * 60 * 1000);
							// obj['notificationReminder120Hours'] = new Date(Date.now() + 3 * 60 * 1000);
							// obj['notificationReminder24HoursStatus'] = false;
							// obj['notificationReminder72HoursStatus'] = false;
							// obj['notificationReminder120HoursStatus'] = false;

							contactArray.push(obj);

							// REMOVED SMS

						}
					}
				}
				let pushParams = { "message": MESSAGES_NOTIFICATION.COMMUNITY_INVITE1 + communityName + MESSAGES_NOTIFICATION.COMMUNITY_INVITE2, "title": TITLE_NOTIFICATION.COMMUNITY_INVITE, "type": TYPE_NOTIFICATION.COMMUNITY_INVITE }
				await notificationManager.PublishNotification(pushArray, step2._id, pushParams);
				if (contactArray) await communitiesDaoV2.addMember(contactArray);
			} else {
				return Promise.reject(MESSAGES.ERROR.SOMETHING_WENT_WRONG);
			}
			return MESSAGES.SUCCESS.EDIT_COMMUNITY;
		} catch (error) {
			throw error;
		}
	}

	async getPublicCommunities(params: any) {
		// You can implement reportedContent exclusion here in the future if needed
		return await communitiesDaoV2.getPublicCommunities(params);
	}

	async getPublicCommunityDetail(params: any) {
		const { communityId } = params;
		return await communitiesDaoV2.getPublicCommunityById(communityId);
	}

	/**
	 * @function requestToJoinCommunity
	 * @description allows a user to request to join a community
	 */
	async requestToJoinCommunity(params: CommunitiesRequest.JoinRequest, tokenData: TokenData) {
		try {
			// Check if user exists
			const user = await userDaoV1.findUserById(tokenData.userId);
			if (!user) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			
			// Check if community exists
			const community = await communitiesDaoV2.findCommunityById(params.communityId);
			if (!community) return Promise.reject(MESSAGES.ERROR.COMMUNITY_NOT_FOUND);
			
			// Check if community is public
			if (community.visibility !== "PUBLIC") {
				return Promise.reject(MESSAGES.ERROR.COMMUNITY_NOT_PUBLIC);
			}
			
			// Check if user is already a member or has a pending request
			const existingMembership = await baseDao.findOne("members", {
				communityId: params.communityId,
				userId: tokenData.userId,
				status: { $in: [STATUS.ACTIVE, STATUS.PENDING] }
			}, {});
			
			if (existingMembership) {
				if (existingMembership.status === STATUS.ACTIVE) {
					return Promise.reject(MESSAGES.ERROR.ALREADY_MEMBER);
				} else {
					return Promise.reject(MESSAGES.ERROR.JOIN_REQUEST_ALREADY_SENT);
				}
			}
			
			// Create join request
			const memberObj = {
				userId: toObjectId(tokenData.userId),
				communityId: toObjectId(params.communityId),
				type: WISH_TAG_TYPE.JOIN_REQUEST,
				status: STATUS.PENDING,
				createdAt: new Date(),
				created: Date.now()
			};
			
			await communitiesDaoV2.addMember([memberObj]);
			
			// Send notification to community owner
			const pushParams = {
				"message": `${user.firstName} ${user.lastName} has requested to join your community "${community.name}"`,
				"title": TITLE_NOTIFICATION.COMMUNITY_JOIN_REQUEST,
				"type": TYPE_NOTIFICATION.COMMUNITY_JOIN_REQUEST
			};
			
			// Use PublishNotification instead of sendNotification
			await notificationManager.PublishNotification([{"userId": community.userId}], community._id, pushParams);
			
			return MESSAGES.SUCCESS.JOIN_REQUEST_SENT;
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function respondToJoinRequest
	 * @description allows a community owner to accept or reject join requests
	 */
	async respondToJoinRequest(params: CommunitiesRequest.JoinRequestResponse, tokenData: TokenData) {
		try {
			// Check if user exists
			const user = await userDaoV1.findUserById(tokenData.userId);
			if (!user) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			
			// Check if community exists and user is the owner
			const community = await communitiesDaoV2.findCommunityById(params.communityId);
			if (!community) return Promise.reject(MESSAGES.ERROR.COMMUNITY_NOT_FOUND);
			
			if (community.userId.toString() !== tokenData.userId) {
				return Promise.reject(MESSAGES.ERROR.NOT_AUTHORIZED);
			}
			
			// Check if join request exists
			const joinRequest = await baseDao.findOne("members", {
				communityId: params.communityId,
				userId: params.userId,
				type: WISH_TAG_TYPE.JOIN_REQUEST,
				status: STATUS.PENDING
			}, {});
			
			if (!joinRequest) {
				return Promise.reject(MESSAGES.ERROR.JOIN_REQUEST_NOT_FOUND);
			}
			
			// Update join request status
			if (params.accept) {
				await baseDao.updateOne("members", 
					{ _id: joinRequest._id }, 
					{ $set: { status: STATUS.ACTIVE, type: WISH_TAG_TYPE.CONTACTS } }, 
					{}
				);
				
				// Send notification to requester
				const requester = await userDaoV1.findUserById(params.userId);
				const pushParams = {
					"message": `Your request to join "${community.name}" has been accepted`,
					"title": TITLE_NOTIFICATION.COMMUNITY_JOIN_ACCEPTED,
					"type": TYPE_NOTIFICATION.COMMUNITY_JOIN_ACCEPTED
				};
				
				// Use PublishNotification instead of sendNotification
				await notificationManager.PublishNotification([{"userId": params.userId}], community._id, pushParams);
				
				return MESSAGES.SUCCESS.JOIN_REQUEST_ACCEPTED;
			} else {
				await baseDao.updateOne("members", 
					{ _id: joinRequest._id }, 
					{ $set: { status: STATUS.REJECTED } }, 
					{}
				);
				
				// Send notification to requester
				const requester = await userDaoV1.findUserById(params.userId);
				const pushParams = {
					"message": `Your request to join "${community.name}" has been declined`,
					"title": TITLE_NOTIFICATION.COMMUNITY_JOIN_DECLINED,
					"type": TYPE_NOTIFICATION.COMMUNITY_JOIN_DECLINED
				};
				
				// Use PublishNotification instead of sendNotification
				await notificationManager.PublishNotification([{"userId": params.userId}], community._id, pushParams);
				
				return MESSAGES.SUCCESS.JOIN_REQUEST_DECLINED;
			}
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function getJoinRequests
	 * @description get all pending join requests for a community
	 */
	async getJoinRequests(params: CommunitiesRequest.JoinRequestsList, tokenData: TokenData) {
		try {
			// Check if user exists
			const user = await userDaoV1.findUserById(tokenData.userId);
			if (!user) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			
			// Check if community exists and user is the owner
			const community = await communitiesDaoV2.findCommunityById(params.communityId);
			if (!community) return Promise.reject(MESSAGES.ERROR.COMMUNITY_NOT_FOUND);
			
			if (community.userId.toString() !== tokenData.userId) {
				return Promise.reject(MESSAGES.ERROR.NOT_AUTHORIZED);
			}
			
			// Get join requests with pagination
			const pageNo = params.pageNo || 1;
			const limit = params.limit || 10;
			const skip = (pageNo - 1) * limit;
			
			const joinRequests = await baseDao.aggregate("members", [
				{
					$match: {
						communityId: toObjectId(params.communityId),
						type: WISH_TAG_TYPE.JOIN_REQUEST,
						status: STATUS.PENDING
					}
				},
				{
					$lookup: {
						from: "users",
						localField: "userId",
						foreignField: "_id",
						as: "userDetails"
					}
				},
				{
					$unwind: "$userDetails"
				},
				{
					$project: {
						_id: 1,
						userId: 1,
						communityId: 1,
						status: 1,
						createdAt: 1,
						"userDetails.firstName": 1,
						"userDetails.lastName": 1,
						"userDetails.profilePicture": 1
					}
				},
				{
					$skip: skip
				},
				{
					$limit: limit
				}
			]);
			
			const total = await baseDao.count("members", {
				communityId: params.communityId,
				type: WISH_TAG_TYPE.JOIN_REQUEST,
				status: STATUS.PENDING
			});
			
			return {
				data: joinRequests,
				total,
				pageNo,
				limit
			};
		} catch (error) {
			throw error;
		}
	}

}
export const communitiesController = new CommunitiesController();
