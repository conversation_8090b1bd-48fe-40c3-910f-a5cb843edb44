"use strict";
import { MESSAGES, STATUS, USER_TYPE, WISH_TAG_TYPE, WISH_TYPE,ACTION_TYPE, COMMUNITY_LISTING_TYPE, FLAG_TYPE, REPORT_TYPE, SMS_CONTENT, ADMIN_NOTIFICATION_TYPE, MESSAGES_NOTIFICATION, TITLE_NOTIFICATION, TYPE_NOTIFICATION, SQS_TYPES } from "@config/constant";
import { SERVER, DEEPLINK_TYPE } from "@config/index";
import { communitiesDaoV1 } from "@modules/community/index";
import {userDaoV1} from "@modules/user/index"
import { toObjectId } from "@utils/appUtils";
import { smsManager } from "@lib/SMSManager";
import { baseDao } from "@modules/baseDao/index";
import { reportDaoV1 } from "@modules/reports/index";
import { notificationManager } from "@utils/NotificationManager";
import { awsSQS } from "@lib/AwsSqs";

export class CommunitiesController {
    /**
	 * @function addCommunities
	 * @description here are adding the community.
	 */
	async addCommunities(params: CommunitiesRequest.Add,tokenData:TokenData) {
		try {
            const step1 = await userDaoV1.findUserById(tokenData.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			params.status= STATUS.ACTIVE;
			params.userId = tokenData.userId
			let step2 = await communitiesDaoV1.addCommunities(params);
            let registeredUser = [], nonRegisteredUser = [];
			if(params.members && params.members.length !== 0){
				for(let i = 0; params.members.length > i; i++){
					if(params.members[i]['isAppUser']){
						registeredUser.push(params.members[i])
					}else{
						nonRegisteredUser.push(params.members[i])
					}
				}
			}
            let contactArray=[];
			let pushArray = [];
            let obj = {};
					obj['userId'] = toObjectId(tokenData.userId);
					obj['communityId'] = step2._id;
					obj['status'] = STATUS.ACTIVE;
                    obj['creatorId'] = toObjectId(tokenData.userId);
                    obj['type']=WISH_TAG_TYPE.CREATOR;
					obj['createdAt'] = new Date();
					obj['created'] = new Date().getTime();
                    contactArray.push(obj);  
            if (registeredUser && registeredUser.length !== 0) {
                for (let i = 0; registeredUser.length > i; i++) {
					let obj = {};
					obj['userId'] = toObjectId(registeredUser[i]['userId']);
					obj['communityId'] = step2._id;
					obj['status'] = STATUS.PENDING;
                    obj['creatorId'] = toObjectId(tokenData.userId);
                    obj['type']=WISH_TAG_TYPE.CONTACTS
					obj['createdAt'] = new Date();
					obj['created'] = new Date().getTime();
					//obj['contactId']=toObjectId(registeredUser[i]['contactId']);
                    contactArray.push(obj);
					pushArray.push(obj);  
                }
            }
			if (nonRegisteredUser && nonRegisteredUser.length !== 0) {
				for (let i = 0; nonRegisteredUser.length > i; i++) {
					let obj = {};
					obj['communityId'] = step2._id; 
					obj['countryCode'] = nonRegisteredUser[i].countryCode;
					obj['phoneNumber'] = nonRegisteredUser[i].phoneNumber;
					obj['status'] = STATUS.PENDING;
                    obj['type']=WISH_TAG_TYPE.INVITED
					obj['contactId']=toObjectId(nonRegisteredUser[i]['contactId']);
					obj['createdAt'] = new Date();
					obj['created'] = new Date().getTime();
                    contactArray.push(obj);  
					// const deeplink = `${SERVER.APP_URL}${SERVER.API_BASE_URL}/v1/common/deeplink?type=${DEEPLINK_TYPE.SMS_INVITE}`;  // deeplinking
					// await smsManager.sendMessageViaAWS(nonRegisteredUser[i].countryCode,nonRegisteredUser[i].phoneNumber,SMS_CONTENT.COMMUNITY_INVITE1+params.name+SMS_CONTENT.COMMUNITY_INVITE2+deeplink)
					// const contact = await userDaoV1.findUserById(nonRegisteredUser[i]['contactId']);
					// let user 
					// contact ? user = contact.name : user = 'user';
					// await awsSQS.signupMagicLinkProducer({"countryCode":nonRegisteredUser[i].countryCode, "phoneNumber":nonRegisteredUser[i].phoneNumber, "SMS_CONTENT":SMS_CONTENT.DEAR_USER+user+`\n`+SMS_CONTENT.COMMUNITY_INVITE1+params.name+SMS_CONTENT.COMMUNITY_INVITE2+deeplink+`\n`+SMS_CONTENT.WITH_GRAT+step1.name, "type":SQS_TYPES.COMMUNITY_CREATE_SMS})

                }
			}
            console.log("contactArray : ",contactArray);
            console.log("pushArray : ", pushArray);
			await communitiesDaoV1.addMember(contactArray);
			// let pushParams = {"message":MESSAGES_NOTIFICATION.COMMUNITY_INVITE1+params.name+MESSAGES_NOTIFICATION.COMMUNITY_INVITE2, "title":TITLE_NOTIFICATION.COMMUNITY_INVITE, "type":TYPE_NOTIFICATION.COMMUNITY_INVITE}
			const communityName = step2.name ? step2.name : "";
			const creatorName = step1.name ? step1.name : "";
			let pushParams = { "message": MESSAGES_NOTIFICATION.COMMUNITY_INVITE3.replace('[COMMUNITY_NAME]', params.name), "title": TITLE_NOTIFICATION.COMMUNITY_INVITE.replace("[NAME]", params.name), "type": TYPE_NOTIFICATION.COMMUNITY_INVITE }
			await notificationManager.PublishNotification(pushArray, step2._id, pushParams);
			return MESSAGES.SUCCESS.COMMUNITY_CREATED;
		} catch (error) {
			console.log(error);
			throw error;
		}
	}
    /**
	 * @function getCreatorCommunities
	 * @description here are getting the creator-community list.
	 *
	 */
	async getCreatorCommunities(params: CommunitiesRequest.CommunityList,tokenData:TokenData) {
		try {
			let step1 = await userDaoV1.findUserById(tokenData.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			//let step2 = await reportDaoV1.reportedWishes(REPORT_TYPE.COMMUNITY,tokenData.userId)
			let step3 = await communitiesDaoV1.getCreatorCommunities(params);
			return MESSAGES.SUCCESS.COMMUNITY_LIST(step3);
		} catch (error) {
			throw error;
		}
	}

    /**
	 * @function getCommunities
	 * @description here are getting the community list.
	 *
	 */
	async getCommunities(params: CommunitiesRequest.CommunityList,tokenData:TokenData) {
		try {
			let step1 = await userDaoV1.findUserById(tokenData.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
            params.userId = tokenData.userId
			let step2 = await reportDaoV1.reportedWishes(REPORT_TYPE.COMMUNITY,tokenData.userId)
			let step3 = await communitiesDaoV1.getCommunities(params,step2);
			// let step3 = await communitiesDaoV1.findMembers(params)
			// step2[0].members = step3
			return MESSAGES.SUCCESS.COMMUNITY_LIST(step3);
		} catch (error) {
			throw error;
		}
	}

    /**
	 * @function communityDetail
	 * @description here are getting the community-detail.
	 */
	async communityDetail(params: CommunitiesRequest.CommunityDetail,tokenData:TokenData) {
		try {
			let step1 = await userDaoV1.findUserById(tokenData.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			let step2 = await communitiesDaoV1.communityDetail(params);
			let step3 = await communitiesDaoV1.findMembers(params)

			for (const member of step3) {
				if(!member.userId) {
					member._id = member.contactId;
				}
			}

			step2[0].members = step3
			return MESSAGES.SUCCESS.COMMUNITY_DETAIL(step2[0]);
		} catch (error) {
			throw error;
		}
	}
    /**
	 * @function leaveCommunity
	 * @description for leaving the community.
	 */
	async leaveCommunity(params: CommunitiesRequest.LeaveCommunity,tokenData:TokenData) {
		try {
			let step1 = await userDaoV1.findUserById(tokenData.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			let step2 = await communitiesDaoV1.findCommunityById(params.communityId); 
			if (!step2) return Promise.reject(MESSAGES.ERROR.COMMUNITY_NOT_FOUND);
			if(step2.userId == tokenData.userId) return Promise.reject(MESSAGES.ERROR.SOMETHING_WENT_WRONG);

			await baseDao.updateMany("members",{communityId: toObjectId(params.communityId),userId:toObjectId(tokenData.userId)},{'$set' : {status:STATUS.REJECTED}},{});
			return MESSAGES.SUCCESS.COMMUNITY_UPDATED;
		} catch (error) {
			throw error;
		}
    }

    /**
	 * @function acceptDeclineCommunity
	 * @description for accept/reject the community.
	 */
	async acceptDeclineCommunity(params: CommunitiesRequest.AcceptDecline,tokenData:TokenData) {
		try {
			let step1 = await userDaoV1.findUserById(tokenData.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			let step2 = await communitiesDaoV1.findCommunityById(params.communityId); 
			if (!step2) return Promise.reject(MESSAGES.ERROR.COMMUNITY_NOT_FOUND);
			if(step2.userId == tokenData.userId) return Promise.reject(MESSAGES.ERROR.SOMETHING_WENT_WRONG);
			if(params.type == ACTION_TYPE.REJECT){
				await baseDao.updateOne("members",{communityId: toObjectId(params.communityId),userId:toObjectId(tokenData.userId),status:STATUS.PENDING},{'$set' : {status:STATUS.REJECTED}},{});
				await baseDao.updateOne("tagwishes",{communityIdList: toObjectId(params.communityId),userId:toObjectId(tokenData.userId),status:STATUS.PENDING},{'$set' : {status:STATUS.DELETED}},{});

			}else{
				await baseDao.updateOne("members",{communityId: toObjectId(params.communityId),userId:toObjectId(tokenData.userId),status:STATUS.PENDING},{'$set' : {status:STATUS.ACTIVE}},{});
				await baseDao.updateMany("tagwishes",{communityIdList: toObjectId(params.communityId),userId:toObjectId(tokenData.userId),status:STATUS.PENDING},{'$set' : {status:STATUS.ACTIVE}},{});
			}
			
			return MESSAGES.SUCCESS.COMMUNITY_UPDATED;
		} catch (error) {
			throw error;
		}
    }
	/**
	 * @function deleteCommunity
	 * @description here are deleting the community.
	 */
	async deleteCommunity(params: CommunitiesRequest.DeleteCommunity,tokenData:TokenData) {
		try {
			let step1 = await userDaoV1.findUserById(tokenData.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			let step2 = await communitiesDaoV1.findCommunityById(params.communityId); 
			if (!step2){ return Promise.reject(MESSAGES.ERROR.COMMUNITY_NOT_FOUND);
			}else if((step2.userId == tokenData.userId) || (step2.hostId == tokenData.userId)){
					await baseDao.updateOne("communities",{_id: toObjectId(params.communityId)},{'$set' : {status:STATUS.DELETED}},{});
					await baseDao.updateMany("members",{communityId: toObjectId(params.communityId)},{'$set' : {status:STATUS.DELETED}},{});
					// await baseDao.updateMany("tagwishes",{communityId: toObjectId(params.communityId)},{'$set' : {status:STATUS.DELETED}},{});
					await baseDao.updateMany("wishes",{ communityId: toObjectId(params.communityId), status: { "$ne": STATUS.DELETED } },{$pull: { communityId:  toObjectId(params.communityId) }},{});
					if(step1.communityCount > 0) {
						await baseDao.updateOne("users", { _id: step2.userId }, { $inc: { communityCount: -1 } }, {});
					}
									
					return MESSAGES.SUCCESS.DELETE_COMMUNITY;
			}else{
				return Promise.reject(MESSAGES.ERROR.SOMETHING_WENT_WRONG);
			}
		} catch (error) {
			throw error;
		}
		
	}
    /**
	 * @function editCommunity
	 * @description here are editing the community.
	 */
	async editCommunity(params:CommunitiesRequest.EditCommunity,tokenData:TokenData) {
		try {
			let step1 = await userDaoV1.findUserById(tokenData.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			let step2 = await communitiesDaoV1.findCommunityById(params.communityId); 
			if (!step2){ return Promise.reject(MESSAGES.ERROR.COMMUNITY_NOT_FOUND);
			}else if((step2.userId == tokenData.userId) ){
				//remove members
				let obj = {}
				if(params.name) obj['name']= params.name
				if(params.image) obj['image']= params.image
				if(params.purpose) obj['purpose']= params.purpose
				await baseDao.updateOne("communities",{_id: toObjectId(params.communityId)},{'$set' : obj},{});
				let registeredUser = [], nonRegisteredUser = [];
				if(params.removeMembers && params.removeMembers.length !== 0){
					params.removeMembers.forEach(element => {
						if(element['isAppUser']){
							registeredUser.push(element['userId'])
						}else{
							nonRegisteredUser.push(element['phoneNumber'])
						}
					});
			    }
				let users1 = await baseDao.find("members", { communityId: params.communityId, userId:{'$in':registeredUser}},{_id:1}  );
				let users2 = await baseDao.find("members", { communityId: params.communityId, phoneNumber:{'$in':nonRegisteredUser}},{_id:1}  );
				let resultRepo =users1.concat(users2)
				await baseDao.updateMany("members",{_id: {'$in':resultRepo}},{'$set' : {status: STATUS.DELETED}},{});
				await baseDao.updateMany("tagwishes",{communityId: params.communityId, userId:{'$in':registeredUser}},{'$set' : {status: STATUS.DELETED}},{}); // remove community member tagged on wish
				
				//add members
				let contactArray=[];
				let pushArray=[];
				registeredUser = [], nonRegisteredUser = [];
				let communityName = params.name ? params.name : step2.name;
				if(params.addMembers && params.addMembers.length !== 0){
					params.addMembers.forEach(element => {
						if(element['isAppUser']){
							registeredUser.push(element['userId'])
						}else{
							nonRegisteredUser.push({phoneNumber: element['phoneNumber'],countryCode: element['countryCode'],contactId: element['contactId'] })
						}
					});
					if (registeredUser && registeredUser.length !== 0) {
						for (let i = 0; registeredUser.length > i; i++) {
							let obj = {};
							obj['userId'] = toObjectId(registeredUser[i]);
							obj['communityId'] = step2._id ;
							obj['type'] = WISH_TAG_TYPE.CONTACTS;
							obj['status'] = STATUS.PENDING;
							obj['createdAt'] = new Date();
							obj['created'] = Date.now();
							contactArray.push(obj);  
							pushArray.push(obj); 
						}
					}
					if (nonRegisteredUser && nonRegisteredUser.length !== 0) {
						for (let i = 0; nonRegisteredUser.length > i; i++) {
							let obj = {};
							obj['communityId'] = step2._id ;
							obj['type'] = WISH_TAG_TYPE.INVITED;
							obj['countryCode'] = nonRegisteredUser[i].countryCode;
							obj['phoneNumber'] = nonRegisteredUser[i].phoneNumber;
							obj['status'] = STATUS.PENDING;
							obj['contactId'] = nonRegisteredUser[i].contactId;
							obj['createdAt'] = new Date();
							obj['created'] = Date.now();
							contactArray.push(obj);  
							// const deeplink = `${SERVER.APP_URL}${SERVER.API_BASE_URL}/v1/common/deeplink?type=${DEEPLINK_TYPE.SMS_INVITE}`; 
							// await smsManager.sendMessageViaAWS(nonRegisteredUser[i].countryCode,nonRegisteredUser[i].phoneNumber,"you have been invited on wishwell" )
							// await awsSQS.signupMagicLinkProducer({"countryCode":nonRegisteredUser[i].countryCode, "phoneNumber":nonRegisteredUser[i].phoneNumber, "SMS_CONTENT":"you have been invited on wishwell", "type":SQS_TYPES.COMMUNITY_EDIT_SMS});
							// const contact = await userDaoV1.findUserById(nonRegisteredUser[i]['contactId']);
							// let user 
							// contact ? user = contact.name : user = 'user';
							// await awsSQS.signupMagicLinkProducer({"countryCode":nonRegisteredUser[i].countryCode, "phoneNumber":nonRegisteredUser[i].phoneNumber, "SMS_CONTENT":SMS_CONTENT.DEAR_USER+user+`\n`+SMS_CONTENT.COMMUNITY_INVITE1+communityName+SMS_CONTENT.COMMUNITY_INVITE2+deeplink+`\n`+SMS_CONTENT.WITH_GRAT+step1.name, "type":SQS_TYPES.COMMUNITY_CREATE_SMS})

						}
					}
			    }
				// let pushParams = {"message":MESSAGES_NOTIFICATION.COMMUNITY_INVITE1+communityName+MESSAGES_NOTIFICATION.COMMUNITY_INVITE2, "title":TITLE_NOTIFICATION.COMMUNITY_INVITE, "type":TYPE_NOTIFICATION.COMMUNITY_INVITE}
				let pushParams = {"message":MESSAGES_NOTIFICATION.COMMUNITY_INVITE3.replace('[COMMUNITY_NAME]', communityName), "title":TITLE_NOTIFICATION.COMMUNITY_INVITE.replace('[NAME]', communityName), "type":TYPE_NOTIFICATION.COMMUNITY_INVITE}
				await notificationManager.PublishNotification(pushArray, step2._id, pushParams);
				if(contactArray) await communitiesDaoV1.addMember(contactArray);
			}else{
				return Promise.reject(MESSAGES.ERROR.SOMETHING_WENT_WRONG);
			}
			return MESSAGES.SUCCESS.EDIT_COMMUNITY;
		} catch (error) {
			throw error;
		}
		
	}

	/**
	 * @function communityListing
	 * @description admin community listing
	 */
	async communityListing(params: CommunitiesRequest.CommunityListing) {
		try {
			const step1 = await communitiesDaoV1.communityListing(params);
			if(params.type == COMMUNITY_LISTING_TYPE.USER) {
				const step2 = await userDaoV1.findUserById(params.userId, { _id: 1, firstName: 1, lastName: 1 });
				step1["userDetails"] = step2;
			}
			return MESSAGES.SUCCESS.COMMUNITY_LIST(step1);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function communityDetailAdmin
	 * @description admin community details
	 */
	async communityDetailAdmin(params: CommunitiesRequest.CommunityId) {
		try {
			// const step1 = await communitiesDaoV1.findCommunityById(params.communityId);
			// if(!step1) {
			// 	return Promise.reject(MESSAGES.ERROR.COMMUNITY_NOT_FOUND);
			// }
			const step2 = await communitiesDaoV1.communityDetailAdmin(params);
			return MESSAGES.SUCCESS.COMMUNITY_DETAIL(step2);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function flaggedCommunityReports
	 * @description admin flagged community reports
	 */
	async flaggedCommunityReports(params: CommunitiesRequest.CommunityListing) {
		try {
			const step1 = await communitiesDaoV1.flaggedCommunityReports(params);
			return MESSAGES.SUCCESS.FLAGGED_COMMUNITY_REPORT(step1);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function unflagDeleteCommunity
	 * @description admin unflag/delete flagged community
	 */
	async unflagDeleteCommunity(params: CommunitiesRequest.UnflagDeleteCommunity, tokenData: TokenData) {
		try {
			const step1 = await communitiesDaoV1.findCommunityById(params.id);
            const step2 = await communitiesDaoV1.unflagDeleteCommunity(params, tokenData);
            if(params.status == FLAG_TYPE.UN_FLAGGED) {
				// await communitiesDaoV1.unflagMembers(params)
				await baseDao.updateMany("reports",{subjectId: toObjectId(params.id)},{'$set' : {status:STATUS.ACTIVE}},{});
                return MESSAGES.SUCCESS.UNFLAG_COMMUNITY;
            } else {
				const step2 = await baseDao.findOne("communities", { _id: toObjectId(params.id) }, { userId: 1 });
				if(step2) {
					const step3 = await baseDao.findOne("users", { _id: step2.userId }, { communityCount: 1 });
					if(step3 && step3.communityCount > 0) {
						await baseDao.updateOne("users", { _id: step2.userId }, { $inc: { communityCount: -1 } }, {});
					}
				}
				// await commonControllerV1.saveAdminWellLogs(tokenData.userId, [step1.userId], step1, params.deleteReason, ADMIN_NOTIFICATION_TYPE.DELETE_COMMUNITY_ADMIN.TYPE);
				await notificationManager.PublishAdminNotifications([step1.userId], ADMIN_NOTIFICATION_TYPE.DELETE_COMMUNITY_ADMIN.TYPE, step1._id, params.deleteReason);
                return MESSAGES.SUCCESS.DELETE_FLAGGED_COMMUNITY;
            }
		} catch (error) {
			throw error;
		}
	}
}
export const communitiesController = new CommunitiesController();