declare namespace UserRequest {

	export interface SignUp extends Device {
		email?: string;
		userType: string;
		socialId?: string;
		loginType?: string;
		name?: string;
		profilePicture?: string;
		socialData?: Object;
		deviceId: string;
		deviceToken: string;
		firstName?: string;
		isEmailVerified?: boolean;
	}

	export interface SetPassword extends Device {
		password: string;
		hash?: string;
	}

	export interface Registration {
		firstName: string;
		lastName: string;
		country?:string;
		state?:string;
		city?: string;
		countryCode?: string;
		phoneNumber?: string;
		fullPhoneNumber?: string;
		countryFlagCode:string;
		deviceId?:string;

	}
	export interface Favourites {
		userId?: string;
		wishId: string;
		type: string;
	}

	export interface FavouritesList extends ListingRequest  {
		wishId?:string;
		communityId?:string;
	}

	export interface ContactSync {
		userId?: string;
		countryCode?: string;
		phoneNumber: string;
		contacts: Array<Object>;
		contactUserId?: string;
		deviceId?:string;
	}

	export interface ContactSyncV2 {
		userId: string;
		contacts: Array<{
			name?: string;
			phoneNumbers: Array<{
				phoneNumber: string;
				// countryCode?: string;
				// default?: boolean;
				type: string;
			}>;
		}>;
		deviceId?: string;
	}

	export interface DeleteSyncContact {
		contacts: Array<Object>;
		deviceId?:string;
	}

	interface _contact {
		name: string
		phoneNumber: string;
	}

	export interface DeleteSyncContactV2 {
		contacts: Array<_contact>;
		deviceId?:string;
	}

	export interface SendOtp {
		type?: string;
		email: string;
		mobileNo?: string;
	}

	export interface VerifyOTP extends Device {
		type?: string;
		email: string;
		otp: string;
		mobileNo?: string;
	}

	export interface Login extends Device {
		email: string;
		password: string;
		userType?: string;
	}

	export interface ForgotPassword {
		email: string;
	}

	export interface ChangeForgotPassword {
		newPassword: string;
		confirmPassword: string;
		hash?: string;
		salt?: string;
		email: string;
		resetToken?: string;
	}

	export interface VerifyUser {
		isApproved: string;
		userId: string;
		reason?: string;
		declinedReason?: string;
	}

	export interface SkipSteps {
		type: string;
	}

	export interface supportChat {
		message: string;
		type: number;
		userId?: string;
	}
	export interface AboutMe {
		userId?: string;
		name?: string;
		firstName: string;
		lastName: string;
		dob: number;
		gender: string;
		language: string;
		interpreterRequired: boolean;
		identifyAsAboriginal: boolean;
		location: GeoLocation;
		residentialAddress: GeoLocation;
		postalAddress: GeoLocation;
		aboutMe: string;
	}

	export interface MyneedAndSKill {
		tags: string[];
		needAndExperience: string;
	}


	export interface EditProfilePic {
		profilePicture: string;
	}


	export interface Setting {
		pushNotificationStatus: boolean;
		groupaNotificationStatus: boolean;
		isProfileHidden: boolean;
	}
	export interface EditInterests {
		interests: Array<Interests>;
	}
	export interface UploadDocument {
		type: string;
		documentUrl: string;
		documentUploadToken?: string;
	}

	export interface Checkilist {
		type: string;
	}

	export interface AddSignature {
		type: string;
		firstName: string;
		lastName: string;
	}
	export interface Like {
		set: number;
		userId: string;
	}
	export interface UserList extends ListingRequest {
		userType?: string;
		lat?: number;
		lng?: number;
		users?: any[];
		gender?: string;
		categoryIds?: any;
		interestIds?: any;
		activityId?: string;
	}
	export interface FriendRequestList extends Pagination {
		searchKey?: string;
	}
	export interface UserChatList extends Pagination {

	}
	export interface NotificationList {
		pageNo: number,
		limit: number
	}
	export interface SupportCHatList extends Pagination {
		userId?: string,
		searchKey?: string
	}
	export interface inbox {
		pageNo: number,
		limit: number,
		type: string,
		inboxType: string
	}
	export interface inboxDelete {
		messageIds: Array<string>
	}
	export interface mailStar {
		id: string,
		addLabelIds: Array<string>,
		removeLabelIds: Array<string>
	}
	export interface TimeSHeetHistory {
		pageNo: number,
		limit: number,
		userId: string,
		type: string
	}
	export interface SupporterLog {
		pageNo: number,
		limit: number,
		searchKey: string,
		isExport: boolean,
		userType?:string
	}
	export interface SupporterLogProcessed {
		pageNo: number,
		limit: number,
	}
	export interface Settings {
		pushNotificationStatus: boolean;
		communityNotificationStatus: boolean;
		wishesNotificationStatus:boolean;
		gratitudeNotificationStatus:boolean;
		perferMedidation:boolean;
		preferPrayer:boolean;
		locationSharing:boolean;
		offerBlessing?:string;
		intension?:string;
	}
	export interface NotificationStatus {
		isRead: boolean;
		notificationId: boolean;
	}
	export interface HideProfile {
		isProfileHidden: string;
	}
	export interface OnboardSuccess {
		userId: string;
	}
	export interface ProfileImage {
		profilePicture: string;
		userId?: string;
	}
	export interface RatingList extends ListingRequest {
		userId?: string;
	}
	export interface FriendRequest {
		userId: string;
		status: string;
		requestId?: string;
		friendId?: {
			_id: string;
			name: string;
			profilePicture?: string;
			userType: string;
		};
	}
	export interface FriendResponse {
		requestId: string;
		accept: boolean;
	}
	export interface UserGraph {
		type: string;
		month?: number;
		year?: number;
		userType?: string;
	}
	export interface Home extends ListingRequest {
		type: string;
		isInclude: boolean;
		userList?: string;
		
	}

	export interface EditProfile {
		profilePicture?: string;
		firstName?: string;
		lastName?: string;
		countryCode?: string;
		phoneNumber?: string;
		countryFlagCode?:string;
		deviceId?:string;
		offset?:number;
	}

	export interface WishwellThanks {
		note?: string;
		audio?: string;
		amount?: number;
		paymentIntentid?:string;
		intentId?:string;
		
	}

	export interface ThankWishwellListing {
		userId?: string;
		type: string;
		pageNo: number;
		limit: number;
		sortBy?: number;
		sortCriteria?: string;
		searchKey?: string;
	}

	export interface matchPassword {
		password: string;
	}
}