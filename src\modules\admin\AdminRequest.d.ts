declare namespace AdminRequest {

	export interface SetNewPassword extends Device {
		password: string;
		userId: string;
		hash?: string;
	}

	export interface AdminDetails extends Device {
		firstName: string;
		lastName: string;
		contactNo?: string;
		userId: string;
	}

	export interface Create {
		name?: string;
		firstName: string,
		lastName: string,
		email: string;
		password: string;
		created?: number;
	}

	export interface Login extends Device {
		email: string;
		password: string;
		salt: string;
		hash: string;
	}

	export interface ForgotPasswordRequest {
		email: string;
	}

	export interface ChangeForgotPassword {
		password: string;
		hash?: string;
		token?: string;
	}

	export interface VerifyResetPasswordToken {
		token: string;
	}

	export interface Dashboard extends Filter {
		year?: string;
		month?: string;
		type: string;
		dashboardType?: string;
		corporateId?: string;
	}

	export interface EditProfile {
		profilePicture?: string;
		firstName: string;
		lastName: string;
		contactNo?: string;
		company?: string;
		jobTitle?: string;
	}

	export interface Dashboard {
		fromDate: number;
		toDate: number;
	}

	export interface UserListing extends ListingRequest {
		userType?: string;
		latestUsers: boolean;
	}

	export interface BlessingList {
		pageNo: number;
		limit: number;
		sortCriteria?: string;
		sortBy?: number;
		searchKey?: string;
	}

	export interface CreateBlessing {
		image: string;
		name: string;
		blessingType: string;
		language: string;
		audioFile: string;
		voiceover: string;
	}

	export interface BlessingDetail {
		id: string;
	}

	export interface DashboardExpansion extends Device {
		type: string;
		startDate?: number;
		endDate?: number;
		pageNo: number;
		limit: number;
	}

	export interface DashboardExpansionExport {
		startDate?: number;
		endDate?: number;
	}

	interface Location {
		type: "Point"; // Ensures type is always "Point"
		address: string;
		coordinates: [number, number]; // [longitude, latitude]
		city: string;
		country: string;
		state: string;
	}	  

	export interface CreateWish {
		title: string;
		description: string;
		intension: string;
		image: string;
		isGlobalWish: boolean;
		disasterCategory?: number;
		familyName?: string;
		location?: Location;
		status?: string;
		userId?: string;
		createdBy?: string;
	}

	export interface WishList {
		searchKey?: string;
		pageNo: number;
		limit: number;
		sortBy?: number;
		sortCriteria?: string;
	}

	export interface WishDetail {
		id: string;
		deleteReason?: string;
	}

	export interface WishEdit extends WishDetail {
		title: string;
		description: string;
		intension: string;
		image: string;
		isGlobalWish: boolean;
	}

	export interface FlaggedWish {
		wishId?: string;
		pageNo: number;
		limit: number;
		sortBy?: number;
		sortCriteria?: string;
		searchKey?: string;
	}
	
	export interface UserList {
		pageNo: number;
		limit: number;
		sortBy?: number;
		sortCriteria?: string;
		searchKey?: string;
	}

	export interface UserDetail {
		id: string;
	}

	export interface EditUser extends UserDetail {
		status: string;
		reason?: string;
	}

	export interface InvestorList {
		pageNo: number;
		limit: number;
		sortBy?: number;
		sortCriteria?: string;
		searchKey?: string;
	}

	export interface InvestorDetail {
		id: string;
	}

	export interface CreateInvestor {
		firstName: string;
		lastName: string;
		email: string;
		contactNo?: string;
		company?: string;
		jobTitle?: string;
		profilePicture?: string;
	}

	export interface EditInvestor extends InvestorDetail {
		firstName: string;
		lastName: string;
		email: string;
		contactNo?: string;
		company?: string;
		jobTitle?: string;
		profilePicture?: string;
	}

	export interface DeleteInvestor {
		id: string;
		status: string;
	}

}