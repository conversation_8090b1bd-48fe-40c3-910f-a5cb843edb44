"use strict";
import { BaseDao, baseDao } from "@modules/baseDao/BaseDao";
import { STATUS, USER_TYPE, WISH_TAG_TYPE } from "@config/constant";
import { escapeSpecial<PERSON>haracter } from "@utils/appUtils";
import { toObjectId } from "@utils/appUtils";
export class WishesDao extends BaseDao {
	/**
	 * @function findGlobalWishById
	 */
	async findGlobalWishById(wishId: string, project = {}) {
		try {
			const query: any = {};
			query._id = toObjectId(wishId);
			query.isGlobalWish = true;
			const projection = (Object.values(project).length) ? project : { createdAt: 0, updatedAt: 0 };
			return await this.findOne("wishes", query, projection);
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function addWishes
	 */
	async addWishes(params: WishesRequest.Add) {
		try {
			return await this.save("wishes", params);
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function getContacts
	 */
	async getContacts(userId, isAppUser?: boolean) {
		try {
			const query: any = {};
			query['userId'] = userId
			query['status'] = { '$nin': [STATUS.DELETED, STATUS.IN_ACTIVE, STATUS.UN_PUBLISHED, STATUS.BLOCKED] }
			if (isAppUser) query['isAppUser'] = isAppUser
			const projection = { _id: 1, name: 1, profilePicture: 1, phoneNumber: 1, countryCode: 1, contactUserId: 1, isAppUser: 1 };
			return await this.find("contacts", query, projection);
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function addTagWishes
	 */
	async addTagWishes(params) {
		try {
			return await this.insertMany("tagwishes", params, {});
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function getCohosts
	 */
	async getCohosts(cohosts) {
		try {
			const query: any = {};
			query['_id'] = { '$in': cohosts }
			query['status'] = { '$nin': [STATUS.DELETED, STATUS.IN_ACTIVE, STATUS.UN_PUBLISHED] }
			const projection = { _id: 1, name: 1, firstName: 1, lastName: 1, profilePicture: 1, phoneNumber: 1, countryCode: 1 };
			return await this.find("users", query, projection);
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function getWishes
	 */
	async getWishes(params: WishesRequest.WishList, reportList) {
		try {
			const aggPipe = [];
			const match: any = {};
			match['_id'] = { '$nin': reportList }
			if (params.communityId) {
				let community = params.communityId.split(',')
				params.communityId = community
				for (let i = 0; params.communityId.length > i; i++) {
					params.communityId[i] = toObjectId(params.communityId[i])
				}
				match['communityId'] = { '$in': params.communityId }
			}
			aggPipe.push({
				"$lookup": {
					"from": "users",
					"let": { "hostId": "$hostId" },

					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$_id", "$$hostId"]
										}
									]
								}
							},
						},
						{
							"$project": {
								_id: 0, "profilePicture": 1, "firstName": 1, "lastName": 1
							}
						}
					],
					"as": "hostDetail"
				}
			})
			aggPipe.push({
				'$unwind': {
					"path": "$hostDetail",
					"preserveNullAndEmptyArrays": true
				}
			});
			aggPipe.push({
				"$lookup": {
					"from": "pinned_wishes",
					"let": { "subjectId": "$_id" },
					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$subjectId", "$$subjectId"]
										},
										{
											"$eq": ["$userId", toObjectId(params.userId)]
										},
										{
											"$eq": ["$status", STATUS.ACTIVE]
										}
									]
								}

							},
						},
						{ "$project": { _id: 1, subjectId: 1 } }
					],
					"as": "pinnedWishes"
				}
			});
			aggPipe.push({
				'$unwind': {
					"path": "$pinnedWishes",
					"preserveNullAndEmptyArrays": true
				}
			});
			aggPipe.push({
				"$lookup": {
					"from": "tagwishes",
					"let": { "wishId": "$_id" },
					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$wishId", "$$wishId"]
										},
										{
											"$eq": ["$status", STATUS.ACTIVE]
										},
										{
											"$eq": ["$type", WISH_TAG_TYPE.COHOST]
										}
									]
								}

							},
						},
						{ "$project": { _id: 1, userId: 1 } }
					],
					"as": "cohosts"
				}
			});

			// to check if wish has only cohost
			aggPipe.push({
				"$lookup": {
					"from": "tagwishes",
					"let": { "wishId": "$_id" },
					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$wishId", "$$wishId"]
										},
										{
											"$in": ["$status", [STATUS.ACTIVE, STATUS.PENDING]]
										},
										{
											"$in": ["$type", [WISH_TAG_TYPE.CONTACTS, WISH_TAG_TYPE.INVITED]]
										}
									]
								}

							},
						},
						{ "$project": { _id: 1, userId: 1 } }
					],
					"as": "contactAndInvited"
				}
			});
			if (params.searchKey) {
				params.searchKey = escapeSpecialCharacter(params.searchKey);
				// match["$or"] = [
				// 	{ description: { "$regex": params.searchKey, "$options": "i" } },
				// ];
				match["$and"] = [
					{
						"$or": [
							{ userId: toObjectId(params.userId), status: { '$nin': [STATUS.DELETED, STATUS.IN_ACTIVE, STATUS.UN_PUBLISHED, STATUS.CLOSED, STATUS.REJECTED] } },
							{ hostId: toObjectId(params.userId), status: { '$nin': [STATUS.DELETED, STATUS.IN_ACTIVE, STATUS.UN_PUBLISHED, STATUS.CLOSED, STATUS.REJECTED, STATUS.PENDING] } },
						]
					},
					{
						"$or": [
							{ description: { "$regex": params.searchKey, "$options": "i" } },
						]
					},
				];
			} else {
				match["$or"] = [
					{ userId: toObjectId(params.userId), status: { '$nin': [STATUS.DELETED, STATUS.IN_ACTIVE, STATUS.UN_PUBLISHED, STATUS.CLOSED, STATUS.REJECTED, STATUS.BLOCKED] } },
					{ hostId: toObjectId(params.userId), status: { '$nin': [STATUS.DELETED, STATUS.IN_ACTIVE, STATUS.UN_PUBLISHED, STATUS.CLOSED, STATUS.REJECTED, STATUS.PENDING, STATUS.BLOCKED] } },
				];
			}
			aggPipe.push({ "$match": match });
			aggPipe.push({
				"$project": {
					_id: 1, userId: 1, type: 1, created: 1, wishId: '$_id',
					"wishDetail.cohosts": '$cohosts',
					'wishDetail.status': '$status',
					"wishDetail._id": '$_id',
					"wishDetail.description": '$description',
					"wishDetail.image": '$image',
					"wishDetail.created": '$created',
					"wishDetail.isGlobalWish": '$isGlobalWish',
					"wishDetail.hostDetail": '$hostDetail',
					"wishDetail.userDetail": '$userDetail',
					"wishDetail.hostId": '$hostId',
					"wishDetail.userId": '$userId',
					'pinnedWishes': 1,
					"wishDetail.totalBlessings": "$totalBlessings",
					"wishDetail.userLastBlessTime": "$userLastBlessTime",
					"wishDetail.hostLastBlessTime": "$hostLastBlessTime",
					communityId: 1,
					"wishDetail.title": "$title",
					showRallySupport: {
						$cond: {
							if: {
								$or: [
									{ $gt: [{ $size: { $ifNull: ["$contactAndInvited", []] } }, 0] },
									{ $gt: [{ $size: { $ifNull: ["$communityId", []] } }, 0] }
								]
							},
							then: false,
							else: true
						}
					},
					pinned: {
						$cond: [
							{ $ifNull: ["$pinnedWishes", false] },
							1,
							0
						]
					}
				}
			});
			// aggPipe.push({ "$sort": { pinned: -1,created: -1} });
			aggPipe.push({ "$sort": { created: -1 } });
			const options = {};
			// let counData = await this.aggregate("wishes", aggPipe);
			// let total = counData.length;
			let response = await this.paginate("wishes", aggPipe, params.limit, params.pageNo, options, true);
			// response.total = total;
			return response;
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function getReceivedWishes
	 */
	async getReceivedWishes(params: WishesRequest.WishList, reportList?) {
		try {
			const aggPipe = [];
			const match: any = {};
			match['userId'] = toObjectId(params.userId);
			match['status'] = { '$nin': [STATUS.DELETED, STATUS.IN_ACTIVE, STATUS.UN_PUBLISHED, STATUS.CLOSED, STATUS.REJECTED, STATUS.BLOCKED, STATUS.PENDING] };
			match['type'] = { '$nin': [WISH_TAG_TYPE.HOST, WISH_TAG_TYPE.INVITED_HOST] };
			match['isDuplicate'] = false;
			if (reportList && reportList.length) match['wishId'] = { '$nin': reportList };
			if (params.communityId) {
				let community = params.communityId.split(',');
				params.communityId = community;
				for (let i = 0; params.communityId.length > i; i++) {
					params.communityId[i] = toObjectId(params.communityId[i]);
				}
				match['communityId'] = { '$in': params.communityId };
			}
			aggPipe.push({
				"$lookup": {
					"from": "wishes",
					"let": { "wishId": "$wishId" },
					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$_id", "$$wishId"]
										},
										{
											'$eq': ["$status", STATUS.ACTIVE]
										}
									]
								}

							},
						},
						{
							"$project": {
								_id: 1, title: 1, description: 1, userId: 1, hostId: 1, userDetail: 1, created: 1, image: 1, isGlobalWish: 1, totalBlessings: 1, status: 1
							}
						}
					],
					"as": "wishDetail"
				}
			});
			aggPipe.push({
				'$unwind': {
					"path": "$wishDetail",
					"preserveNullAndEmptyArrays": false
				}
			});
			aggPipe.push({
				"$lookup": {
					"from": "users",
					"let": { "hostId": "$wishDetail.hostId" },

					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$_id", "$$hostId"]
										}
									]
								}
							},
						},
						{
							"$project": {
								_id: 0, "profilePicture": 1, "firstName": 1, "lastName": 1
							}
						}
					],
					"as": "wishDetail.hostDetail"
				}
			});
			aggPipe.push({
				'$unwind': {
					"path": "$wishDetail.hostDetail",
					"preserveNullAndEmptyArrays": true
				}
			});
			aggPipe.push({
				"$lookup": {
					"from": "pinned_wishes",
					"let": { "subjectId": "$wishId" },
					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$subjectId", "$$subjectId"]
										},
										{
											"$eq": ["$userId", toObjectId(params.userId)]
										},
										{
											"$eq": ["$status", STATUS.ACTIVE]
										}
									]
								}

							},
						},
						{ "$project": { _id: 1, subjectId: 1 } }
					],
					"as": "pinnedWishes"
				}
			});
			aggPipe.push({
				'$unwind': {
					"path": "$pinnedWishes",
					"preserveNullAndEmptyArrays": true
				}
			});
			aggPipe.push({
				"$lookup": {
					"from": "tagwishes",
					"let": { "wishId": "$wishId" },
					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$wishId", "$$wishId"]
										},
										{
											"$eq": ["$status", STATUS.ACTIVE]
										},
										{
											"$eq": ["$type", WISH_TAG_TYPE.COHOST]
										}
									]
								}

							},
						},
						{ "$project": { _id: 1, userId: 1 } }
					],
					"as": "wishDetail.cohosts"
				}
			});

			if (params.searchKey) {
				params.searchKey = escapeSpecialCharacter(params.searchKey);
				match["$or"] = [
					{ "wishDetail.description": { "$regex": params.searchKey, "$options": "i" } },
					{ "wishDetail.userDetail.firstName": { "$regex": params.searchKey, "$options": "i" } },
					{ "wishDetail.hostDetail.firstName": { "$regex": params.searchKey, "$options": "i" } }
				];
			}
			aggPipe.push({ "$match": match });
			aggPipe.push({
				"$project": {
					_id: 1, wishId: 1, userId: 1, created: 1, createdBy: 1, wishDetail: 1, type: 1, pinnedWishes: 1, communityId: 1, lastBlessTime: 1, createdAt: 1,
					pinned: {
						$cond: [
							{ $ifNull: ["$pinnedWishes", false] },
							1,
							0
						]
					}
				}
			});
			// aggPipe.push({ "$sort": { pinned: -1,createdAt: -1} });
			aggPipe.push({ "$sort": { createdAt: -1 } });
			const options = {};
			// let counData = await this.aggregate("tagwishes", aggPipe);
			// let total = counData.length;
			let response = await this.paginate("tagwishes", aggPipe, params.limit, params.pageNo, options, true);
			// response.total = total;
			return response;
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function getGlobalWishes
	 */
	async getGlobalWishes(params, tokenData) {
		try {
			const aggPipe = [];
			const match: any = {};
			match['isGlobalWish'] = { '$eq': true }
			if (params.disasterCategory) {
				match['disasterCategory'] = { '$eq': parseInt(params.disasterCategory, 10) }
			}
			match['status'] = { '$nin': [STATUS.DELETED, STATUS.IN_ACTIVE, STATUS.UN_PUBLISHED, STATUS.REJECTED, STATUS.CLOSED] }
			match['userRemovedWish'] = { '$ne': toObjectId(tokenData.userId) }
			if (params.searchKey) {
				params.searchKey = escapeSpecialCharacter(params.searchKey);
				match["$or"] = [
					{ description: { "$regex": params.searchKey, "$options": "i" } },
				];
			}
			aggPipe.push({ "$match": match });
			aggPipe.push({
				"$lookup": {
					"from": "pinned_wishes",
					"let": { "subjectId": "$_id" },
					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$subjectId", "$$subjectId"]
										},
										{
											"$eq": ["$userId", toObjectId(tokenData.userId)]
										},
										{
											"$eq": ["$status", STATUS.ACTIVE]
										}
									]
								}

							},
						},
						{ "$project": { _id: 1, subjectId: 1 } }
					],
					"as": "pinnedWishes"
				}
			});
			aggPipe.push({
				'$unwind': {
					"path": "$pinnedWishes",
					"preserveNullAndEmptyArrays": true
				}
			});
			aggPipe.push({
				"$lookup": {
					"from": "perform_blessings",
					"let": { "wishId": "$_id" },
					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$wishId", "$$wishId"]
										},
										{
											"$eq": ["$userId", toObjectId(tokenData.userId)]
										},
										{
											"$eq": ["$status", STATUS.ACTIVE]
										}
									]
								}

							},
						},
						{ "$project": { _id: 1, createdAt: 1 } }
					],
					"as": "blessingData"
				}
			});
			// aggPipe.push({
			// 	'$unwind': {
			// 		"path": "$blessingData",
			// 		"preserveNullAndEmptyArrays": true
			// 	}
			// });
			aggPipe.push({
				"$lookup": {
					"from": "disaster_categories",
					"localField": "disasterCategory",
					"foreignField": "disasterCategory",
					"as": "disasterCategoryDetails"
				}
			});
			aggPipe.push({
				"$unwind": {
					"path": "$disasterCategoryDetails",
					"preserveNullAndEmptyArrays": true
				}
			});
			aggPipe.push({
				"$project": {
					_id: 1,
					"wishDetail._id": '$_id',
					"wishDetail.title": '$title',
					"wishDetail.description": '$description',
					"wishDetail.image": '$image',
					"wishDetail.created": '$created',
					'wishDetail.status': '$status',
					"wishDetail.isGlobalWish": '$isGlobalWish',
					"wishDetail.totalBlessings": '$totalBlessings',
					"pinnedWishes": 1,
					lastBlessTime: { $arrayElemAt: ["$blessingData.createdAt", -1] },
					"wishDetail.disasterCategory": '$disasterCategoryDetails',
					"wishDetail.familyName": '$familyName',
					pinned: {
						$cond: [
							{ $ifNull: ["$pinnedWishes", false] },
							1,
							0
						]
					}
				}
			});
			// aggPipe.push({ "$sort": { pinned: -1,created: -1} });
			aggPipe.push({ "$sort": { created: -1 } });
			const options = {};
			let counData = await baseDao.countDocuments("wishes", match)
			let response = await this.paginate("wishes", aggPipe, params.limit, params.pageNo, options, true);
			response.total = counData;
			return response;
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function wishDetail
	 */
	async wishDetail(params, userId, wish) {
		try {
			const aggPipe = [];
			const match: any = {};
			match['_id'] = toObjectId(params.wishId)
			aggPipe.push({
				"$lookup": {
					"from": "users",
					"let": { "hostId": "$hostId" },

					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$_id", "$$hostId"]
										}
									]
								}
							},
						},
						{
							"$project": {
								_id: 0, "profilePicture": 1, "firstName": 1, "lastName": 1
							}
						}
					],
					"as": "hostDetail"
				}
			});
			aggPipe.push({
				'$unwind': {
					"path": "$hostDetail",
					"preserveNullAndEmptyArrays": true
				}
			});
			aggPipe.push({
				"$lookup": {
					"from": "pinned_wishes",
					"let": { "subjectId": "$_id" },
					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$subjectId", "$$subjectId"]
										},
										{
											"$eq": ["$userId", toObjectId(userId)]
										},
										{
											"$eq": ["$status", STATUS.ACTIVE]
										}
									]
								}

							},
						},
						{ "$project": { _id: 1 } }
					],
					"as": "pinnedWishes"
				}
			});
			aggPipe.push({
				'$unwind': {
					"path": "$pinnedWishes",
					"preserveNullAndEmptyArrays": true
				}
			});
			aggPipe.push({
				"$lookup": {
					"from": "favourites",
					"let": { "wishId": "$_id" },
					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$wishId", "$$wishId"]
										},
										{
											"$eq": ["$userId", toObjectId(userId)]
										},
										{
											"$eq": ["$status", STATUS.ACTIVE]
										}
									]
								}

							},
						},
						{ "$project": { _id: 1 } }
					],
					"as": "favourites"
				}
			});
			aggPipe.push({
				'$unwind': {
					"path": "$favourites",
					"preserveNullAndEmptyArrays": true
				}
			});
			aggPipe.push({
				"$lookup": {
					"from": "tagwishes",
					"let": { "wishId": "$_id" },
					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$wishId", "$$wishId"]
										},
										{
											"$eq": ["$status", STATUS.ACTIVE]
										},
										{
											"$eq": ["$type", WISH_TAG_TYPE.COHOST]
										}
									]
								}

							},
						},
						{ "$project": { _id: 1, userId: 1 } }
					],
					"as": "cohosts"
				}
			});
			aggPipe.push({
				"$lookup": {
					"from": "tagwishes",
					"let": { "wishId": "$_id" },

					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$wishId", "$$wishId"]
										},
										{
											"$eq": ["$userId", toObjectId(userId)]
										},
										{
											'$in': ["$status", [STATUS.ACTIVE]]
										}
									]
								}

							},
						},
						{
							"$project": {
								_id: 1, status: 1, communityId: 1, userId: 1, lastBlessTime: 1
							}
						}
					],
					"as": "taggedWishDetail"
				}
			});
			aggPipe.push({
				'$unwind': {
					"path": "$taggedWishDetail",
					"preserveNullAndEmptyArrays": true
				}
			});
			aggPipe.push({
				"$lookup": {
					"from": "disaster_categories",
					"localField": "disasterCategory",
					"foreignField": "disasterCategory",
					"as": "disasterCategoryDetails"
				}
			});
			aggPipe.push({
				"$unwind": {
					"path": "$disasterCategoryDetails",
					"preserveNullAndEmptyArrays": true
				}
			});
			if (!wish.isGlobalWish) {
				aggPipe.push({
					"$lookup": {
						"from": "communities",
						"let": { "communityId": "$communityId" },
						"pipeline": [
							{
								"$match": {

									"$expr":
									{
										$and: [
											{
												"$in": ["$_id", "$$communityId"]
											},
											{
												'$in': ["$status", [STATUS.ACTIVE, STATUS.PENDING]]
											},
										]
									}

								},
							},
							{
								"$project": {
									'communityDetail._id': '$_id', 'communityDetail.name': '$name'
								}
							}
						],
						"as": "communityData"
					}
				});
			}
			aggPipe.push({ "$match": match });
			aggPipe.push({ "$sort": { created: -1 } });
			aggPipe.push({
				"$project": {
					_id: 1, title: 1, description: 1, intension: 1, userId: 1, hostId: 1, type: 1, userDetail: 1, hostDetail: 1, created: 1, createdBy: 1, isGlobalWish: 1, taggedUsers: 1, favourites: 1, pinnedWishes: 1,
					image: 1, cohosts: 1, communityId: 1, communityData: 1, totalBlessings: 1, hostLastBlessTime: 1, userLastBlessTime: 1, taggedWishDetail: 1, location: 1, status: 1,
					familyName: 1, disasterCategory: '$disasterCategoryDetails',
				}
			});
			const options = {};
			let response = await this.aggregate("wishes", aggPipe, options);
			return response;
		} catch (error) {
			throw error;
		}
	}


	/**
	 * @function findTaggedUser
	 */
	async findTaggedUser(params) {
		try {
			const aggPipe = [];
			const match: any = {};
			match['wishId'] = toObjectId(params.wishId)
			match['type'] = { '$in': [WISH_TAG_TYPE.CONTACTS, WISH_TAG_TYPE.COHOST, WISH_TAG_TYPE.INVITED] }
			match['status'] = { '$nin': [STATUS.DELETED, STATUS.IN_ACTIVE, STATUS.UN_PUBLISHED] }
			match['communityId'] = { $exists: false }
			aggPipe.push({
				"$lookup": {
					"from": "users",
					"let": { "userId": "$userId" },
					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$_id", "$$userId"]
										}
									]
								}

							},
						},
						{ "$project": { _id: 1, firstName: 1, lastName: 1, } }
					],
					"as": "userDetail"
				}
			});
			aggPipe.push({
				'$unwind': {
					"path": "$userDetail",
					"preserveNullAndEmptyArrays": true
				}
			})
			aggPipe.push({
				"$lookup": {
					"from": "wishes",
					"let": { "wishId": '$wishId', },
					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$_id", "$$wishId"]
										}
									]
								}

							},
						},
						{ "$project": { _id: 1, userId: 1 } }
					],
					"as": "wishes"
				}
			});
			aggPipe.push({
				'$unwind': {
					"path": "$wishes",
					"preserveNullAndEmptyArrays": true
				}
			})
			aggPipe.push({
				"$lookup": {
					"from": "contacts_v2",
					"let": { "contactId": "$contactId", "userId": '$wishes.userId' },
					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$_id", "$$contactId"]
										},
										// {
										// 	"$eq": ["$userId", "$$userId"]
										// }

									]
								}

							},
						},
						{ "$project": { _id: 1, name: 1 } }
					],
					"as": "contactDetail"
				}
			});
			aggPipe.push({
				'$unwind': {
					"path": "$contactDetail",
					"preserveNullAndEmptyArrays": true
				}
			})

			aggPipe.push({ "$match": match });
			aggPipe.push({ "$sort": { created: -1 } });
			aggPipe.push({
				"$project": {
					_id: 1, userId: 1, userDetail: 1, status: 1, communityId: 1, type: 1, contactDetail: 1

				}
			});
			//aggPipe.push({ "$limit": 1 })
			const options = {};
			let response = await this.aggregate("tagwishes", aggPipe, options);
			return response;
		} catch (error) {
			throw error;
		}
	}

	/**    
	 * @function findWishById
	 */
	async findWishById(wishId: string, project = {}) {
		try {
			const query: any = {};
			query._id = toObjectId(wishId);
			query.status = { '$nin': [STATUS.DELETED, STATUS.IN_ACTIVE, STATUS.UN_PUBLISHED, STATUS.REJECTED] };
			const projection = (Object.values(project).length) ? project : { createdAt: 0, updatedAt: 0 };
			return await this.findOne("wishes", query, projection);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function getPinnedWishes
	 */
	async getPinnedWishes(userId, reportList?) {
		try {
			const aggPipe = [];
			const match: any = {};
			match['userId'] = toObjectId(userId)
			match.status = { '$nin': [STATUS.DELETED, STATUS.IN_ACTIVE, STATUS.CLOSED, STATUS.REPORTED] };
			if (reportList && reportList.length) match['subjectId'] = { '$nin': reportList }
			aggPipe.push({
				"$lookup": {
					"from": "wishes",
					"let": { "wishId": "$subjectId" },

					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$_id", "$$wishId"]
										},
										{
											'$in': ["$status", [STATUS.ACTIVE, STATUS.PENDING]]
										}
									]
								}

							},
						},
						{
							"$project": {
								_id: 1, image: 1, description: 1, totalBlessings: 1, intension: 1, title: 1
							}
						}
					],
					"as": "wishDetail"
				}
			});
			aggPipe.push({
				'$unwind': {
					"path": "$wishDetail",
					// "preserveNullAndEmptyArrays": true
					"preserveNullAndEmptyArrays": false
				}
			});
			aggPipe.push({ "$match": match });
			aggPipe.push({ "$sort": { created: -1 } });
			aggPipe.push({
				"$project": {
					_id: 1, subjectId: 1, wishDetail: 1
				}
			});
			const options = {};
			let response = await this.aggregate("pinned_wishes", aggPipe, options);
			return response;
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function addPinnedWishes
	 */
	async addPinnedWishes(params) {
		try {
			return await this.save("pinned_wishes", params);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function removePinnedWishes
	 */
	async removePinnedWishes(params) {
		try {
			const query: any = {};
			query['userId'] = toObjectId(params.userId);
			query['subjectId'] = toObjectId(params.subjectId);
			query['status'] = { '$nin': [STATUS.DELETED, STATUS.IN_ACTIVE, STATUS.UN_PUBLISHED] }
			// return await this.findOneAndUpdate("pinned_wishes", query,{status:STATUS.DELETED}, {returnDocument: 'after'}); 
			return await this.deleteMany("pinned_wishes", query); // New Query (removed completely from DB)
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function editWishDetail
	 */
	async editWishDetail(params) {
		try {
			const aggPipe = [];
			const match: any = {};
			match['_id'] = toObjectId(params.wishId);
			aggPipe.push({
				"$lookup": {
					"from": "users",
					"let": { "hostId": "$hostId" },

					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$_id", "$$hostId"]
										}
									]
								}
							},
						},
						{
							"$project": {
								_id: 0, "profilePicture": 1, "firstName": 1, "lastName": 1
							}
						}
					],
					"as": "hostDetail"
				}
			});
			aggPipe.push({
				'$unwind': {
					"path": "$hostDetail",
					"preserveNullAndEmptyArrays": true
				}
			});
			aggPipe.push({
				"$lookup": {
					"from": "communities",
					"let": { "communityId": "$communityId" },
					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$in": ["$_id", "$$communityId"]
										},
										{
											'$in': ["$status", [STATUS.ACTIVE, STATUS.PENDING]]
										},
									]
								}

							},
						},
						{
							"$project": {
								'communityDetail._id': '$_id', 'communityDetail.name': '$name'
							}
						}
					],
					"as": "communityData"
				}
			});
			aggPipe.push({ "$match": match });
			aggPipe.push({ "$sort": { created: -1 } });
			aggPipe.push({
				"$project": {
					_id: 1, description: 1, intension: 1, created: 1, image: 1, communityId: 1, communityData: 1, totalBlessings: 1, wishType: 1, status: 1, hostId: 1, hostDetail: 1, userId: 1, userDetail: 1, title: 1

				}
			});
			const options = {};
			let response = await this.aggregate("wishes", aggPipe, options);
			return response;
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function findTaggedUserDetail
	 */
	async findTaggedUserDetail(params, userId) {
		try {
			const aggPipe = [];
			const match: any = {};
			match['wishId'] = toObjectId(params.wishId)
			match['type'] = { '$in': [WISH_TAG_TYPE.CONTACTS, WISH_TAG_TYPE.COHOST, WISH_TAG_TYPE.INVITED] }
			match['status'] = { '$nin': [STATUS.DELETED, STATUS.IN_ACTIVE, STATUS.UN_PUBLISHED] }
			match['communityId'] = { $exists: false }  // Community members shouldn't be shown in tagged-users
			aggPipe.push({
				"$lookup": {
					"from": "users",
					"let": { "userId": "$userId" },
					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$_id", "$$userId"]
										}
									]
								}

							},
						},
						{ "$project": { _id: 1, firstName: 1, lastName: 1, } }
					],
					"as": "userDetail"
				}
			});
			aggPipe.push({
				'$unwind': {
					"path": "$userDetail",
					"preserveNullAndEmptyArrays": true
				}
			})
			aggPipe.push({
				"$lookup": {
					"from": "wishes",
					"let": { "wishId": '$wishId', },
					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$_id", "$$wishId"]
										}
									]
								}

							},
						},
						{ "$project": { _id: 1, userId: 1 } }
					],
					"as": "wishes"
				}
			});
			aggPipe.push({
				'$unwind': {
					"path": "$wishes",
					"preserveNullAndEmptyArrays": true
				}
			})
			aggPipe.push({
				"$lookup": {
					"from": "contacts_v2",
					//"let": { "phoneNumber": "$phoneNumber", "userId": '$wishes.userId', },
					"let": { "contactId": "$contactId", "userId": '$wishes.userId', },
					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$_id", "$$contactId"]
										},
										// {
										// 	"$eq": ["$userId", "$$userId"]
										// }
									]
								}

							},
						},
						{ "$project": { _id: 1, name: 1 } }
					],
					"as": "contactDetail"
				}
			});
			aggPipe.push({
				'$unwind': {
					"path": "$contactDetail",
					"preserveNullAndEmptyArrays": true
				}
			})
			aggPipe.push({ "$match": match });
			aggPipe.push({ "$sort": { created: -1 } });
			aggPipe.push({
				"$project": {
					_id: 1, userId: 1, userDetail: 1, status: 1, name: 1, contactDetail: 1, type: 1, countryCode: 1, phoneNumber: 1

				}
			});
			//aggPipe.push({ "$limit": 1 })
			const options = {};
			let response = await this.aggregate("tagwishes", aggPipe, options);
			return response;
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function findTaggedWish
	 */
	async findTaggedWish(params) {
		try {
			return await baseDao.countDocuments("tagwishes", { wishId: params.wishId, type: { $in: [WISH_TAG_TYPE.CONTACTS, WISH_TAG_TYPE.INVITED] } });
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function blessingDetail
	 */
	async blessingDetail(params) {
		try {
			const aggPipe = [];
			const match: any = {};
			match['wishId'] = toObjectId(params.wishId)
			match['status'] = STATUS.ACTIVE

			aggPipe.push({
				"$lookup": {
					"from": "users",
					"let": { "userId": "$userId" },
					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$_id", "$$userId"]
										},
										{
											'$in': ["$status", [STATUS.UN_BLOCKED]]
										},
									]
								}

							},
						},
						{
							"$project": {
								firstName: 1, lastName: 1,
							}
						}
					],
					"as": "userDetail"
				}
			});
			aggPipe.push({
				'$unwind': {
					"path": "$userDetail",
					"preserveNullAndEmptyArrays": false
				}
			})
			aggPipe.push({ "$match": match });
			aggPipe.push({ "$sort": { created: -1 } });
			aggPipe.push({
				"$project": {
					_id: 1, audio: 1, type: 1, notes: 1, userDetail: 1

				}
			});
			const options = {};
			let response = await this.aggregate("perform_blessings", aggPipe, options);
			return response;
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function wishCohosts
	 */
	async wishCohosts(wishId) {
		try {
			const query: any = {};
			query['_id'] = toObjectId(wishId)
			query['status'] = { '$nin': [STATUS.DELETED, STATUS.IN_ACTIVE, STATUS.UN_PUBLISHED] }
			query['type'] = WISH_TAG_TYPE.COHOST
			const projection = { _id: 1 };
			return await this.find("tagwishes", query, projection);
		} catch (error) {
			throw error;
		}
	}
	// /**
	//  * @function wishRequestList
	//  */
	// async wishRequestList(params: WishesRequest.WishList) {
	// 	try {
	// 		const aggPipe = [];
	// 		const match: any = {};
	// 		match['hostId'] = toObjectId(params.userId)
	// 		match['status'] = { '$eq': params.status}
	// 		aggPipe.push({
	// 			"$lookup": {
	// 				"from": "users",
	// 				"let": { "hostId": "$hostId" },

	// 				"pipeline": [
	// 					{
	// 						"$match": {

	// 							"$expr":
	// 							{
	// 								$and: [
	// 									{
	// 										"$eq": ["$_id", "$$hostId"]
	// 									}
	// 								]
	// 							}
	// 					 },
	// 					},
	// 					{ "$project": {_id:0,  "profilePicture": 1,"firstName": 1,"lastName": 1
	// 					} }
	// 				],
	// 				"as": "hostDetail"
	// 			}
	// 		})
	// 		aggPipe.push({
	// 			'$unwind': {
	// 				"path": "$hostDetail",
	// 				"preserveNullAndEmptyArrays": true
	// 			}
	// 		});
	// 		aggPipe.push({ "$match": match });
	// 		aggPipe.push({
	// 			"$project": {
	// 				_id:1,userId:1,hostId:1,type:1,created:1,status:1,image:1,userDetail:1,hostDetail:1,description:1
	// 			}
	// 		});
	// 		aggPipe.push({ "$sort": {created: -1} });
	// 		const options = {};
	// 		let counData = await this.aggregate("wishes", aggPipe);
	//         let total = counData.length;
	// 		let response = await this.paginate("wishes", aggPipe, params.limit, params.pageNo, options, true);
	// 		response.total = total;
	// 		return response;
	// 	} catch (error) {
	// 		throw error;
	// 	}
	// }


	/**
	 * @function wishRequestList
	 */
	async wishRequestList(params: WishesRequest.WishList) {
		try {
			const aggPipe = [];
			const match: any = {};
			match['userId'] = toObjectId(params.userId)
			match['status'] = { '$eq': STATUS.ACTIVE }
			//match['type'] = { '$ne': WISH_TAG_TYPE.CREATOR}
			aggPipe.push({
				"$lookup": {
					"from": "wishes",
					"let": { "wishId": "$wishId" },
					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$_id", "$$wishId"]
										},
										{
											'$eq': ["$status", STATUS.PENDING]
										}
									]
								}

							},
						},
						{
							"$project": {
								_id: 1, description: 1, userId: 1, hostId: 1, userDetail: 1, created: 1, image: 1, isGlobalWish: 1, status: 1, totalBlessings: 1, title: 1
							}
						}
					],
					"as": "wishDetail"
				}
			});
			aggPipe.push({
				'$unwind': {
					"path": "$wishDetail",
					"preserveNullAndEmptyArrays": false
				}
			});
			aggPipe.push({
				"$lookup": {
					"from": "users",
					"let": { "hostId": "$wishDetail.hostId" },

					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$_id", "$$hostId"]
										}
									]
								}
							},
						},
						{
							"$project": {
								_id: 0, "profilePicture": 1, "firstName": 1, "lastName": 1
							}
						}
					],
					"as": "wishDetail.hostDetail"
				}
			});
			aggPipe.push({
				'$unwind': {
					"path": "$wishDetail.hostDetail",
					"preserveNullAndEmptyArrays": true
				}
			});

			// if (params.searchKey) {
			// 	params.searchKey = escapeSpecialCharacter(params.searchKey);
			// 	match["$or"] = [
			// 		{ "wishDetail.description": { "$regex": params.searchKey, "$options": "i" } },
			// 		{ "wishDetail.userDetail.firstName": { "$regex": params.searchKey, "$options": "i" } },
			// 		{ "wishDetail.hostDetail.firstName": { "$regex": params.searchKey, "$options": "i" } }
			// 	];
			// }
			match["$or"] = [
				{
					"$and": [
						{ 'wishDetail.status': STATUS.PENDING },
						{ type: { '$in': [WISH_TAG_TYPE.HOST, WISH_TAG_TYPE.INVITED_HOST] } },
					]
				},
				// {
				// 	"$and": [
				// 		{ 'wishDetail.status':STATUS.ACTIVE},
				// 		{ type:{'$in':[WISH_TAG_TYPE.COHOST,WISH_TAG_TYPE.CONTACTS,WISH_TAG_TYPE.INVITED]}},					
				// 	]
				// },
			];
			aggPipe.push({ "$match": match });
			aggPipe.push({
				"$project": {
					_id: 1, wishId: 1, userId: 1, wishDetail: 1, type: 1, createdAt: 1
				}
			});
			aggPipe.push({ "$sort": { createdAt: -1 } });
			const options = {};
			// let counData = await this.aggregate("tagwishes", aggPipe);
			// let total = counData.length;
			let response = await this.paginate("tagwishes", aggPipe, params.limit, params.pageNo, options, true);
			// response.total = total;
			return response;
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function updateGlobalWishStatus
	 */
	async updateGlobalWishStatus(params: WishesRequest.WishStatus) {
		try {
			const query: any = {};
			query["_id"] = params.id;
			const update = {};
			update["$set"] = params;
			return this.updateOne("wishes", query, update, {});
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function totalBlessReceived
	 */
	async totalBlessReceived(userId) {
		try {
			const aggPipe = [];
			const match: any = {};
			match['userId'] = toObjectId(userId)
			// match['status'] = { '$eq': STATUS.ACTIVE}
			aggPipe.push({ "$match": match });
			aggPipe.push(
				{
					$group: {
						_id: null,
						totalWishbless: { $sum: "$totalBlessings" }
					}
				}
			)

			const options = {};
			let response = await this.aggregate("wishes", aggPipe);
			return response;
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function topPinnedGlobal
	 */
	async topPinnedGlobal() {
		try {
			let response;
			const aggPipe = [];
			const match: any = {};
			match['isGlobalWish'] = true;
			match['isGlobalPinned'] = true;
			match['status'] = { '$eq': STATUS.ACTIVE };
			aggPipe.push({ "$match": match });
			aggPipe.push({ "$sort": { created: -1 } });
			aggPipe.push({
				"$project": {
					_id: 1,
					isGlobalWish: 1,
					title: 1,
					description: 1,
					image: 1,
					intension: 1
				}
			});
			response = await this.aggregate("wishes", aggPipe, { limit: 3 });
			return response
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function wishesLocation
	 */
	async wishesLocation(params, userId, type, dateFilter) {
		try {

			const aggPipe = [];
			const match: any = {};
			match['status'] = STATUS.ACTIVE;
			match['location'] = { $exists: true };
			// aggPipe.push({
			// 	"$lookup": {
			// 		"from": "tagwishes",
			// 		"let": { "id": "$_id" },
			// 		"pipeline": [
			// 			{
			// 				"$match": {

			// 					"$expr":
			// 					{
			// 						$and: [
			// 							{
			// 								"$eq": ["$wishId", "$$id"]
			// 							}
			// 						]
			// 					}

			// 				},
			// 			},
			// 			{ "$project": {_id:0, userId:1} }
			// 		],
			// 		"as": "invitedUser"
			// 	}
			// });
			if (type != 0 && dateFilter) {
				// match["$or"] = [
				// 	{userId:toObjectId(userId)},
				// 	{"invitedUser.userId":{"$in":[toObjectId(userId)]}}
				//  ];
				if (params.startDate) match.createdAt = { "$gte": params.startDate };
			}
			aggPipe.push({ "$match": match });
			aggPipe.push({ "$sort": { createdAt: -1 } });
			aggPipe.push({ "$project": { id: 1, userId: 1, location: 1 } });
			const options = {};
			let data = await this.aggregate("wishes", aggPipe, options);
			return data;
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function pinnedGloablWish
	 */
	async pinnedGloablWish(wishList?, limit?, type?) {
		try {
			const aggPipe = [];
			const match: any = {};
			match['isGlobalWish'] = true
			match['status'] = { '$eq': STATUS.ACTIVE }
			if (wishList) match['_id'] = { '$nin': wishList }
			aggPipe.push({ "$match": match });
			//aggPipe.push({ '$limit': limit?limit :8 });
			aggPipe.push({ $sample: { size: limit ? limit : 8 } });
			if (type) aggPipe.push({ "$sort": { totalBlessings: 1 } });

			aggPipe.push({
				"$project": {
					'wishDetail._id': '$_id',
					'wishDetail.image': '$image',
					'wishDetail.totalBlessings': '$totalBlessings',
					"wishDetail.description": "$description",
					"wishDetail.title": "$title"
				}
			});
			let response = await this.aggregate("wishes", aggPipe);
			return response;
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function pinnedWishCount
	 */
	async pinnedWishCount(params: WishesRequest.PinnedWishes, reportedWishes?) {
		try {
			const query: any = {};
			if (reportedWishes && reportedWishes.length) query["subjectId"] = { $nin: reportedWishes };
			query["userId"] = params.userId;
			query["status"] = { $nin: [STATUS.DELETED, STATUS.IN_ACTIVE, STATUS.CLOSED, STATUS.REPORTED] };
			return await baseDao.countDocuments("pinned_wishes", query);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function wellLogSave
	 */
	async wellLogSave(params) {
		try {
			return await this.insertMany("well_logs", params, {});
		} catch (error) {
			throw error;
		}
	}

}


export const wishesDao = new WishesDao();
