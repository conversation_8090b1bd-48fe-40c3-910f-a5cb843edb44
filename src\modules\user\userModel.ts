"use strict";
const mongoose = require("mongoose");
import { Document, model, Model, Schema } from "mongoose";
import {
	DB_MODEL_REF,
	GENDER,
	UPDATE_TYPE,
	STATUS,
	LOGIN_TYPE,
	USER_TYPE,
	INVITATION_TYPE
} from "@config/index";
import { sendMessageToFlock } from "@utils/FlockUtils";
import { userControllerV1 } from "@modules/user/index";
export interface IUser extends Document {
	name?: string;
	firstName?: string;
	lastName?: string;
	email: string;
	isApproved: boolean;
	//salt: string;
	//hash: string;
	gender?: string;
	profilePicture?: string;
	dob?: number;
	language?: string;
	countryCode?: string;
	phoneNumber?: string;
	fullPhoneNumber?: string;
	isMobileVerified: boolean;
	location?: GeoLocation;
	status: string;
	isContactSynced?:boolean;
	donationCount?: number,
	streakCount?: number,
	wishCount?: number,
	blessingCount?: number,
	gratitudeCount?: number,
	communityCount?: number,
	communityReportCount?: number,
	wishReportCount?: number,
	blessingReportCount?: number,
	gratitudeReportCount?: number,
	country?: string;
	state?: string;
	city?: string;
	communityNotificationStatus: boolean,
	wishesNotificationStatus:boolean,
	gratitudeNotificationStatus:boolean,
	perferMedidation:boolean,
	preferPrayer:boolean
	blessingCreatedCount: number,
	blessingReceivedCount: number,
	gratitudeSharedCount: number,
	gratitudeReceivedCount:number,
	oldEmail:string;
	platform: string;
	googleSocialId:string;
    appleSocialId:string;
	countryFlagCode:string;
	stripeCustomerId:string;
	passwordResetToken:string;
	profileOffset: number;
	globalGratRecieveCount:number;
	lastSeen:number;
	created: number;

}

const geoSchema: Schema = new mongoose.Schema({
	type: { type: String, default: "Point" },
	address: { type: String, required: false },
	coordinates: { type: [Number], index: "2dsphere", required: false }, // [longitude, latitude]
	city: { type: String, required: false },
	country: { type: String, required: false },
    state: { type: String, required: false }
}, {
	_id: false
});
const socialDataSchema = new Schema({
	profilePic: { type: String, required: false, default: "" },
	firstName: { type: String, required: false },
	lastName: { type: String, required: false },
	socialId: { type: String, required: false },
	email: { type: String, trim: true, lowercase: true },
	phoneNumber: { type: String, trim: true },
	_id: false
})
const actionSummary = new Schema({
    adminId: { type: Schema.Types.ObjectId, required: true },
	status: {
		type: String,
		required: true,
		enum: [STATUS.BLOCKED, STATUS.UN_BLOCKED]
	},
	reason: { type: String, required: false, default: "" },
    _id: false
}, {
    versionKey: false,
    timestamps: true
});
const userSchema: Schema = new mongoose.Schema({
	_id: { type: Schema.Types.ObjectId, required: true, auto: true },
	name: { type: String, trim: true, required: false }, 
	firstName: { type: String, trim: true, required: false }, 
	lastName: { type: String, trim: true, required: false }, 
	email: { type: String, trim: true, required: false }, 
	isProfileComplete: { type: Boolean, default: false },
	isEmailVerified: { type: Boolean, default: false },
	isPasswordSet: { type: Boolean, default: false },
	salt: { type: String, required: false },
	hash: { type: String, required: false },
	isApproved: { type: Boolean, default: true }, 
	gender: { // for user
		type: String,
		required: false,
		enum: Object.values(GENDER)
	},
	profilePicture: { type: String, required: false }, 
	dob: { type: Number, required: false }, // for user
	language: { type: String, required: false }, // for user
	interpreterRequired: { type: Boolean }, // for user
	identifyAsAboriginal: { type: Boolean }, // for user
	countryCode: { type: String, required: false },
	phoneNumber: { type: String, required: false },
	fullPhoneNumber: { type: String, required: false },
	isMobileVerified: { type: Boolean, default: false }, // for user
	location: geoSchema, // for user
	postalAddress: { // for user
		address: { type: String, required: false },
		coordinates: { type: [Number], required: false } // [longitude, latitude]
	},
	pushNotificationStatus: { type: Boolean, default: true }, 
	status: {
		type: String,
		enum: [STATUS.BLOCKED, STATUS.UN_BLOCKED, STATUS.DELETED],
		default: STATUS.UN_BLOCKED
	},
	socialData: socialDataSchema,
	isContactSynced:{ type: Boolean, default: false, required: true },
	donationCount: { type: Number, required: false, default: 0 },
	streakCount: { type: Number, required: false, default: 0 },
	lastBlessingTime: { type: Date, required: false },
	wishCount: { type: Number, required: false, default: 0 },
	blessingCount: { type: Number, required: false, default: 0 },
	gratitudeCount: { type: Number, required: false, default: 0 },
	communityCount: { type: Number, required: false, default: 0 },
	communityReportCount: { type: Number, required: false, default: 0 },
	wishReportCount: { type: Number, required: false, default: 0 },
	blessingReportCount: { type: Number, required: false, default: 0 },
	gratitudeReportCount: { type: Number, required: false, default: 0 },
	deletedAt: { type: Date, required: false },
	actionSummary: [actionSummary],
	country: { type: String, required: false },
	state: { type: String, required: false },
	city: { type: String, required: false },
	platform:{ type: String, required: false },
	loginType:{type:String,requred:true,default:LOGIN_TYPE.NORMAL},
	//settgings
	communityNotificationStatus: { type: Boolean, default: true }, 
	wishesNotificationStatus: { type: Boolean, default: true }, 
	gratitudeNotificationStatus: { type: Boolean, default: true }, 
	perferMedidation: { type: Boolean, default: false }, 
	preferPrayer: { type: Boolean, default: false }, 
	locationSharing: { type: Boolean, default: false }, 
	blessingCreatedCount: { type: Number, required: false, default: 0 },
	blessingReceivedCount: { type: Number, required: false, default: 0 },
	gratitudeSharedCount: { type: Number, required: false, default: 0 },
	gratitudeReceivedCount: { type: Number, required: false, default: 0 },
	offerBlessing: { type: String, required: false }, //admin pinned wish id selectd by user in string type
	oldEmail: { type: String, trim: true, required: false }, 
	isDeletedBy: { type: String, enum:[USER_TYPE.ADMIN,USER_TYPE.USER] }, 
	googleSocialId: { type: String, required: false },
	appleSocialId: { type: String, required: false },
	countryFlagCode:{type:String,required:false},
	stripeCustomerId:{type:String,required:false},
	intension:{type:String,required:false},
	deleteReason: { type: String, required: false, default: "" }, //app user delete reason
	adminDeleteReason: { type: String, required: false, default: "" }, //admin user delete reason
	invitationType: { type: String, required: false, default: INVITATION_TYPE.NONE, enum: [INVITATION_TYPE.NONE, INVITATION_TYPE.HOST, INVITATION_TYPE.TAG, INVITATION_TYPE.COMMUNITY] },
	invitationDate: { type: Date, required: false },
	invitationWishId: { type: Schema.Types.ObjectId, required: false },
	updateType:{type:String,enum:[UPDATE_TYPE.EDIT_PROFILE]},
	completeTooltip: {type: Array, required: false,default:[]}, //tootip user checked tooltip
	skipTooltip: {type: Array, required: true},//tootip user skip tooltip
	passwordResetToken: { type: String, required: false },
	profileOffset: { type: Number, required: false },
	lastSeen: { type: Date, required: false },
	globalGratRecieveCount: { type: Number, required: false, default:0 },
	created: { type: Number, default: Date.now }
}, {
	versionKey: false,
	timestamps: true
});
userSchema.post("save", async function (doc) {
	setTimeout(() => {
	}, 10);
});

/**
 * @function _updateDataInModels
 */
const _updateDataInModels = async (doc) => {
	try {
		switch (doc["updateType"]) {
			case UPDATE_TYPE.BLOCK_UNBLOCK:
			case UPDATE_TYPE.APPROVED_DECLINED:
			case UPDATE_TYPE.SET_PROFILE_PIC:
			case UPDATE_TYPE.ABOUT_ME:
			case UPDATE_TYPE.EDIT_PROFILE:{
				await userControllerV1.updateUserDataInRedis(doc, true);
				await userControllerV1.updateUserDataInDb(doc);
				break;
			}
			case UPDATE_TYPE.EDIT_PROFILE: {
				await userControllerV1.updateUserDataInRedis(doc, true);
				break;
			}
		}
	} catch (error) {
		sendMessageToFlock({ "title": "_updateDataInUserModel", "error": { error, "userId": doc["_id"] } });
	}
};
userSchema.post("findOneAndUpdate", function (doc) {

	setTimeout(() => {
		_updateDataInModels(doc);
	}, 10);
});
userSchema.index({ firstName: 1 });
userSchema.index({ lastName: 1 });
userSchema.index({ name: 1 });
userSchema.index({ email: 1 });
userSchema.index({ status: 1 });
userSchema.index({ created: -1 });
// Export user
export const users: Model<IUser> = model<IUser>(DB_MODEL_REF.USER, userSchema);