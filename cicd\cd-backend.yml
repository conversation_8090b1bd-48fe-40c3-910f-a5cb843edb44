version: 0.2

phases:
  install:
    runtime-versions:
      docker: 20



  build:
    commands:
      - curl -LO https://storage.googleapis.com/kubernetes-release/release/v1.16.0/bin/linux/amd64/kubectl
      - chmod +x ./kubectl
      - export PATH=$PWD/:$PATH
      - yum install -y jq
      - aws sts get-caller-identity  --output text
      - aws configure set aws_access_key_id $ID
      - aws configure set aws_secret_access_key $KEY
     # - aws eks update-kubeconfig --name ww-stg-backend --role-arn $EKS_ROLE
      - aws eks update-kubeconfig --name $EKS_CLUSTER_NAME 
      - kubectl get nodes
     #- kubectl rollout restart deployment/$ENVIRONMENT-ww-backend -n $ENVIRONMENT-wishwell
     #- kubectl rollout restart deployment ww-stg -n wishwell-stage
      - kubectl rollout restart deployment $deployment -n $ns
