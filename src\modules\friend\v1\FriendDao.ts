"use strict";
import { BaseDao } from "@modules/baseDao/BaseDao";
import { FRIEND_REQUEST_STATUS, TYPE_NOTIFICATION } from "@config/constant";
import { toObjectId } from "@utils/appUtils";

export class Friend<PERSON>ao extends BaseDao {

	/**
	 * @function isAlreadyAFriend
	 * @description to check if both user is already sended request to each other or already friend or not
	 */
	async isAlreadyAFriend(params, userId: string) {
		try {
			const query: any = {};
			query["$or"] = [
				{ "$and": [{ "userId": params.userId }, { "friendId._id": userId }] },
				{ "$and": [{ "userId": userId }, { "friendId._id": params.userId }] }
			];

			const projection = { createdAt: 0, updatedAt: 0 };

			return await this.findOne("friends", query, projection);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function addFriend
	 */
	async addFriend(params: UserRequest.FriendRequest) {
		try {
			return await this.save("friends", params);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function requestDetails
	 */
	async requestDetails(requestId: string, project = {}) {
		try {
			const query: any = {};
			query._id = requestId;

			const projection = (Object.values(project).length) ? project : { createdAt: 0, updatedAt: 0 };

			return await this.findOne("friends", query, projection);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function getAllFriends
	 */
	async getAllFriends(userId: string) {
		try {
			const query: any = {};
			query["$or"] = [
				{ "userId": userId },
				{ "friendId._id": userId }
			];
			query.status = FRIEND_REQUEST_STATUS.REQUEST_ACCEPTED;

			const projection = { userId: 1, "friendId._id": 1 };

			return await this.find("friends", query, projection);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function findFriendRequestById
	 * @description find a friend request by its ID
	 */
	async findFriendRequestById(requestId: string) {
		try {
			return await this.findOne("friends", { _id: toObjectId(requestId) }, {});
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function updateFriendRequest
	 * @description update a friend request status
	 */
	async updateFriendRequest(request: any) {
		try {
			return await this.updateOne("friends", 
				{ _id: request._id }, 
				{ status: request.status }, 
				{});
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function getPendingFriendRequests
	 * @description get all pending friend requests for a user
	 */
	async getPendingFriendRequests(userId: string, params: ListingRequest) {
		try {
			const { skip = 0, limit = 10 } = params;
			
			const query = {
				"friendId._id": userId,
				"status": FRIEND_REQUEST_STATUS.REQUEST_PENDING
			};
			
			const requests = await this.find("friends", query, {}, { skip, limit });
			const total = await this.countDocuments("friends", query);
			
			// Populate user details for each request
			for (const request of requests) {
				const user = await this.findOne("users", 
					{ _id: toObjectId(request.userId) }, 
					{ name: 1, profilePic: 1, email: 1 });
				request.userDetails = user;
			}
			
			return {
				data: requests,
				total
			};
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function getFriendsList
	 * @description get all friends for a user
	 */
	async getFriendsList(userId: string, params: ListingRequest) {
		try {
			const { skip = 0, limit = 10 } = params;
			
			const query = {
				"$or": [
					{ "userId": userId, "status": FRIEND_REQUEST_STATUS.REQUEST_ACCEPTED },
					{ "friendId._id": userId, "status": FRIEND_REQUEST_STATUS.REQUEST_ACCEPTED }
				]
			};
			
			const friends = await this.find("friends", query, {}, { skip, limit });
			const total = await this.countDocuments("friends", query);
			
			// Populate friend details
			const friendsList = [];
			for (const friend of friends) {
				const friendId = friend.userId === userId ? friend.friendId._id : friend.userId;
				const friendDetails = await this.findOne("users", 
					{ _id: toObjectId(friendId) }, 
					{ name: 1, profilePic: 1, email: 1 });
				
				if (friendDetails) {
					friendsList.push({
						_id: friend._id,
						friendDetails,
						createdAt: friend.createdAt
					});
				}
			}
			
			return {
				data: friendsList,
				total
			};
		} catch (error) {
			throw error;
		}
	}
}

export const friendDao = new FriendDao();
