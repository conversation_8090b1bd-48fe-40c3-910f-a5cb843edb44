declare namespace WishesRequest {

	export interface Id {
		wishId: string;
	}

	export interface Text extends Pagination {
		searchKey: string;
	}
	export interface Add {
		title?: string;
		description: string;
		wishType: string;
		hostId?: string;
		userId?: string;
		isGlobalWish: boolean;
		visibility?: string;
		intension:string;
		image?:string;
		communityId:Array<string>;
		tagContacts: Array<Object>;
		cohosts: Array<Object>;
		userDetail?: Object;
		status?: string;
		location?: Object;
		hostDetail: Object;
	}
	export interface WishList extends Pagination, Filter {
		userId: string;
		status?: string;
		communityId?:any;
	}

	export interface PublicWishList extends Pagination {
		pageNo?: number;
		limit?: number;
		searchKey?: string;
	}
	
	export interface WishDetail {
		wishId: string;
	}

	export interface Delete extends Id {
		status: string;
	}

	export interface Edit extends Id {
		name: string;
		lowercaseName?: string;
		status: string;
	}

	export interface PinnedWishes {
		userId?: string;
		subjectId: string;
		type: string;
	}

	export interface DeleteWish {
		wishId: string;
		status: string;
		location?: Object;
	}

	export interface EditWish {
		title?: string;
		description?: string;
		wishId: string;
		intension?: string;
		image?: string;
		addCommunityId: Array<string>;
		removeCommunityId:Array<string>;
		addTagContacts?: Array<TagContact>;
		removeTagContacts?: Array<TagContact>;
		addCohosts?: Array<Object>;
		removeCohosts?: Array<Object>;

	}
	export interface EditWishDetail {
		wishId: string;
	}
	export interface TagContact {
		userId?: string;
		contactId?: string;
		countryCode?: string;
		phoneNumber?: string;
		isAppUser?: boolean;
	}
	export interface AcceptReject {
		wishId: string;
		type: string;
		rejectReason: string;
	}
	export interface BlessingPerform {
		wishId: string;
		type: string;
		rejectReason: string;
	}
	export interface RemoveWish {
		wishId: string;
		status: string;
	}
	export interface WishStatus {
		id: string;
		status?: string;
		isGlobalPinned?: boolean;
	}
	export interface MapLocation
	{
		type?:number;
	}
}