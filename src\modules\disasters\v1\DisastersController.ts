"use strict";

import { MESSAGES } from "@config/index";
import { disastersDaoV1 } from "@modules/disasters/index";

export class DisastersController {
    /**
	   * @function categoriesSearch
	   * @description get the list of disaster categories in our application
	   * @param params 
	   * @returns returns the list of the disaster categories
	   */
	async categoriesSearch(params: ListingRequest) {
		try {
			let step1 = await disastersDaoV1.categoriesSearch(params);
			return MESSAGES.SUCCESS.LIST(step1);
		} catch (error) {
			console.log(error, "error detected");
			throw error;
		}
	}
}

export const disastersController = new DisastersController();