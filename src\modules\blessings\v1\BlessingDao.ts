"use strict";
import { BLESSING_PERFORM_TYPE, STATUS } from "@config/constant";
import { BaseDao } from "@modules/baseDao/BaseDao";
import { escapeSpecialCharacter,toObjectId } from "@utils/appUtils";
import { Types } from "mongoose";
import { FLAG_TYPE, DB_MODEL_REF, REPORT_TYPE, FLAGGED_BLESSING_SORT_CRITERIA,NOTIFICATION_TYPE } from "@config/constant";
import moment = require("moment");
export class BlessingDao extends BaseDao {
     /**    
	 * @function findBlessingById
	 */
	async findBlessingById(blessingId: string, project = {}) {
		try {
			const query: any = {};
			query._id = toObjectId(blessingId);
			query.status ={ '$nin': [STATUS.DELETED,STATUS.IN_ACTIVE,STATUS.UN_PUBLISHED]};
			const projection = (Object.values(project).length) ? project : { createdAt: 0, updatedAt: 0 };
			return await this.findOne("perform_blessings", query, projection);
		} catch (error) {
			throw error;
		}
	}
    /**
     * @function userBlessingLib
     */
    async userBlessingLib(params: ListingRequest) {
        try {
            const aggPipe = [];
            const match: any = {};
            match.blessingType = params.type;
            //match.isGlobalBlessing=true;
            match.status = STATUS.ACTIVE;
            if (Object.keys(match).length) aggPipe.push({ "$match": match });
            aggPipe.push({ "$project": { _id: 1, name: 1, blessingType: 1, language: 1, audioFile: 1, voiceover: 1,intension:1,authorName:1 } });
            let sort = {};
            (params.sortBy && params.sortOrder) ? sort = { [params.sortBy]: params.sortOrder } : sort = { name: 1 };
            aggPipe.push({ "$sort": sort });
            const options = { collation: true };
            // let counData = await this.aggregate("blessing_library", aggPipe, {});
            // let total = counData.length;
            let response = await this.paginate("blessing_library", aggPipe, params.limit, params.pageNo, options, true);
            // response.total = total;
            return response;
        } catch (error) {
            throw error;
        }
    }

    /**
      * @function findFormBelssings
      */
    async findFormBelssings(params: BlessingRequest.Add, tokenData) {
        try {
            const query: any = {};
            query.wishId = params.wishId;
            query.userId = tokenData.userId;
            query.listenBlessingId = params.listenBlessingId
            query.status = { "$ne": STATUS.DELETED };
            const projection = { updatedAt: 0 };
            return await this.findOne("perform_blessings", query, projection);
        } catch (error) {
            throw error;
        }
    }

    /**
      * @function saveFormBelssings
      */
    async saveFormBelssings(params: BlessingRequest.Add) {
        try {
            return await this.save("perform_blessings", params);
        } catch (error) {
            throw error;
        }
    }

    /**
      * @function findGuidedBlessingById
      */
    async findGuidedBlessingById(id: string, project = {}) {
        try {
            const query: any = {};
			query._id = id;
			query.status = { "$ne": STATUS.DELETED };
			const projection = (Object.values(project).length) ? project : { createdAt: 0, updatedAt: 0 };
			return await this.findOne("blessing_library", query, projection);
        } catch (error) {
            throw error;
        }
    }

	/**
      * @function guidedBlessingDetail
      */
	async guidedBlessingDetail(id: string) {
		try {
			const data = await this.aggregate("blessing_library", [
				{
					$match: { _id: toObjectId(id) }
				},
				{
					$lookup: {
						from: DB_MODEL_REF.PERFORM_BLESSING,
						localField: "_id",
						foreignField: "listenBlessingId",
						as: "performBlessingDetail",
						pipeline: [
							{
								$project: { _id: 0, status: 1 }
							},
							{
								$match: { status: { $ne: STATUS.DELETED } }
							}
						]
					}
				},
				{
					$project: { _id: 1, status: 1, image: 1, name: 1, blessingType: 1, language: 1, audioFile: 1, voiceover: 1, intension: 1, authorName: 1, createdAt: 1, uses: { $size: "$performBlessingDetail" } }
				}
			]);
			return data[0];
		} catch (error) {
			throw error;
		}
	}
    /**
      * @function editGuidedBlessing
      */
    async editGuidedBlessing(params: BlessingRequest.EditGuidedBlessing) {
        try {
            const query: any = {};
			query["_id"] = params.id;
			const update = {};
			update["$set"] = params;
			return await this.updateOne("blessing_library", query, update, {});
        } catch (error) {
            throw error;
        }
    }
    /**
	 * @function findTaggedUserDetail
	 */
	async wishesBlessing(params:BlessingRequest.WishBlessings,reportList?,reportGratitudeList?){
		try {
			const aggPipe = [];
			const match: any = {};
			match['wishId'] = toObjectId(params.wishId);
			match['status'] = { '$nin': [STATUS.DELETED,STATUS.IN_ACTIVE]};
			match['type'] = { $in: [BLESSING_PERFORM_TYPE.AUDIO, BLESSING_PERFORM_TYPE.TEXT] };
            if(reportList && reportList.length) match['_id'] ={ '$nin': reportList};
			if(reportGratitudeList && reportGratitudeList.length) match['_id'] ={ '$nin': reportGratitudeList};
			aggPipe.push({
				"$lookup": {
					"from": "users",
					"let": { "userId": "$userId" },
					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$_id", "$$userId"]
										}
									]
								}

						 },
						},
						{ "$project": {_id:0, firstName:1, lastName:1,profilePicture:1,name:1} }
					],
					"as": "userDetail"
				}
			});
			aggPipe.push({
				'$unwind': {
					"path": "$userDetail",
					"preserveNullAndEmptyArrays": true
				}
			})
            // if(reportList && reportList.length){

            // }else{

            // }
            aggPipe.push({
				"$lookup": {
					"from": "gratitudes",
					"let": { "blessingId": "$_id" },
					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$blessingId", "$$blessingId"]
										},
										{
											'$in':  ["$status", [STATUS.ACTIVE]]
										},
                                        {
											'$not': {'$in': ["$_id", reportGratitudeList]}
										},
									]
								}

						 },
						},
						{ "$project": {type:1, status:1, audio:1, notes:1, gratitudeType:1, userId:1,userDetail:1,blessingCreatorId:1,created:1
						} }
					],
					"as": "gratitudeDetails"
				}
			});
			aggPipe.push({ "$match": match });
			aggPipe.push({ "$sort": { created: -1} });
			aggPipe.push({
				"$project": {
					_id:1, userId:1,userDetail:1,status:1,type:1,listenAudio:1,audio:1,notes:1,wishDetail:1,created:1,gratitudeDetails:1,isGratitude:1,listenDuration:1
				}
			});
            const options = { collation: true };
			// let counData = await this.aggregate("perform_blessings", aggPipe, {});
            // let total = counData.length;
            let response = await this.paginate("perform_blessings", aggPipe, params.limit, params.pageNo, options, true);
            // response.total = total;
            return response;
		} catch (error) {
			throw error;
		}
	}
    /** 
	 * @function flaggedBlessings
	 */
    async flaggedBlessings(params: BlessingRequest.FlaggedBlessing) {
        try {
            let match: any = {};
			let aggPipe: any = [];
			let sort: any = {};
            match["flagStatus"] = { $in: [FLAG_TYPE.FLAGGED, FLAG_TYPE.UN_FLAGGED, FLAG_TYPE.DELETED] };
            match['isGratitude']={'$ne':true}
            const userLookup = {
				from: DB_MODEL_REF.USER,
				localField: "userId",
				foreignField: "_id",
				as: "userDetails"
			};
            if(params.searchKey) {
                params.searchKey = params.searchKey.replace(/B-|W-/g, "");
                params.searchKey = escapeSpecialCharacter(params.searchKey);
				match = {
					...match,
                    $expr: {
                        $or: [
                            {
                                $regexMatch: {
                                    input: { $concat: ['$userDetails.firstName', ' ', '$userDetails.lastName'] },
                                    regex: params.searchKey,
                                    options: 'i'
                                }
                            },
                            {
                                $regexMatch: {
                                    input: { $toString: "$blessingNumber" },
                                    regex: params.searchKey,
                                    options: 'i'
                                }
                            },
                            {
                                $regexMatch: {
                                    input: { $toString: "$wishDetail.wishNumber" },
                                    regex: params.searchKey,
                                    options: 'i'
                                }
                            },
                            {
                                $regexMatch: {
                                    input: "$flagStatus",
                                    regex: params.searchKey,
                                    options: 'i'
                                }
                            },
                        ]
                    }
                }
			}
            aggPipe.push({ "$lookup": userLookup });
            aggPipe.push({ "$unwind": "$userDetails" });
			aggPipe.push({ "$match": match });
            const project = { _id: 1, userId: 1, type: 1, createdAt: 1, reportCount: 1, flagStatus: 1, wishDetail: 1, blessingNumber: 1, "userDetails._id": 1, "userDetails.firstName": 1, "userDetails.lastName": 1, audio: 1, notes: 1 };
            if (params.sortBy == 1) {
				params.sortBy = 1;
			} else {
				params.sortBy = -1;
			}
            if(params.sortCriteria == FLAGGED_BLESSING_SORT_CRITERIA.BLESSED_BY) {
                sort = { "userDetails.firstName": params.sortBy }
            } else if (params.sortCriteria == FLAGGED_BLESSING_SORT_CRITERIA.BLESSING_NUMBER) {
                sort = { "blessingNumber": params.sortBy }
            } else if (params.sortCriteria == FLAGGED_BLESSING_SORT_CRITERIA.WISH_NUMBER) {
                sort = { "wishDetail.wishNumber": params.sortBy }
            } else if (params.sortCriteria == FLAGGED_BLESSING_SORT_CRITERIA.STATUS) {
                sort = { "flagStatus": params.sortBy }
            } else if (params.sortCriteria == FLAGGED_BLESSING_SORT_CRITERIA.CREATED_AT) {
                sort = { "createdAt": params.sortBy }
            }
            aggPipe.push({ "$sort": sort });
			aggPipe.push({ "$project": project });
            console.log(aggPipe)
            const options = { collation: true };
            return this.paginate("perform_blessings", aggPipe, params.limit, params.pageNo, options, true);
        } catch (error) {
            throw error;
        }
    }
    /** 
	 * @function findPerformBlessingById
	 */
    async findPerformBlessingById(id: string, project = {}) {
        try {
            const query: any = {};
			query._id = id;
			const projection = (Object.values(project).length) ? project : { createdAt: 0, updatedAt: 0 };
			return await this.findOne("perform_blessings", query, projection);
        } catch (error) {
            throw error;
        }
    }
    /** 
	 * @function performBlessingDetail
	 */
    async performBlessingDetail(params: BlessingRequest.GuidedBlessingDetail) {
        try {
            let match: any = {};
			let aggPipe: any = [];
            match["_id"] = Types.ObjectId(params.id);
            aggPipe.push({ "$match": match });
            const userLookup = {
                from: DB_MODEL_REF.USER,
				localField: "userId",
				foreignField: "_id",
				as: "userDetails"
            };
            const wishLookup = {
                from: DB_MODEL_REF.WISHES,
				localField: "wishId",
				foreignField: "_id",
				as: "wishDetails"
            }
            const project = {
                _id: 1,
                blessingNumber: 1,
                userId: 1,
                "userDetails._id": 1,
                "userDetails.firstName": 1,
                "userDetails.lastName": 1,
                createdAt: 1,
                type: 1,
                notes: 1,
                audio: 1,
                flagStatus: 1,
                reportCount: 1,
                unflagHistory: 1,
                deleteReason: 1,
                deletedAt: 1,
                "wishDetails._id": 1,
                "wishDetails.userId": 1,
                "wishDetails.userDetail": 1,
                "wishDetails.wishNumber": 1,
                "wishDetails.createdAt": 1,
                "wishDetails.intension": 1,
                "wishDetails.description": 1,
                "wishDetails.image": 1
            }
            aggPipe.push({ "$lookup": userLookup });
            aggPipe.push({ "$lookup": wishLookup });
            aggPipe.push({ "$unwind": "$userDetails" });
            aggPipe.push({ "$unwind": "$wishDetails" });
            aggPipe.push({ "$project": project });
            const result = await this.aggregate("perform_blessings", aggPipe);
            return result[0];
        } catch (error) {
            throw error;
        }
    }

   /**
	 * @function wellBlessLogs
	 */
	async wellBlessLogs1(params:BlessingRequest.WellLog,tokenData){
		try {
			const aggPipe = [];
			const match: any = {};
            match['status'] = { '$nin': [STATUS.DELETED,STATUS.IN_ACTIVE]};
			match['senderId'] = toObjectId(tokenData.userId);
            // match["$or"] = [
            //     {"receiverId":{"$in":[toObjectId(tokenData.userId)]}},
            //     {"senderId":toObjectId(tokenData.userId)}
            // ];
            aggPipe.push({ "$match": match });   
			
			const senderLog = {
				from: "users",
				localField: "senderId",
				foreignField: "_id",
				as: "senderDetail",
				pipeline: [
					{
						$project: {__id:1, firstName:1, lastName:1,profilePicture:1,name:1,phoneNumber:1,fullPhoneNumber:1 }
					}
				]
			};

			const receiverLog = {
				from: "users",
				localField: "receiverId",
				foreignField: "_id",
				as: "receiverDetail",
				pipeline: [
					{
						$project: {_id:1, firstName:1, lastName:1,profilePicture:1,name:1,phoneNumber:1,fullPhoneNumber:1 }
					}
				]
			};

			const contactLog = {
				from: "contacts",
				localField: "receiverId",
				foreignField: "_id",
				as: "contactUserDetail",
				pipeline: [
					{
						$project: {_id:1, firstName:1, lastName:1,profilePicture:1,name:1,phoneNumber:1 }
					}
				]
			};
			const communitiesLog = {
				from: "communities",
				localField: "receiverId",
				foreignField: "_id",
				as: "communitiesDetail",
				pipeline: [
					{
						$project: {_id:1, name:1 }
					}
				]
			};
			aggPipe.push({ "$lookup": senderLog });
			aggPipe.push({ "$lookup": receiverLog });
			aggPipe.push({ "$lookup": contactLog });
			aggPipe.push({ "$lookup": communitiesLog });
           
			if(params.phoneNumber){
			let match1={};
			let phone = params.phoneNumber.split(',');
            match1["$or"] = [
				{"senderDetail":{"$elemMatch":{"phoneNumber":{"$in":phone}}}},
				{"senderDetail":{"$elemMatch":{"fullPhoneNumber":{"$in":phone}}}},
                {"contactUserDetail":{"$elemMatch":{"phoneNumber":{"$in":phone}}}},
                {"receiverDetail":{"$elemMatch":{"phoneNumber":{"$in":phone}}}},
			    {"receiverDetail":{"$elemMatch":{"fullPhoneNumber":{"$in":phone}}}}
            ];
            aggPipe.push({ "$match": match1 });  
		    }
			
           aggPipe.push({ "$sort": { created: -1} });
			aggPipe.push({
				"$project": {
					_id:1, receiverDetail:1,senderDetail:1,subjectDetail:1,type:1,isRead:1,created:1,senderId:1,receiverId:1,contactUserDetail:1,communitiesDetail:1
				}
			});
            const options = {  };
          // let counData = await this.aggregate("well_logs", aggPipe, {});
           //let total = counData.length;
           let response = await this.paginate("well_logs", aggPipe, params.limit, params.pageNo, options, true);
           //response.total = total;
            return response;
		} catch (error) {
			throw error;
		}
	} 

	/**
 * @function wellBlessLogs
 */
async wellBlessLogs(params: BlessingRequest.WellLog, tokenData) {
    try {
        const aggPipe = [];
        const match: any = {};
        const addFieldsStage: any = {};
        
        match['status'] = { '$nin': [STATUS.DELETED, STATUS.IN_ACTIVE] };
        match['subjectDetail.type'] = { $ne: BLESSING_PERFORM_TYPE.NONE };
        match["$or"] = [
            { "receiverId": { "$in": [toObjectId(tokenData.userId)] } },
            { "globalGratiduteUser": { "$in": [toObjectId(tokenData.userId)] } },
            { "senderId": toObjectId(tokenData.userId) }
        ];

        aggPipe.push({ "$match": match });

        const senderLog = {
            from: "users",
            localField: "senderId",
            foreignField: "_id",
            as: "senderDetail",
            pipeline: [
                { $project: { _id: 1, firstName: 1, lastName: 1, profilePicture: 1, name: 1, phoneNumber: 1, fullPhoneNumber: 1 } }
            ]
        };

        const receiverLog = {
            from: "users",
            localField: "receiverId",
            foreignField: "_id",
            as: "receiverDetail",
            pipeline: [
                { $project: { _id: 1, firstName: 1, lastName: 1, profilePicture: 1, name: 1, phoneNumber: 1, fullPhoneNumber: 1 } }
            ]
        };

        const tagUserLog = {
            from: "users",
            localField: "taggedUser",
            foreignField: "_id",
            as: "tagUserDetail",
            pipeline: [
                { $project: { _id: 1, firstName: 1, lastName: 1, profilePicture: 1, name: 1, phoneNumber: 1, fullPhoneNumber: 1 } }
            ]
        };

        const contactLog = {
            from: "contacts_v2",
            localField: "taggedUser",
            foreignField: "_id",
            as: "contactUserDetail",
            pipeline: [
                { $project: { _id: 1, firstName: 1, lastName: 1, profilePicture: 1, name: 1, phoneNumbers: 1 } }
            ]
        };

        const communitiesLog = {
            from: "communities",
            localField: "taggedUser",
            foreignField: "_id",
            as: "communitiesDetail",
            pipeline: [
                { $project: { _id: 1, name: 1, image: 1 } }
            ]
        };

        aggPipe.push({ "$lookup": senderLog });
        aggPipe.push({ "$unwind": { "path": "$senderDetail", "preserveNullAndEmptyArrays": false } });
        aggPipe.push({ "$lookup": receiverLog });
        aggPipe.push({ "$lookup": tagUserLog });
        aggPipe.push({ "$lookup": contactLog });
        aggPipe.push({ "$lookup": communitiesLog });

        if (params.phoneNumber) {
            let match1 = {};
            let phone = params.phoneNumber.split(',');
            match1["$or"] = [
                { "senderDetail.phoneNumber": { "$in": phone } },
                { "senderDetail.fullPhoneNumber": { "$in": phone } },
                { "tagUserDetail": { "$elemMatch": { "phoneNumber": { "$in": phone } } } },
                { "tagUserDetail": { "$elemMatch": { "fullPhoneNumber": { "$in": phone } } } },
                { "contactUserDetail": { "$elemMatch": { "phoneNumbers.phoneNumber": { "$in": phone } } } },
                { "receiverDetail": { "$elemMatch": { "phoneNumber": { "$in": phone } } } },
                { "receiverDetail": { "$elemMatch": { "fullPhoneNumber": { "$in": phone } } } },
            ];
            aggPipe.push({ "$match": match1 });
        }

        aggPipe.push({ "$sort": { created: -1 } });
        aggPipe.push({
            "$project": {
                _id: 1, receiverDetail: 1, senderDetail: 1, subjectDetail: 1, type: 1, isRead: 1,
                created: 1, senderId: 1, receiverId: 1, contactUserDetail: 1, communitiesDetail: 1,
                hostDetail: 1, tagUserDetail: 1, globalGratiduteUser: 1, hostId: 1
            }
        });

        const options = {};

        let response = await this.paginate("well_logs", aggPipe, params.limit, params.pageNo, options, true);

        return response;
    } catch (error) {
        throw error;
    }
}

	//  /**
	//  * @function wellBlessLogs
	//  */
	//  async wellBlessLogs(params:BlessingRequest.WellLog,tokenData){
	// 	try {
	// 		//tokenData.userId = '653a1342dc19744c4449fb9e';
	// 		const aggPipe = [];
	// 		const match: any = {};
	// 		const addFieldsStage:any ={};
    //         match['status'] = { '$nin': [STATUS.DELETED,STATUS.IN_ACTIVE]};
	// 		match['subjectDetail.type'] = { $ne: BLESSING_PERFORM_TYPE.NONE };
	// 		match["$or"] = [
    //             {"receiverId":{"$in":[toObjectId(tokenData.userId)]}},
	// 			{"globalGratiduteUser":{"$in":[toObjectId(tokenData.userId)]}},
    //             {"senderId":toObjectId(tokenData.userId)}
    //         ];
			
    //         aggPipe.push({ "$match": match });   
	// 		const senderLog = {
	// 			from: "users",
	// 			localField: "senderId",
	// 			foreignField: "_id",
	// 			as: "senderDetail",
	// 			pipeline: [
	// 				{
	// 					$project: {__id:1, firstName:1, lastName:1,profilePicture:1,name:1,phoneNumber:1,fullPhoneNumber:1}
	// 				}
	// 			]
	// 		};

	// 		const receiverLog = {
	// 			from: "users",
	// 			localField: "receiverId",
	// 			foreignField: "_id",
	// 			as: "receiverDetail",
	// 			pipeline: [
	// 				{
	// 					$project: {_id:1, firstName:1, lastName:1,profilePicture:1,name:1,phoneNumber:1,fullPhoneNumber:1 }
	// 				}
	// 			]
	// 		};
	// 		const tagUserLog = {
	// 			from: "users",
	// 			localField: "taggedUser",
	// 			foreignField: "_id",
	// 			as: "tagUserDetail",
	// 			pipeline: [
	// 				{
	// 					$project: {_id:1, firstName:1, lastName:1,profilePicture:1,name:1,phoneNumber:1,fullPhoneNumber:1 }
	// 				}
	// 			]
	// 		};

	// 		// const hostLog = {
	// 		// 	from: "users",
	// 		// 	localField: "hostId",
	// 		// 	foreignField: "_id",
	// 		// 	as: "hostDetail",
	// 		// 	pipeline: [
	// 		// 		{
	// 		// 			$project: {_id:1, firstName:1, lastName:1,profilePicture:1,name:1 }
	// 		// 		}
	// 		// 	]
	// 		// };

	// 		const contactLog = {
	// 			from: "contacts",
	// 			localField: "taggedUser",
	// 			foreignField: "_id",
	// 			as: "contactUserDetail",
	// 			pipeline: [
	// 				{
	// 					$project: {_id:1, firstName:1, lastName:1,profilePicture:1,name:1,phoneNumber:1 }
	// 				}
	// 			]
	// 		};
	// 		const communitiesLog = {
	// 			from: "communities",
	// 			localField: "taggedUser",
	// 			foreignField: "_id",
	// 			as: "communitiesDetail",
	// 			pipeline: [
	// 				{
	// 					$project: {_id:1, name:1, image: 1 }
	// 				}
	// 			]
	// 		};
	// 		aggPipe.push({ "$lookup": senderLog });
	// 		aggPipe.push({ "$unwind": { "path": "$senderDetail", "preserveNullAndEmptyArrays": false } });
	// 		aggPipe.push({ "$lookup": receiverLog });
	// 		aggPipe.push({ "$lookup": tagUserLog });
	// 		aggPipe.push({ "$lookup": contactLog });
	// 		//aggPipe.push({ "$lookup": hostLog });
	// 		aggPipe.push({ "$lookup": communitiesLog });
			
	// 		if(params.phoneNumber){
	// 		let match1={};
	// 		let phone = params.phoneNumber.split(',');
    //         match1["$or"] = [
	// 			{"senderDetail.phoneNumber":{"$in":phone}},
	// 			{"senderDetail.fullPhoneNumber":{"$in":phone}},
	// 			{"tagUserDetail":{"$elemMatch":{"phoneNumber":{"$in":phone}}}},
	// 			{"tagUserDetail":{"$elemMatch":{"fullPhoneNumber":{"$in":phone}}}},
    //             {"contactUserDetail":{"$elemMatch":{"phoneNumber":{"$in":phone}}}},
    //             {"receiverDetail":{"$elemMatch":{"phoneNumber":{"$in":phone}}}},
	// 		    {"receiverDetail":{"$elemMatch":{"fullPhoneNumber":{"$in":phone}}}},
				
    //         ];
    //         aggPipe.push({ "$match": match1 });  
	// 	    }
			
    //        aggPipe.push({ "$sort": { created: -1} });
	// 		aggPipe.push({
	// 			"$project": {
	// 				_id:1, receiverDetail:1,senderDetail:1,subjectDetail:1,type:1,isRead:1,created:1,senderId:1,receiverId:1,contactUserDetail:1,communitiesDetail:1, hostDetail:1,tagUserDetail:1,globalGratiduteUser:1,hostId:1
	// 			}
	// 		});
    //         const options = {  };
    //     //    let counData = await this.aggregate("well_logs", aggPipe, {});
    //     //    let total = counData.length;
    //       let response = await this.paginate("well_logs", aggPipe, params.limit, params.pageNo, options, true);
    //         //response.total = total;
    //         return response;
	// 	} catch (error) {
	// 		throw error;
	// 	}
	// } 

    /** 
	 * @function performBlessingDetail
	 */
    async flaggedBlessingReports(params: BlessingRequest.FlaggedBlessing) {
        try {
            let match: any = {};
			let aggPipe: any = [];
			let sort: any = {};
            match["subjectId"] = Types.ObjectId(params.blessingId);
			match["type"] = REPORT_TYPE.BLESSING;
            const userLookup = {
				from: DB_MODEL_REF.USER,
				localField: "userId",
				foreignField: "_id",
				as: "userDetails"
			};
			aggPipe.push({ "$match": match });
			aggPipe.push({ "$lookup": userLookup });
            const project = {
				createdAt: 1, description: 1, reportType: 1, userId: 1, "userDetails.firstName": 1, "userDetails.lastName": 1, "userDetails.profilePicture": 1
			};

            if (params.sortBy == 1) {
				params.sortBy = 1;
			} else {
				params.sortBy = -1;
			}
			sort = { "createdAt": params.sortBy }
			aggPipe.push({ "$sort": sort });
			aggPipe.push({ "$project": project });
            const options = { collation: true };
            return this.paginate("reports", aggPipe, params.limit, params.pageNo, options, true);
        } catch (error) {
            throw error;
        }
    }

    /** 
	 * @function unflagDeletePerformBlessing
	 */
    async unflagDeletePerformBlessing(params: BlessingRequest.UnflagDeletePerformBlessing, tokenData: TokenData) {
        try {
            const query: any = {};
			const update: any = {};
            query["_id"] = params.id;
            if(params.status == FLAG_TYPE.UN_FLAGGED) {
                update["$set"] = {
                    isFlagged: false,
                    flagStatus: FLAG_TYPE.UN_FLAGGED
                }
                update["$push"] = {
                    unflagHistory: {
                        adminId: tokenData.userId
                    }
                }
            } else {
                update["$set"] = {
                    status: STATUS.DELETED,
                    flagStatus: STATUS.DELETED,
                    deleteReason: params.deleteReason,
                    deletedAt: Date.now()
                }
            }
            return this.updateOne("perform_blessings", query, update, {});
        } catch (error) {
            throw error;
        }
    }


    /**
	 * @function totalBlessListen
	 */
	async totalBlessListen(userId,dateFilter,offset,timezone) {
		try {
			const aggPipe = [];
			const match: any = {};
            let date = new Date();
			console.log(date,'=============================date')
			// const localTime = new Date(Date.now() - new Date().getTimezoneOffset() * 60000);
			const localTime = new Date(Date.now() + offset);
			const localTimeDate = new Date(Date.now() + offset);
			console.log(localTime,'===================LOCAL TIME')
			let endDate = new Date(localTime.setHours(23,59,59,999));
			let startDate = new Date(localTime.setHours(0,0,0,0));

			console.log(startDate, endDate,'=============================BEFORE')

			if(offset) {
				// TIMEZONES TABLE
				if(timezone) {
					console.log(localTimeDate,'===================LOCAL TIME DATE')
					var t1 = await this.save("timezones", {
						userId: toObjectId(userId),
						offset: offset,
						date: date,
						localDate: localTimeDate,
						startDate: startDate,
						endDate: endDate
					});
				}

				const offsetTime = offset / 1000; // convert milliseconds to seconds
				const offsetHours = Math.floor(offsetTime / 3600);
				const offsetMinutes = Math.floor((offsetTime % 3600) / 60);

				// console.log(offsetHours, offsetMinutes)
				// console.log(startDate.getUTCHours(), 'startDate.getUTCHours()')
				// console.log(startDate.getUTCMinutes(), 'startDate.getUTCMinutes()')

				// var offsetStart = moment(startDate).subtract(offsetTime, "seconds").toDate();
				// var offsetEnd = moment(endDate).subtract(offsetTime, "seconds").toDate();
				// console.log(offsetStart, offsetEnd,'===================================AFTER')

				// startDate.setUTCHours(startDate.getUTCHours() - offsetHours);
				// startDate.setUTCMinutes(startDate.getUTCMinutes() - offsetMinutes);
				// endDate.setUTCHours(endDate.getUTCHours() - offsetHours);
				// endDate.setUTCMinutes(endDate.getUTCMinutes() - offsetMinutes);

				startDate.setHours(startDate.getHours() - offsetHours);
				startDate.setMinutes(startDate.getMinutes() - offsetMinutes);
				endDate.setHours(endDate.getHours() - offsetHours);
				endDate.setMinutes(endDate.getMinutes() - offsetMinutes);

				if(timezone) {
					await this.updateOne("timezones", { _id: t1._id }, { $set: { offsetStartDate: startDate, offsetEndDate: endDate } }, {});
				}
			}

			console.log(startDate, endDate)

			match['userId'] = toObjectId(userId);
			// match['status'] = { '$eq': STATUS.ACTIVE };
            match['isGratitude'] = false;
            if (dateFilter) match.createdAt = { "$gte": startDate, "$lte": endDate };
			aggPipe.push({ "$match": match });
			aggPipe.push(
				{
					$group: {
						_id: null,
						totalListenBlessTime: { $sum: "$listenDuration" },
                        totalDocuments: { $sum: 1 }
					}
				}
			);
            
			const options = {};
			let response = await this.aggregate("perform_blessings", aggPipe);
			return response;
		} catch (error) {
			throw error;
		}
	}

    /**
	 * @function saveStreakCount
	 */
    async saveStreakCount(params: BlessingRequest.Add, tokenData: TokenData) {
        try {
            const lastBlessing = await this.findOne("users", { _id: tokenData.userId }, { streakCount: 1, lastBlessingTime: 1 });
            if(lastBlessing && lastBlessing.lastBlessingTime) {
                const currentDate = new Date().toISOString().slice(0,10);
                const blessingDate = lastBlessing.lastBlessingTime.toISOString().slice(0,10);
                if(currentDate !== blessingDate) {
                    return this.updateOne("users", { _id: tokenData.userId }, { lastBlessingTime: Date.now(), $inc: { streakCount: 1 } }, {});
                }
            } else {
                return this.updateOne("users", { _id: tokenData.userId }, { lastBlessingTime: Date.now(), $inc: { streakCount: 1 } }, {});
            }
        } catch (error) {
            throw error;
        }
    }

	  /**
		 * @function blessLocation
		 */
	async blessLocation(params,userId,type, dateFilter) {
		try {
			const aggPipe = [];
			const match: any = {};
			match['status'] = STATUS.ACTIVE;
			match['location'] = { $exists: true };
			// match['userId']  = toObjectId(userId);
			aggPipe.push({ "$match": match });
			aggPipe.push({ "$sort": { createdAt: -1}});
			if(type!=0 && dateFilter){
				if (params.startDate) match.createdAt = { "$gte": params.startDate };
			}
			aggPipe.push(
				{"$project":{id:1, location:1}},
				);
			const options = {};
			let data = await this.aggregate("perform_blessings", aggPipe,options);
			return data;
		} catch (error) {
			throw error;
		}
	}
	 /**
		* @function totalBlessDuration
		*/
	async totalBlessDuration(params) {
		try {
			const aggPipe = [];
			const match: any = {};
			// match['status'] = STATUS.ACTIVE;
			match['location'] = { $exists: true };
			aggPipe.push({ "$match": match });
			if (params.startDate) match.createdAt = { "$gte": params.startDate };
			aggPipe.push({ "$sort": { createdAt: -1}});
			aggPipe.push(
				{
					$group: {
						_id: null,
						totalListenBlessTime: { $sum: "$listenDuration" },
                        totalDocuments: { $sum: 1 }

					  }
				}
				);
			const options = {};
			let data = await this.aggregate("perform_blessings", aggPipe,options);
			return data;
		} catch (error) {
			throw error;
		}
	}

 

}
export const blessingDao = new BlessingDao();