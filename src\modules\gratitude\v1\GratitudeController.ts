"use strict";
import { MESSAGES, STATUS,NOTIFICATION_TYPE, FLAG_TYPE, GRATITUDE_TYPE,WISH_TAG_TYPE, MESSAGES_NOTIFICATION, ADMIN_NOTIFICATION_TYPE, TITLE_NOTIFICATION, TYPE_NOTIFICATION ,GEO_LOCATION_TYPE} from "@config/constant";
import { gratitudesDaoV1 } from "@modules/gratitude/index";
import { toObjectId } from "@utils/appUtils";
import { baseDao } from "@modules/baseDao/index";
import { userDaoV1 } from "@modules/user/index"
import { wishesDaoV1 } from "@modules/wishes/index"
import { blessingDaoV1 } from "@modules/blessings/index"
import { notificationManager } from "@utils/NotificationManager";
import { commonControllerV1 } from "@modules/common/index";
import { array } from "joi";
const fetch = require('node-fetch');
export class GratitudesController {

     /**
         * @function gratitudes
         * @description user personalised/global gratitudes on blessing
         *
         */
     async gratitudes(params: GratitudesRequest.Add, tokenData) {
        try {
            const step1 = await userDaoV1.findUserById(tokenData.userId);
            if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
            if(!step1.isProfileComplete) return Promise.reject(MESSAGES.ERROR.REGISTRATION_PENDING);
            let step2 = await wishesDaoV1.findWishById(params.wishId);
            if (!step2) return Promise.reject(MESSAGES.ERROR.WISH_NOT_FOUND);
            let step3 = await baseDao.findOne("tagwishes", { userId: toObjectId(tokenData.userId), wishId:toObjectId(params.wishId), type: WISH_TAG_TYPE.COHOST });
            if((step2.userId.toString() == tokenData.userId.toString()) || (step2.hostId && (step2.hostId.toString() == tokenData.userId.toString())) || (step3)){
                let step3;
                if(params.blessingId){
                    step3 = await blessingDaoV1.findBlessingById(params.blessingId);
                    params.blessingCreatorId = step3.userId
                    if (!step3) return Promise.reject(MESSAGES.ERROR.BLESSING_NOT_FOUND);
                }
                params.userId = tokenData.userId;
                if(params.blessingId) params["blessingDetail.blessingNumber"] = step3.blessingNumber?step3.blessingNumber:"";
                if(params.wishId) params["wishDetail.wishNumber"] = step2.wishNumber?step2.wishNumber:"";
                let obj = {};
                obj['firstName'] = step1.firstName
                obj['lastName'] = step1.lastName
                obj['profilePicture'] = step1.profilePicture
                params.userDetail = obj;
                let wellLogs;
                let data;
                let tagUser =[];

                // if (!params.location) {
                //     let userLocationData = await this.getLocation(tokenData.userId);
                //     params["location"] = userLocationData;
                // }

                let userLocationData = await this.getLocation(tokenData.userId,params);
                    params["location"] = userLocationData;

                data = await gratitudesDaoV1.saveGratitudes(params);
                //add location in location table for most compassionate
                if(params.location){
                    await commonControllerV1.saveLocation(params,GEO_LOCATION_TYPE.GRATITUDE,data._id,tokenData.userId);
                }
                if(params.gratitudeType == GRATITUDE_TYPE.GLOBAL){
                    let obj={};
                    obj['wishId']= toObjectId(params.wishId)
                    obj['gratitudeId']= toObjectId(data._id)
                    obj['userId']= toObjectId(tokenData.userId)                    
                    obj['isGratitude']= true;
                    obj['status']= STATUS.ACTIVE;
                    if(params.audio) obj['audio']= params.audio
                    if(params.notes) obj['notes']= params.notes;
                    obj['type']= params.type;
                    //get all user from the wish
                  //  if(params.gratitudeType == GRATITUDE_TYPE.GLOBAL) tagUser = await baseDao.find("tagwishes",{wishId:params.wishId},{});
                //   if(params.gratitudeType == GRATITUDE_TYPE.GLOBAL) 
                    await baseDao.save("perform_blessings", obj);
                    tagUser = await baseDao.distinct("perform_blessings","userId",{wishId:params.wishId,isGratitude:false,userId:{$ne:tokenData.userId}});
                    await userDaoV1.updateMany("users",{ "_id": { $in: tagUser } },{$inc:{globalGratRecieveCount: 1}},{});
                    if(tagUser && tagUser.length){
                        tagUser=  tagUser.map(object => {
                        return { userId: object};
                    });
                    //well section log maintain for this
                    wellLogs = await this.wellLogs(data,params,step3,tagUser,step2);
                    
                    if(wellLogs && params.gratitudeType == GRATITUDE_TYPE.GLOBAL){
                        // let blessData = await baseDao.find("tagwishes", {wishId:params.wishId, $or:[{type:"HOST"},{type:"CONTACTS"},{type:"COHOST"}], userId: { $nin: [tokenData.userId] } },{})
                        let blessData = await baseDao.find("tagwishes", {wishId:params.wishId, $or:[{type:"HOST"},{type:"COHOST"}], userId: { $nin: [tokenData.userId] } },{});
                        let arr = [...tagUser, ...blessData]; 
                        let toBeNotified = [...new Set(arr)];
                        if(toBeNotified && toBeNotified.length){
                            let pushParams = {"message":MESSAGES_NOTIFICATION.GRATITUDE_RECEIVED, "title":TITLE_NOTIFICATION.GRATITUDE_RECEIVED, "type":TYPE_NOTIFICATION.GRATITUDE_RECEIVED_GLOBAL}
                            await notificationManager.PublishNotification(toBeNotified, params.wishId, pushParams); // earlier send to blessData
                        }
                    }
                    // return blessdata;
                    }

                }else{
                //well section log maintain for this
                wellLogs = await this.wellLogs(data,params,step3,[],step2);
                if(wellLogs && params.gratitudeType == GRATITUDE_TYPE.PERSONALISED){
                    let blessData = await blessingDaoV1.findBlessingById(params.blessingId);
                    if(blessData && blessData.userId){
                        let pushParams = {"message":MESSAGES_NOTIFICATION.GRATITUDE_RECEIVED, "title":TITLE_NOTIFICATION.GRATITUDE_RECEIVED, "type":TYPE_NOTIFICATION.GRATITUDE_RECEIVED_PERSONALISED}
                        await notificationManager.PublishNotification([blessData], params.wishId, pushParams);
                    }
                }
               }
             
                return MESSAGES.SUCCESS.GRATITUDE_SUBMITTED;
                   
            }else{
                return Promise.reject(MESSAGES.ERROR.SOMETHING_WENT_WRONG);
            }
          
        } catch (error) {
            throw error;
        }
    }
      /**
         * @function wellLogs
         * @description logs maintain for well section
         *
         */
      async wellLogs(data,params,blessDetail,tagUser,wishDetail) {
        try {
           let wellLogSave={};
           let ids =[];
		   if(tagUser && tagUser.length){
			tagUser.filter(invite => {
				if(invite.userId)ids.push(invite.userId);
				//if(invite.contactId)ids.push(invite.contactId);
			});
		   }
           wellLogSave['senderId'] = params.userId;
           if(ids && ids.length && params.gratitudeType==GRATITUDE_TYPE.GLOBAL)
           {
            wellLogSave['globalGratiduteUser'] = ids;
           }else{
            if(blessDetail)wellLogSave['receiverId'] = blessDetail.userId;
           }
          
           wellLogSave['wishId'] = data.wishId?data.wishId:"";
           wellLogSave['subjectId'] = data._id; //gratitudeId
           wellLogSave['subjectDetail.audio'] = data.audio?data.audio:"";
           wellLogSave['subjectDetail.notes'] = data.notes?data.notes:"";
           wellLogSave['subjectDetail.type'] = data.type?data.type:"";
           wellLogSave['subjectDetail.audioLength'] = data.audioLength?data.audioLength:0;
           if(wishDetail.image) wellLogSave["subjectDetail.wishImage"] = wishDetail.image;
           if(blessDetail){
            wellLogSave['type'] = NOTIFICATION_TYPE.GRATITUDES;
           }else{
            wellLogSave['type'] = NOTIFICATION_TYPE.GLOBAL_GRATITUDES;
           }
           let saveData = await baseDao.save("well_logs",wellLogSave);
           return saveData;
            
        } catch (error) {
            throw error;
        }  
    }
    /**
     * @function flaggedGratitudes
     * @description admin view flagged gratitudes
     *
     */
    async flaggedGratitudes(params: GratitudesRequest.FlaggedGratitude) {
        try {
            const step1 = await gratitudesDaoV1.flaggedGratitudes(params);
			return MESSAGES.SUCCESS.FLAGGED_GRATITUDE_LIST(step1);
        } catch (error) {
            throw error;
        }
    }
    /**
     * @function flaggedGratitudeDetail
     * @description admin flagged gratitude detail
     *
     */
    async flaggedGratitudeDetail(params: GratitudesRequest.GratitudeId) {
        try {
            const step1 = await gratitudesDaoV1.findFlaggedGratitudeById(params.id, { created: 0 });
            if(!step1) {
                return Promise.reject(MESSAGES.ERROR.INVALID_FLAGGED_GRATITUDE);
            }
            const step2 = await gratitudesDaoV1.flaggedGratitudeDetail(params);
            return MESSAGES.SUCCESS.FLAGGED_GRATITUDE_DETAIL(step2);
        } catch (error) {
            throw error;
        }
    }
    /**
     * @function flaggedGratitudeReports
     * @description admin flagged gratitude reports
     *
     */
    async flaggedGratitudeReports(params: GratitudesRequest.FlaggedGratitude) {
        try {
            const step1 = await gratitudesDaoV1.findFlaggedGratitudeById(params.gratitudeId);
            if(!step1) {
                return Promise.reject(MESSAGES.ERROR.INVALID_FLAGGED_GRATITUDE);
            }
            const step2 = await gratitudesDaoV1.flaggedGratitudeReports(params);
            return MESSAGES.SUCCESS.FLAGGED_GRATITUDE_REPORT(step2);
        } catch (error) {
            throw error;
        }
    }
    /**
     * @function unflagDeleteGratitude
     * @description admin unflag/delete gratitude
     *
     */
    async unflagDeleteGratitude(params: GratitudesRequest.UnflagDeleteGratitude, tokenData: TokenData) {
        try {
            const step1 = await gratitudesDaoV1.findFlaggedGratitudeById(params.id, { created: 0 });
            const step2 = await gratitudesDaoV1.unflagDeleteGratitude(params, tokenData);
            if(params.status == FLAG_TYPE.UN_FLAGGED) {
                // await baseDao.updateMany("reports",{subjectId: toObjectId(params.id)},{'$set' : {status:STATUS.ACTIVE}},{});
                return MESSAGES.SUCCESS.UNFLAG_GRATITUDE;
            } else {
                // await commonControllerV1.saveAdminWellLogs(tokenData.userId, [step1.userId], step1, params.deleteReason, ADMIN_NOTIFICATION_TYPE.DELETE_GRATITUDE_ADMIN.TYPE);
                await notificationManager.PublishAdminNotifications([step1.userId], ADMIN_NOTIFICATION_TYPE.DELETE_GRATITUDE_ADMIN.TYPE, step1._id, params.deleteReason);
                return MESSAGES.SUCCESS.DELETE_FLAGGED_GRATITUDE;
            }
        } catch (error) {
            throw error;
        }
    }

	async getLocation(userId,params) {
		try {
		  let address;
		  let city;
		  let state;
		  let country;
		  //get the location from user
		   if(params.location){

			if (params.location.city) { address = params.location.city; }
			if (params.location.state)
			address = address + "," + params.location.state;
			city = params.location.city;
			state = params.location.state;
			country = params.location.country;

     		}else{
				const userProfileAddress = await userDaoV1.findOne("users", { _id: toObjectId(userId) }, {} );
				if (userProfileAddress.city) { 
					address = userProfileAddress.city; 
					city = userProfileAddress.city;
				}else{
					return  Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
				}
				if (userProfileAddress.state) {
					address = address + "," + userProfileAddress.state;
					state = userProfileAddress.state;
					country = userProfileAddress.country;
				}
				
		    }
	    	
		 const apiUrl = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(address)}&key=${process.env.GOOGLE_API_KEY}`;
		  return new Promise((resolve, reject) => {
			// Make the API request
			fetch(apiUrl)
			  .then((response) => response.json())
			  .then((data) => {
				// Extract latitude and longitude from the response
				
				if (data.results.length > 0) {
					if(!data.results[0].geometry.location){
						reject(MESSAGES.ERROR.LOCATION_NOT_FOUND);
						//return  Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
						
                    }
					const location = data.results[0].geometry.location;
					const latitude = location.lat;
					const longitude = location.lng;
					let retData = {
							address: city,
							coordinates: [longitude, latitude],
							city:city,
							state: state,
							country:country
					};
					resolve(retData);
			
				} else {
				 console.error("Location not found.222");
				  reject(MESSAGES.ERROR.LOCATION_NOT_FOUND);
				  //return  Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
				  
				}
			  })
			  .catch((error) => {
				console.error("Error:", error);
				reject(MESSAGES.ERROR.LOCATION_NOT_FOUND);
				
			  });
		  });
		} catch (err) {
		    return  Promise.reject(MESSAGES.ERROR.SOMETHING_WENT_WRONG);
		}
	}
	 
}

export const gratitudesController = new GratitudesController();
