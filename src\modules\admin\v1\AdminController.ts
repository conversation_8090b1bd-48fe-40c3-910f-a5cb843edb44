"use strict";

import * as _ from "lodash";
import * as crypto from "crypto";
import * as promise from "bluebird";
import { encryptHashPassword, getRandomOtp, matchPassword, genRandomString, matchOTP, toObjectId, passwordGenrator, createStream } from "@utils/appUtils";
import { MESSAGES, STATUS, USER_TYPE, TOKEN_TYPE, SERVER, DASH_EXP_TYPE , JOB_SCHEDULER_TYPE, WISH_INTESION, ADMIN_EXPORT_SHEETS, ADMIN_NOTIFICATION_TYPE, SMS_CONTENT_ADMIN, SQS_TYPES } from "@config/index";
import { adminDaoV1 } from "@modules/admin/index";
import {  userDaoV1} from "@modules/user/index";
import { loginHistoryDao } from "@modules/loginHistory/index"
import { baseDao } from "@modules/baseDao/index";
import { mailManager } from "@lib/MailManager";
import { createToken } from "@lib/tokenManager";
import { redisClient } from "@lib/redis/RedisClient";
import { notificationManager } from "@utils/NotificationManager";
import { sendMessageToFlock } from "@utils/FlockUtils";
import { commonControllerV1 } from "@modules/common/index";
import { smsManager } from "@lib/SMSManager";
import { awsSQS } from "@lib/AwsSqs";

class AdminController {
	
	async removeUserSession(params, isSingleSession: boolean) {
		try {
			if (isSingleSession)
				await loginHistoryDao.removeDeviceById({ "userId": params.userId });
			else
				await loginHistoryDao.removeDeviceById({ "userId": params.userId, "deviceId": params.deviceId });

			if (SERVER.IS_REDIS_ENABLE) {
				if (isSingleSession) {
					let keys: any = await redisClient.getKeys(`*${params.userId}*`);
					keys = keys.filter(v1 => Object.values(JOB_SCHEDULER_TYPE).findIndex(v2 => v2 === v1.split(".")[0]) === -1);
					if (keys.length) await redisClient.deleteKey(keys);
				} else
					await redisClient.deleteKey(`${params.userId}.${params.deviceId}`);
			}
		} catch (error) {
			sendMessageToFlock({ "title": "_removeSession", "error": error.stack });
		}
	};

	/**
	 * @function updateUserDataInDb
	 */
	async updateUserDataInDb(params) {
		try {
			await baseDao.updateMany("login_histories", { "userId._id": params._id }, { "$set": { userId: params } }, {});
			return {};
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function login
	 */
	async login(params: AdminRequest.Login) {
		try {
			const isDeletedEmail = await adminDaoV1.isDeletedEmail(params);
			if(isDeletedEmail) return Promise.reject(MESSAGES.ERROR.INVESTOR_DELETED_BY_ADMIN);
			const step1 = await adminDaoV1.isEmailExists(params);
			if (!step1) return Promise.reject(MESSAGES.ERROR.EMAIL_NOT_REGISTERED);
			if (step1.status === STATUS.BLOCKED) return Promise.reject(MESSAGES.ERROR.INVESTOR_DEACTIVATED_BY_ADMIN);
			const isPasswordMatched = await matchPassword(params.password, step1.hash, step1.salt);
			if (!isPasswordMatched) return Promise.reject(MESSAGES.ERROR.INCORRECT_PASSWORD);
			else {
				if((step1.userType == USER_TYPE.ADMIN && step1.isPasswordUpdated && step1.isProfileUpdated) || (step1.userType == USER_TYPE.INVESTOR && step1.isPasswordUpdated)) {
					await loginHistoryDao.removeDeviceById({ "userId": step1._id });
					const salt = crypto.randomBytes(64).toString("hex");
					let step2 = await loginHistoryDao.createUserLoginHistory({ ...params, ...step1, salt });
					const tokenData = {
						"userId": step1._id,
						"deviceId": params.deviceId,
						"accessTokenKey": salt,
						"type": TOKEN_TYPE.ADMIN_LOGIN,
						"userType": step1.userType
					};
					const accessToken = await createToken(tokenData);
					delete step1.salt; delete step1.hash; delete step1.createdAt;
					return MESSAGES.SUCCESS.LOGIN({ accessToken, ...step1 });
				} else {
					const accessToken = '';
					delete step1.salt; delete step1.hash; delete step1.createdAt;
					return MESSAGES.SUCCESS.LOGIN({ accessToken, ...step1 });
				}
			}
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function logout
	 */
	async logout(tokenData: TokenData) {
		try {
			await baseDao.updateOne("admins", { _id: tokenData.userId }, { lastSeen: Date.now() }, {});
			await loginHistoryDao.removeDeviceById(tokenData);
			return MESSAGES.SUCCESS.LOGOUT;
		} catch (error) {
			throw error;
		}
	}
	/**   
	 * @function forgotPassword    
	 */
	async forgotPassword(params: AdminRequest.ForgotPasswordRequest) {
		try {
			const isDeletedEmail = await adminDaoV1.isDeletedEmail(params);
			if(isDeletedEmail) return Promise.reject(MESSAGES.ERROR.INVESTOR_DELETED_BY_ADMIN);
			const step1 = await adminDaoV1.isEmailExists(params); // check is email exist if not then restrict to send forgot password mail
			if (!step1) return Promise.reject(MESSAGES.ERROR.EMAIL_NOT_REGISTERED);
			else if (step1.status === STATUS.BLOCKED) return Promise.reject(MESSAGES.ERROR.INVESTOR_DEACTIVATED_BY_ADMIN);
			else {
				const randomString = genRandomString(64);
				let url = `${process.env.WEB_URL}/account/reset-password/${randomString}`;
				if (SERVER.IS_REDIS_ENABLE) await redisClient.setExp(step1._id.toString(), (SERVER.TOKEN_INFO.EXPIRATION_TIME.FORGOT_PASSWORD / 1000), JSON.stringify({ "email": params.email, "token": randomString }));
				await adminDaoV1.resetPasswordToken(params, randomString);
				awsSQS.signupMagicLinkProducer({ "email": params.email, "name": `${step1.firstName} ${step1.lastName}`, "url": url , type: SQS_TYPES.FORGOT_PASSWORD});
				// mailManager.forgotPasswordMail({ "email": params.email, "name": `${step1.fullName} ${step1.lastName}`, "url": url });
				return MESSAGES.SUCCESS.MAIL_SENT;
			}
		} catch (error) {
			throw error;
		}
	}
	/**   
	 * @function updateAdminDetails    
	 */
	async updateAdminDetails(params: AdminRequest.AdminDetails) {
		try {
			const step1 = await adminDaoV1.findAdminById(params.userId);
			if(!step1) { return Promise.reject(MESSAGES.ERROR.INVALID_ADMIN); }
			const step2 = await adminDaoV1.updateAdminDetails(params);
			const step3 = await adminDaoV1.updateAdminDetailsStatus(params);
			await loginHistoryDao.removeDeviceById({ "userId": step1._id });
			const salt = crypto.randomBytes(64).toString("hex");
			step1.status = STATUS.UN_BLOCKED;
			let step4 = await loginHistoryDao.createUserLoginHistory({ ...params, ...step1, salt });
			const tokenData = {
				"userId": step1._id,
				"deviceId": params.deviceId,
				"accessTokenKey": salt,
				"type": TOKEN_TYPE.ADMIN_LOGIN,
				"userType": step1.userType
			};
			const accessToken = await createToken(tokenData);
			delete step1.salt; delete step1.hash; delete step1.createdAt;
			return MESSAGES.SUCCESS.LOGIN({ accessToken, ...step1 });
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function resetPassword
	 */
	async resetPassword(params: AdminRequest.ChangeForgotPassword) {
		try {
			const step1 = await adminDaoV1.findAdminByToken(params.token)
			if (!step1) return Promise.reject(MESSAGES.ERROR.INVALID_RESET_PASSWORD_TOKEN);
			params.hash = encryptHashPassword(params.password, step1.salt);
			await adminDaoV1.changePassword(params, step1._id);
			await  redisClient.deleteKey(step1._id.toString());
			return MESSAGES.SUCCESS.RESET_PASSWORD;
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function verifyResetPasswordToken
	 */
	async verifyResetPasswordToken(params) {
		try {
			const step1 = await adminDaoV1.findAdminByToken(params.token);
			if (!step1) return Promise.reject(MESSAGES.ERROR.INVALID_RESET_PASSWORD_TOKEN);
			const step2 = await redisClient.getValue(step1._id.toString());
			if(step2) {
				return MESSAGES.SUCCESS.DEFAULT;
			} else {
				return Promise.reject(MESSAGES.ERROR.LINK_EXPIRED);
			}
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function setNewPassword
	 */
	async setNewPassword(params: AdminRequest.SetNewPassword) {
		try {
			const step1 = await adminDaoV1.findAdminById(params.userId);
			if(!step1) { return Promise.reject(MESSAGES.ERROR.INVALID_ADMIN); }
			params.hash = encryptHashPassword(params.password, step1.salt);
			const step2 = await adminDaoV1.setNewPassword(params);
			const step3 = await adminDaoV1.updateSetPasswordStatus(params, step1.userType);
			if(step1.userType == USER_TYPE.INVESTOR) {
				await loginHistoryDao.removeDeviceById({ "userId": step1._id });
				const salt = crypto.randomBytes(64).toString("hex");
				step1.status = STATUS.UN_BLOCKED;
				let step2 = await loginHistoryDao.createUserLoginHistory({ ...params, ...step1, salt });
				const tokenData = {
					"userId": step1._id,
					"deviceId": params.deviceId,
					"accessTokenKey": salt,
					"type": TOKEN_TYPE.ADMIN_LOGIN,
					"userType": step1.userType
				};
				const accessToken = await createToken(tokenData);
				delete step1.salt; delete step1.hash; delete step1.createdAt;
				step1.isPasswordUpdated = true;
				return MESSAGES.SUCCESS.LOGIN({ accessToken, ...step1 });
			} else {
				const accessToken = '';
				delete step1.salt; delete step1.hash; delete step1.createdAt;
				step1.isPasswordUpdated = true;
				return MESSAGES.SUCCESS.SET_NEW_PASSWORD({ accessToken, ...step1 });
			}
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function changePassword
	 */
	async changePassword(params: ChangePasswordRequest, tokenData: TokenData) {
		try {
			const step1 = await adminDaoV1.findAdminById(tokenData.userId, { salt: 1, hash: 1 });
			const oldHash = encryptHashPassword(params.oldPassword, step1.salt);
			if (oldHash !== step1.hash) return Promise.reject(MESSAGES.ERROR.INVALID_OLD_PASSWORD);
			params.hash = encryptHashPassword(params.password, step1.salt);
			await adminDaoV1.changePassword(params, tokenData.userId);
			return MESSAGES.SUCCESS.CHANGE_PASSWORD;
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function adminDetails
	 */
	async adminDetails(tokenData: TokenData) {
		try {
			let admin = await adminDaoV1.findAdminById(tokenData.userId);
			delete admin.salt, delete admin.hash, delete admin.passwordResetToken;
			return MESSAGES.SUCCESS.DETAILS(admin);
		} catch (error) {
			throw error;
		}
	}
	/**    
	 * @function editProfile
	 */
	async editProfile(params: AdminRequest.EditProfile, tokenData: TokenData) {
		try {
			const isExist = await adminDaoV1.isEmailExists(params, tokenData.userId);
			if (isExist) return Promise.reject(MESSAGES.ERROR.EMAIL_ALREADY_EXIST);
			const step1 = await adminDaoV1.editProfile(params, tokenData.userId);
			this.updateUserDataInDb(step1);
			return MESSAGES.SUCCESS.EDIT_PROFILE;
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function dashboard   
	 */
	async dashboard() {
		try {
			const [users, wishes, blessing, gratitude, community, love, peace, health, abundance] = await promise.join(
				await baseDao.countDocuments("users", { "status": { "$ne": STATUS.DELETED } }),
				await baseDao.countDocuments("wishes", { "status": { "$ne": STATUS.DELETED }, "isGlobalWish": false }),
				await baseDao.countDocuments("perform_blessings", { "status": { "$ne": STATUS.DELETED }, isGratitude: false }),
				await baseDao.countDocuments("gratitudes", { "status": { "$ne": STATUS.DELETED } }),
				await baseDao.countDocuments("communities", { "status": { "$ne": STATUS.DELETED } }),
				await baseDao.countDocuments("wishes", { "status": { "$ne": STATUS.DELETED }, "isGlobalWish": false, intension: WISH_INTESION.LOVE }),
				await baseDao.countDocuments("wishes", { "status": { "$ne": STATUS.DELETED }, "isGlobalWish": false, intension: WISH_INTESION.PEACE }),
				await baseDao.countDocuments("wishes", { "status": { "$ne": STATUS.DELETED }, "isGlobalWish": false, intension: WISH_INTESION.HEALTH }),
				await baseDao.countDocuments("wishes", { "status": { "$ne": STATUS.DELETED }, "isGlobalWish": false, intension: WISH_INTESION.ABUNDANCE })
			);

			// const supporters = await baseDao.find("users", { status: { "$ne": STATUS.DELETED } }, { firstName: 1, lastName: 1, streakCount: 1, blessingCount: 1, createdAt: 1 }, {}, { blessingCount: -1, createdAt: -1 }, { pageNo: 1, limit: 10 });

			const supporters = await adminDaoV1.topSupporters();
			// console.log(supporters)

			const finalResponse = {
				counts: { users, wishes, blessing, gratitude, community, love, peace, health, abundance },
				supporters: supporters
			}
			return MESSAGES.SUCCESS.DETAILS(finalResponse);
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function dashboardExpansion   
	 */
	async dashboardExpansion(params: AdminRequest.DashboardExpansion) {
		try {
			let step1;
			if(params.type == DASH_EXP_TYPE.MONTH) {
				step1 = await adminDaoV1.dashboardExpansionMonth(params);
			} else {
				step1 = await adminDaoV1.dashboardExpansionDate(params);
			}
			return MESSAGES.SUCCESS.DETAILS(step1);
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function dashboardExpansionExport   
	 */
	async dashboardExpansionExport(params: AdminRequest.DashboardExpansionExport) {
		try {
			let sheet1_WishData, sheet1_DataFields, sheet1_FileFields, sheet1_Name;
			let sheet2_BlessingData, sheet2_DataFields, sheet2_FileFields, sheet2_Name;
			let sheet3_GratitudeData, sheet3_DataFields, sheet3_FileFields, sheet3_Name;
			let sheet4_UserData, sheet4_DataFields, sheet4_FileFields, sheet4_Name;
			let sheet5_CommunityData, sheet5_DataFields, sheet5_FileFields, sheet5_Name;
			let sheet6_ThankData, sheet6_DataFields, sheet6_FileFields, sheet6_Name;
			let sheet7_LibraryData, sheet7_DataFields, sheet7_FileFields, sheet7_Name;

			sheet1_WishData = await adminDaoV1.sheet1_WishData(params);
			sheet1_DataFields = await adminDaoV1.sheet1_WishDataMapping(sheet1_WishData);
			sheet1_FileFields = ADMIN_EXPORT_SHEETS.SHEET1_WISH;
			sheet1_Name = "WISHES";

			sheet2_BlessingData = await adminDaoV1.sheet2_BlessingData(params);
			sheet2_DataFields = await adminDaoV1.sheet2_BlessingDataMapping(sheet2_BlessingData);
			sheet2_FileFields = ADMIN_EXPORT_SHEETS.SHEET2_BLESSING;
			sheet2_Name = "BLESSINGS";

			sheet3_GratitudeData = await adminDaoV1.sheet3_GratitudeData(params);
			sheet3_DataFields = await adminDaoV1.sheet3_GratitudeDataMapping(sheet3_GratitudeData);
			sheet3_FileFields = ADMIN_EXPORT_SHEETS.SHEET3_GRATITUDE;
			sheet3_Name = "GRATITUDES";

			sheet4_UserData = await adminDaoV1.sheet4_UserData(params);
			sheet4_DataFields = await adminDaoV1.sheet4_UserDataMapping(sheet4_UserData);
			sheet4_FileFields = ADMIN_EXPORT_SHEETS.SHEET4_USER;
			sheet4_Name = "USERS";

			sheet5_CommunityData = await adminDaoV1.sheet5_CommunityData(params);
			sheet5_DataFields = await adminDaoV1.sheet5_CommunityDataMapping(sheet5_CommunityData);
			sheet5_FileFields = ADMIN_EXPORT_SHEETS.SHEET5_COMMUNITY;
			sheet5_Name = "COMMUNITIES";

			sheet6_ThankData = await adminDaoV1.sheet6_ThankData(params);
			sheet6_DataFields = await adminDaoV1.sheet6_ThankDataMapping(sheet6_ThankData);
			sheet6_FileFields = ADMIN_EXPORT_SHEETS.SHEET6_THANK;
			sheet6_Name = "THANK WW";

			sheet7_LibraryData = await adminDaoV1.sheet7_LibraryData(params);
			sheet7_DataFields = await adminDaoV1.sheet7_LibraryDataMapping(sheet7_LibraryData);
			sheet7_FileFields = ADMIN_EXPORT_SHEETS.SHEET7_LIBRARY;
			sheet7_Name = "BLESSING LIBRARY";

			const sheets_DataToExport = await createStream(
				{
					sheet1_DataFields,
					sheet2_DataFields,
					sheet3_DataFields,
					sheet4_DataFields,
					sheet5_DataFields,
					sheet6_DataFields,
					sheet7_DataFields
				},
				{
					sheet1_FileFields,
					sheet2_FileFields,
					sheet3_FileFields,
					sheet4_FileFields,
					sheet5_FileFields,
					sheet6_FileFields,
					sheet7_FileFields
				},
				{
					sheet1_Name,
					sheet2_Name,
					sheet3_Name,
					sheet4_Name,
					sheet5_Name,
					sheet6_Name,
					sheet7_Name
				}
			);
			return MESSAGES.SUCCESS.EXPORTED(sheets_DataToExport);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function blessingListing   
	 */
	async blessingListing(params: AdminRequest.BlessingList) {
		try {
			const step1 = await adminDaoV1.blessingListing(params);
			return MESSAGES.SUCCESS.BLESSING_LIST(step1);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function createBlessing   
	 */
	async createBlessing(params: AdminRequest.CreateBlessing) {
		try {
			const step1 = await adminDaoV1.createBlessing(params);
			return MESSAGES.SUCCESS.CREATE_BLESSING(step1);
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function createWish   
	 */
	async createWish(params: AdminRequest.CreateWish, tokenData: TokenData) {
		try {
			const step1 = adminDaoV1.createWish(params, tokenData);
			return MESSAGES.SUCCESS.CREATE_WISH(step1);
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function wishListing   
	 */
	async wishListing(params: AdminRequest.WishList) {
		try {
			const step1 = await adminDaoV1.wishListing(params);
			return MESSAGES.SUCCESS.WISH_LIST(step1);
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function wishDetails   
	 */
	async wishDetails(params: AdminRequest.WishDetail) {
		try {
			const step1 = await adminDaoV1.wishDetailsAgg(params);
			return MESSAGES.SUCCESS.WISH_DETAIL(step1);
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function editWish   
	 */
	async editWish(params: AdminRequest.WishEdit) {
		try {
			const step1 = await adminDaoV1.wishDetails(params);
			if(!step1) {
				return Promise.reject(MESSAGES.ERROR.INVALID_WISH_ID);
			}
			const step2 = await adminDaoV1.editWish(params);
			return MESSAGES.SUCCESS.EDIT_GLOBAL_WISH;
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function deleteWish   
	 */
	async deleteWish(params: AdminRequest.WishDetail) {
		try {
			const step1 = await adminDaoV1.wishDetails(params);
			if(!step1) {
				return Promise.reject(MESSAGES.ERROR.INVALID_WISH_ID);
			}
			if(step1.status !== STATUS.UN_PUBLISHED) {
				return Promise.reject(MESSAGES.ERROR.DELETE_WISH_NOT_ALLOWED);
			}
			const step2 = await adminDaoV1.deleteWish(params);
			// DELETE GLOBAL WISH FROM PINNED & FAVOURITE
			const step3 = await baseDao.deleteMany("pinned_wishes", { subjectId: params.id });
			const step4 = await baseDao.deleteMany("favourites", { wishId: params.id });
			return MESSAGES.SUCCESS.DELETE_GLOBAL_WISH;
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function flaggedWishes   
	 */
	async flaggedWishes(params: AdminRequest.FlaggedWish) {
		try {
			const step1 = await adminDaoV1.flaggedWishes(params);
			return MESSAGES.SUCCESS.FLAGGED_WISH_LIST(step1);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function unflagWish   
	 */
	async unflagWish(params: AdminRequest.WishDetail, tokenData: TokenData) {
		try {
			const step1 = await adminDaoV1.wishDetails(params);
			if(!step1) {
				return Promise.reject(MESSAGES.ERROR.INVALID_WISH_ID);
			}
			const step2 = await adminDaoV1.unflagWish(params, tokenData);
			// await baseDao.updateMany("reports",{subjectId: toObjectId(params.id)},{'$set' : {status:STATUS.ACTIVE}},{});
			// const step3 = await adminDaoV1.unflagTagWishes(params);
			return MESSAGES.SUCCESS.UNFLAG_WISH;
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function deleteFlaggedWish   
	 */
	async deleteFlaggedWish(params: AdminRequest.WishDetail, tokenData: TokenData) {
		try {
			const step1 = await adminDaoV1.wishDetails(params);
			if(!step1) {
				return Promise.reject(MESSAGES.ERROR.INVALID_WISH_ID);
			}
			const step2 = await adminDaoV1.deleteFlaggedWish(params);
			const newStep1 = await adminDaoV1.deleteTaggedWish(params);
			const step3 = await baseDao.findOne("users", { _id: step1.userId }, { wishCount: 1 });
			if(step3 && step3.wishCount > 0) {
				await baseDao.updateOne("users", { _id: step1.userId }, { $inc: { wishCount: -1 } }, {});
			}
			const userIds = [];
			userIds.push(step1.userId);
			if(step1.hostId) {
				userIds.push(step1.hostId);
			}
			// console.log(tokenData.userId,'========================================tokenData.userId')
			// console.log(userIds,'========================================userIds')
			// console.log(step1,'========================================step1')
			// console.log(params.deleteReason,'========================================params.deleteReason')
			// await commonControllerV1.saveAdminWellLogs(tokenData.userId, userIds, step1, params.deleteReason, ADMIN_NOTIFICATION_TYPE.DELETE_WISH_ADMIN.TYPE);
			// console.log(userIds,'================================userIds')
			await notificationManager.PublishAdminNotifications(userIds, ADMIN_NOTIFICATION_TYPE.DELETE_WISH_ADMIN.TYPE, step1._id, params.deleteReason);
			return MESSAGES.SUCCESS.DELETE_FLAGGED_WISH;
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function flaggedWishReviews   
	 */
	async flaggedWishReports(params: AdminRequest.FlaggedWish) {
		try {
			const step1 = await adminDaoV1.flaggedWishReports(params);
			return MESSAGES.SUCCESS.UNFLAG_WISH_REPORTS(step1);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function userListing   
	 */
	async userListing(params: AdminRequest.UserList) {
		try {
			const step1 = await userDaoV1.userListing(params);
			return MESSAGES.SUCCESS.USER_LIST(step1);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function userDetails   
	 */
	async userDetails(params: AdminRequest.UserDetail) {
		try {
			// const step1 = await userDaoV1.findUserById(params.id);
			// if(!step1) {
			// 	return Promise.reject(MESSAGES.ERROR.INVALID_USER_ID);
			// }
			const step2 = await userDaoV1.userDetails(params);
			return MESSAGES.SUCCESS.USER_DETAILS(step2);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function editUser   
	 */
	async editUser(params: AdminRequest.EditUser, tokenData: TokenData) {
		try {
			const step1 = await userDaoV1.findUserById(params.id);
			if(!step1) {
				return Promise.reject(MESSAGES.ERROR.INVALID_USER_ID);
			}
			const step2 = await userDaoV1.editUser(params, tokenData);
			if (params.status == STATUS.BLOCKED) {
				await this.removeUserSession({ userId: params.id }, true);
				await commonControllerV1.userDeactivateScript(step1);
				// await smsManager.sendMessageViaAWS(step1.countryCode, step1.phoneNumber, SMS_CONTENT_ADMIN.DEACTIVATE_USER(params.reason));
				await awsSQS.signupMagicLinkProducer({"countryCode":step1.countryCode, "phoneNumber":step1.phoneNumber, "SMS_CONTENT":SMS_CONTENT_ADMIN.DEACTIVATE_USER(params.reason), "type":SQS_TYPES.EDIT_USER})

				return MESSAGES.SUCCESS.DEACTIVATE_USER;
			} else if (params.status == STATUS.UN_BLOCKED) {
				await commonControllerV1.userActivateScript(step1);
				// await smsManager.sendMessageViaAWS(step1.countryCode, step1.phoneNumber, SMS_CONTENT_ADMIN.REACTIVATE_USER);
				await awsSQS.signupMagicLinkProducer({"countryCode":step1.countryCode, "phoneNumber":step1.phoneNumber, "SMS_CONTENT":SMS_CONTENT_ADMIN.REACTIVATE_USER, "type":SQS_TYPES.EDIT_USER})

				return MESSAGES.SUCCESS.ACTIVATE_USER;
			} else {
				await this.removeUserSession({ userId: params.id }, true);
				await commonControllerV1.deleteAccountScript(step1,USER_TYPE.ADMIN);
				// await smsManager.sendMessageViaAWS(step1.countryCode, step1.phoneNumber, SMS_CONTENT_ADMIN.DELETE_USER(params.reason));
				await awsSQS.signupMagicLinkProducer({"countryCode":step1.countryCode, "phoneNumber":step1.phoneNumber, "SMS_CONTENT":SMS_CONTENT_ADMIN.DELETE_USER(params.reason), "type":SQS_TYPES.EDIT_USER})

				return MESSAGES.SUCCESS.DELETE_USER;
			}
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function investorListing
	 */
	async investorListing(params: AdminRequest.InvestorList, tokenData: TokenData) {
		try {
			const step1 = await adminDaoV1.investorListing(params, tokenData);
			return MESSAGES.SUCCESS.INVESTOR_LIST(step1);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function createInvestor
	 */
	async createInvestor(params: AdminRequest.CreateInvestor) {
		try {
			const step1 = await adminDaoV1.isEmailExists(params);
			if(step1) {
				return Promise.reject(MESSAGES.ERROR.EMAIL_ALREADY_EXIST);
			}
			const randomString = passwordGenrator(9);
			const step2 = await adminDaoV1.createInvestor(params, randomString);
			// mailManager.investorInvitationMail({ "name": `${params.firstName} ${params.lastName}`, "email": params.email, "randomString": randomString });
			awsSQS.signupMagicLinkProducer({  "name": `${params.firstName} ${params.lastName}`, "email": params.email, "randomString": randomString , type: SQS_TYPES.INVESTOR_CREATED});

			return MESSAGES.SUCCESS.CREATE_INVESTOR;
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function investorDetails
	 */
	async investorDetails(params: AdminRequest.UserDetail) {
		try {
			const project = { _id: 1, userType: 1, status: 1, email: 1, firstName: 1, lastName: 1, profilePicture: 1, contactNo: 1, company: 1, jobTitle: 1, createdAt: 1, lastSeen: 1 }
			const step1 = await adminDaoV1.findAdminById(params.id, project);
			if(!step1) {
				return Promise.reject(MESSAGES.ERROR.INVALID_ADMIN);
			}
			return MESSAGES.SUCCESS.INVESTOR_DETAIL(step1);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function editInvestor
	 */
	async editInvestor(params: AdminRequest.EditInvestor) {
		try {
			const step1 = await adminDaoV1.findAdminById(params.id);
			if(!step1) {
				return Promise.reject(MESSAGES.ERROR.INVALID_ADMIN);
			}
			const step2 = await adminDaoV1.isEmailExists(params, step1._id);
			if(step2) {
				return Promise.reject(MESSAGES.ERROR.EMAIL_ALREADY_EXIST);
			}
			const step3 = await adminDaoV1.editInvestor(params);
			return MESSAGES.SUCCESS.INVESTOR_EDIT;
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function editInvestorStatus
	 */
	async editInvestorStatus(params: AdminRequest.DeleteInvestor) {
		try {
			const step1 = await adminDaoV1.findAdminById(params.id);
			if(!step1) {
				return Promise.reject(MESSAGES.ERROR.INVALID_ADMIN);
			}
			if(params.status == STATUS.UN_BLOCKED) {
				const step2 = await adminDaoV1.editInvestorStatus(params);
				return MESSAGES.SUCCESS.INVESTOR_UNBLOCKED;
			} else if (params.status == STATUS.BLOCKED) {
				const step2 = await adminDaoV1.editInvestorStatus(params);
				return MESSAGES.SUCCESS.INVESTOR_BLOCKED;
			} else {
				if(step1.status == STATUS.BLOCKED) {
					const step2 = await adminDaoV1.editInvestorStatus(params);
					const updatedEmail = step1.email + "_deleted-" + new Date().getTime();
					await baseDao.updateOne("admins", { _id: step1._id }, { email: updatedEmail }, {});
					return MESSAGES.SUCCESS.INVESTOR_DELETED;
				} else {
					return Promise.reject(MESSAGES.ERROR.CANNOT_DELETE_INVESTOR);
				}
			}
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function userList
	 */
	async userList(params: AdminRequest.UserListing) {
		try {
			const data = await userDaoV1.userList(params);
			if (params.latestUsers) {
				const query: any = {};
				query.status = { "$ne": STATUS.DELETED };
				query.name = { $exists: true, $ne: null }
				query.createdAt = { "$gte": new Date(new Date().setHours(0, 0, 0, 0)), "$lt": new Date(new Date().setHours(23, 59, 59, 999)) };
				data.total = await baseDao.countDocuments("users", query);
			}
			return MESSAGES.SUCCESS.LIST(data);
		} catch (error) {
			throw error;
		}
	}
}
export const adminController = new AdminController();