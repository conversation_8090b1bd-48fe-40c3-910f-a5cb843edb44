# Wishwell
This is a Node.js project built with Hapi and TypeScript.

## Description
An app that harnesses the power of collective support and goodwill to create a more compassionate world.

## Tech Stack

*Technologies Used:* Backend is built using a variety of technologies to provide a seamless user experience, including:

*Server Side:* Nodejs 16, Hapi.js framework, redis and typescript 4.x.

*Database:* MongoDB. A NoSQL document-oriented database.

## Prerequisites
* Node.js (version 16.19.0)
* npm (version 9.19.3)
* TypeScript (version 4.9.3)
* Docker

## Installation

#### Follow the steps below to set up and run the project using a Docker container.

* Clone the repository:
```bash
git clone https://bitbucket.org/tal-in04/backend.git
```

* Install the TypeScript globally:
```bash
sudo npm install -U typescript@4.9.3 -g
```

* Install Docker on your machine. Refer to the Docker documentation for installation instructions specific to your operating system.

* Build the project using the Docker container:
```bash
sudo docker build -t wish . --no-cache
```
* Run the Docker container:
```bash
sudo docker run -p 3011:3010 -d wish
# Note: Adjust the port number according to your setup if necessary
```

* Check the running containers
```bash
sudo docker ps
```

* View the logs of the container
```bash
sudo docker logs CONTAINER_ID
```

#### Follow the steps below to set up and run the project without using a Docker container.

1. npm install
2. tsc
3. NODE_ENV=development pm2 start ./build/server.js --name=wishwell-dev

### Basic Setup Start ###
* npm install -g typescript
* npm install --save-dev ts-node
* npm install --save-dev typescript
* npm install --save-dev ts-node-dev
* npm install --save-dev @types/node OR npm install -D @types/node
* npm install --save-dev tsconfig-paths
* npm i googleapis@legacy-8

### Install Mocha globally ###
npm install --global mocha

# Locally in your project. 
npm install -D ts-node
npm install -D typescript

# Or globally. 
npm install -g ts-node

### install git on ubuntu ###
* sudo apt-get install git

### install node on ubuntu ###
* curl -o- https://raw.githubusercontent.com/creationix/nvm/v0.33.6/install.sh | bash
* . ~/.nvm/nvm.sh
* nvm install 16.19.1

### install mongoDB 5.0.14 on ubuntu ###
* echo "deb [ arch=amd64,arm64 ] http://repo.mongodb.org/apt/ubuntu "$(lsb_release -sc)"/mongodb-org/5.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-5.0.list
* sudo apt-get update
* sudo apt-get install mongodb-org
* sudo service mongod start

### install mongoDB 5.0.14 on ubuntu ###
* echo "deb [ arch=amd64,arm64 ] http://repo.mongodb.org/apt/ubuntu "$(lsb_release -sc)"/mongodb-org/5.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-5.0.list
* sudo apt-get update
* sudo apt-get install mongodb-org=5.0.14 mongodb-org-server=5.0.14 mongodb-org-shell=5.0.14 mongodb-org-mongos=5.0.14 mongodb-org-tools=5.0.14
* sudo service mongod start

### install redis on ubuntu v16.04 ###
* sudo apt-get update
* sudo apt-get install redis-server
* redis-server
* redis-cli
* ping

### install pm2 & bower on ubuntu ###
* npm install -g pm2 bower

### Basic Setup End ###

### run build on development server ###
* tsc && NODE_ENV=development pm2 start ./build/server.js --name wishwell-dev

### run build on staging server ###
* tsc && NODE_ENV=qa pm2 start ./build/server.js --name wishwell-qa

### run build on staging server ###
* tsc && NODE_ENV=staging pm2 start ./build/server.js --name wishwell-staging

### run build on production server ###
* tsc && NODE_ENV=prod pm2 start ./build/server.js --name wishwell-prod

### localhost swagger URL ###
* http://localhost:3007/documentation

### development swagger URL ###
* https://api.wishwelldev.com:8000/documentation

### qa swagger URL ###
* --- WIP ---

### staging swagger URL ###
* --- WIP ---

### production swagger URL ###
* --- WIP ---

### configure db authentication ###
db.createUser(
   {
     user: "wishwell",
     pwd: "wishwell",
     "roles" : [ 
        {
            "role" : "readWrite",
            "db" : "wishwell"
        }
    ]
   }
)
