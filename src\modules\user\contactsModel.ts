"use strict";
const mongoose = require("mongoose");
import { Document, model, Model, Schema } from "mongoose";
import {
	DB_MODEL_REF,
	STATUS,
} from "@config/index";
export interface IContact extends Document {   
	name?: string;
	firstName?: string;
	lastName?: string;
	email: string;
	isAppUser: boolean;
	profilePicture?: string;
	countryCode?: string;   
	phoneNumber?: string;
	deviceId?:string;
	status: string;
	created: number;
	platform: string;
}
const contactSchema: Schema = new mongoose.Schema({
	_id: { type: Schema.Types.ObjectId, required: true, auto: true },
	userId: { type: Schema.Types.ObjectId, required: true },
	name: { type: String, trim: true, required: false }, 
	firstName: { type: String, trim: true, required: false }, 
	lastName: { type: String, trim: true, required: false }, 
	email: { type: String, trim: true, required: true }, 
	isAppUser: { type: Boolean, default: true }, 
	profilePicture: { type: String, required: false }, 
	countryCode: { type: String, required: false },
	phoneNumber: { type: String, required: false },
	deviceId: { type: String, required: false },
	contactUserId: { type: Schema.Types.ObjectId, required: false },
	status: {
		type: String,
		enum: [STATUS.BLOCKED, STATUS.UN_BLOCKED, STATUS.DELETED],
		default: STATUS.UN_BLOCKED,
		required: true
	},

	platform:{ type: String, required: false },
	created: { type: Number, default: Date.now }
}, {
	versionKey: false,
	timestamps: true
});
contactSchema.index({ name: 1 });
contactSchema.index({ email: 1 });
contactSchema.index({ phoneNumber: 1, deviceId:1},{unique:true});
contactSchema.index({ status: 1 });
contactSchema.index({ created: -1 });
// Export user
export const contacts: Model<IContact> = model<IContact>(DB_MODEL_REF.CONTACT, contactSchema);