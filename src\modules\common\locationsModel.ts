"use strict";
const mongoose = require("mongoose");
import { Document, model, Model, Schema } from "mongoose";
import {
	DB_MODEL_REF,
    GEO_LOCATION_TYPE
} from "@config/index";
export interface Ilocation extends Document {
	userId?: string;
	stripeCutomerId: string;
	amount: string;
	amount_capturable: string;
	client_secret: string;
	capture_method: string;
	currency: string;
	status: string;
	type: string;
	cityCount : Number;
}
const geoSchema: Schema = new mongoose.Schema({
	type: { type: String, default: "Point" },
	address: { type: String, required: false },
	coordinates: { type: [Number], index: "2dsphere", required: false }, // [longitude, latitude]
	city: { type: String, required: false },
	state: { type: String, required: false },
	country: { type: String, required: false }
}, {
	_id: false
});
const locationsSchema: Schema = new mongoose.Schema({
	_id: { type: Schema.Types.ObjectId, required: true, auto: true },
	// subjectId: {type: Schema.Types.ObjectId, required: true},
    type: {
        type: String,
        required: true,
        enum: [GEO_LOCATION_TYPE.WISH, GEO_LOCATION_TYPE.BLESSING, GEO_LOCATION_TYPE.GRATITUDE]
    },
    location: geoSchema,
	cityCount:{ type: Number, false: false, default: 0 },
	// userId: { type: Schema.Types.ObjectId, required: true },
	created: { type: Number, default: Date.now }
}, {
	versionKey: false,
	timestamps: true
});
// this table is used for most used location with date filter

const mostUsedLocationsSchema: Schema = new mongoose.Schema({
	_id: { type: Schema.Types.ObjectId, required: true, auto: true },
	subjectId: {type: Schema.Types.ObjectId},
    type: {
        type: String,
        required: true,
        enum: [GEO_LOCATION_TYPE.WISH, GEO_LOCATION_TYPE.BLESSING, GEO_LOCATION_TYPE.GRATITUDE]
    },
    location: geoSchema,
	cityCount:{ type: Number, required: false, default: 0 },
	userId: { type: Schema.Types.ObjectId, required: true },
	created: { type: Number, default: Date.now }
}, {
	versionKey: false,
	timestamps: true
});



locationsSchema.index({ type: 1 });
mostUsedLocationsSchema.index({ userId: 1 });
// Export user
export const locations: Model<Ilocation> = model<Ilocation>(DB_MODEL_REF.LOCATIONS, locationsSchema);
export const most_used_locations: Model<Ilocation> = model<Ilocation>(DB_MODEL_REF.MOST_USED_LOCATIONS, mostUsedLocationsSchema);