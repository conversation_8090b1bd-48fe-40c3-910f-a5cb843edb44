"use strict";
import { BaseDao, baseDao } from "@modules/baseDao/BaseDao";
import { escapeSpecial<PERSON>haracter, toObjectId } from "@utils/appUtils";
import {
	THANK_WISHWELL_LISTING_TYPE,
	THANK_WISHWELL_SORT_CRITERIA,
	DB_MODEL_REF,
	PAYMENT_STATUS_RESPONSE
} from "@config/constant";
export class CommonDao extends BaseDao {
	/**
	 * @function countryList
	 */
	async countryList(params: Dropdown) {
		try {
			const aggPipe = [];
			const match: any = {};
			if (params.status) match.status = params.status;
			if (params.searchKey) {
				params.searchKey = escapeSpecialCharacter(params.searchKey);
				match.name = { "$regex": params.searchKey, "$options": "i" };
			}
			if (Object.keys(match).length) aggPipe.push({ "$match": match });
			aggPipe.push({ "$project": { _id:0,id:1, name: 1, phone_code: 1,iso2:1 } });
			let sort = {};
			(params.sortBy && params.sortOrder) ? sort = { [params.sortBy]: params.sortOrder } : sort = { name: 1 };
			aggPipe.push({ "$sort": sort });
			const options = { collation: true };
			return await this.aggregate("countries", aggPipe, {});
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function stateList
	 */
	async stateList(params: Dropdown) {
		try {
			const aggPipe = [];
			const match: any = {};
			match.country_id = params.countryId;
			if (params.searchKey) {
				params.searchKey = escapeSpecialCharacter(params.searchKey);
				match.name = { "$regex": params.searchKey, "$options": "i" };
			}
			if (Object.keys(match).length) aggPipe.push({ "$match": match });
			aggPipe.push({ "$project": { _id:0,id:1, name: 1, country_id: 1,country_code:1,state_code:1 } });
			let sort = {};
			(params.sortBy && params.sortOrder) ? sort = { [params.sortBy]: params.sortOrder } : sort = { name: 1 };
			aggPipe.push({ "$sort": sort });
			const options = { collation: true };
			return await this.aggregate("states", aggPipe, {});
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function cityList
	 */
	async cityList(params: Dropdown) {
		try {
			const aggPipe = [];
			const match: any = {};
			match.country_id = params.countryId;
			match.state_id = params.stateId;
			if (params.searchKey) {
				params.searchKey = escapeSpecialCharacter(params.searchKey);
				match.name = { "$regex": params.searchKey, "$options": "i" };
			}
			if (Object.keys(match).length) aggPipe.push({ "$match": match });
			aggPipe.push({ "$project": { _id:0,id:1, name: 1, country_id: 1,state_id:1} });
			let sort = {};
			(params.sortBy && params.sortOrder) ? sort = { [params.sortBy]: params.sortOrder } : sort = { name: 1 };
			aggPipe.push({ "$sort": sort });
			const options = { collation: true };
			return await this.aggregate("cities", aggPipe, {});
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function wishwellThanksSave
	 */
	async wishwellThanksSave(params: UserRequest.WishwellThanks,tokenData,paymentData) {
		try {
			params['userId'] = tokenData.userId;
			if(paymentData)
			   params['paymentStatus']  = paymentData.status;
			   params['intentId'] = params.intentId;

			return await this.save("wishwell_thanks", params, {});
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function thankWishwellListing
	 */
	async thankWishwellListing(params: UserRequest.ThankWishwellListing) {
		try {
			let match: any = {};
			let aggPipe: any = [];
			let sort: any = {};

			// match["paymentStatus"] = { $ne: PAYMENT_STATUS_RESPONSE.REQUIRES_PAYMENT_METHOD };

			if(params.type == THANK_WISHWELL_LISTING_TYPE.USER) {
				match["userId"] = toObjectId(params.userId);
			}

			const userLookup = {
				from: DB_MODEL_REF.USER,
				localField: "userId",
				foreignField: "_id",
				as: "userDetails"
			};

			if(params.searchKey) {
				params.searchKey = escapeSpecialCharacter(params.searchKey);
				match = {
					...match,
					$expr: {
						$or: [
							{
								$regexMatch: {
									input: { $concat: ['$userDetails.firstName', ' ', '$userDetails.lastName'] },
									regex: params.searchKey,
									options: 'i'
								}
							},
							{
                                $regexMatch: {
                                    input: { $toString: "$serialNumber" },
                                    regex: params.searchKey,
                                    options: 'i'
                                }
                            },
							{
                                $regexMatch: {
                                    input: "$userDetails.location.address",
                                    regex: params.searchKey,
                                    options: 'i'
                                }
                            },
							{
                                $regexMatch: {
                                    input: { $toString: "$amount" },
                                    regex: params.searchKey,
                                    options: 'i'
                                }
                            }
						]
					}
				}
			}

			aggPipe.push({ "$lookup": userLookup });
			aggPipe.push({ "$unwind": "$userDetails" });
			aggPipe.push({ "$match": match });

			const project = {
				_id: 1,
				serialNumber: 1,
				type: 1,
				note: 1,
				notes: "$note",
				audio: 1,
				isAllowShare: 1,
				amount: 1,
				status: 1,
				createdAt: 1,
				"userDetails._id": 1,
				"userDetails.firstName": 1,
				"userDetails.lastName": 1,
				"userDetails.profilePicture": 1,
				"userDetails.location": 1,
				"userDetails.country": 1,
				"userDetails.state": 1,
				"userDetails.city": 1,
				fullName: {
					$concat: [
						{ $ifNull: ['$userDetails.firstName', ''] },
						' ',
						{ $ifNull: ['$userDetails.lastName', ''] }
					]
				},
				isAddressExists: {
					$cond: {
						if: { $ifNull: ["$userDetails.location.address", false] },
						then: true,
						else: false
					}
				}
			}

			aggPipe.push({ "$project": project });

			const addFields = {
				"userDetails.location.address": {
					$cond: {
						if: { $eq: ["$isAddressExists", true] },
						then: "$userDetails.location.address",
						else: { $concat: ["$userDetails.city", ", ", "$userDetails.state", ", ", "$userDetails.country"] }
					}
				}
			};

			aggPipe.push({ "$addFields": addFields });

			if (params.sortBy == 1) {
				params.sortBy = 1;
			} else {
				params.sortBy = -1;
			}

			if(params.sortCriteria == THANK_WISHWELL_SORT_CRITERIA.SERIAL_NUMBER) {
				sort = { "serialNumber": params.sortBy }
			} else if (params.sortCriteria == THANK_WISHWELL_SORT_CRITERIA.NOTE_OWNER) {
                sort = { "fullName": params.sortBy }
			} else if (params.sortCriteria == THANK_WISHWELL_SORT_CRITERIA.LOCATION) {
				sort = { "userDetails.location.address": params.sortBy }
			} else if (params.sortCriteria == THANK_WISHWELL_SORT_CRITERIA.DONATION) {
                sort = { "amount": params.sortBy }
			} else if (params.sortCriteria == THANK_WISHWELL_SORT_CRITERIA.CONSENT) {
				sort = { "isAllowShare": params.sortBy }
			} else {
				sort = { "createdAt": params.sortBy }
			}

			aggPipe.push({ "$sort": sort });
			const options = { collation: true };
			return this.paginate("wishwell_thanks", aggPipe, params.limit, params.pageNo, options, true);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function createPaymentIntent
	 * @description for thanks wishwell create payment intent
	 */
		async createPaymentIntent(paymentdata) {
			try {
				let saveData = paymentdata;
				saveData['amount'] = paymentdata.amount/100;
				return await this.save("payment_intents", saveData);
			} catch (error) {
				throw error;
			}
		}

	/**
	  * @function mostUsedLocation
	  */
		async mostUsedLocation(params) {
			try {
				
				const aggPipe = [];
				const match: any = {};
				match['location.city'] = { $exists: true };
				aggPipe.push({ "$match": match });
				if (params.startDate) match.createdAt = { "$gte": params.startDate };
				
				aggPipe.push(
					{
						$group: {
						  _id: "$location.city",
						  count: { $sum: 1 }
						}
					  }
					);
				aggPipe.push({ "$sort": { count: -1}});	
				aggPipe.push({ "$limit": 1});	
				const options = {};
				let data = await this.aggregate("most_used_locations", aggPipe,options);
				// let data = await baseDao.find("locations", {}, {}, {sort:{"cityCount":-1}, limit: 1})
				return data;
			} catch (error) {
				throw error;
			}
		}	

}

export const commonDao = new CommonDao();