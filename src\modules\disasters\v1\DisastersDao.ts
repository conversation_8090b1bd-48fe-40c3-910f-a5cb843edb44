"use strict";

import { baseDao, BaseDao } from "@modules/baseDao";
import { escapeSpecialCharacter } from "@utils/appUtils";

export class DisastersDao extends BaseDao {
    /**
	 * @function userSearch
	 */
	async categoriesSearch(params: ListingRequest) {
		try {
			const aggPipe = [];
			const match: any = {};
            if(!params.limit) params.limit = 10;
            if(!params.pageNo) params.pageNo = 1;
			if (params.searchKey) {
                params.searchKey = escapeSpecialCharacter(params.searchKey);
                match["name"] = { "$regex": params.searchKey, "$options": "i" };
            }
			aggPipe.push({ "$match": match });
			// aggPipe.push({ "$sort": { createdAt: -1 } });
			aggPipe.push({"$project":{_id:1,disasterCategory:1,name:1}})
			const options = { collation: true };
			let response = await this.paginate("disaster_categories", aggPipe, params.limit, params.pageNo, options, false);
			let counData = await baseDao.countDocuments("disaster_categories",match)
			response.total = counData.length;
			return response;
		} catch (error) {
			throw error;
		}
	}
}

export const disastersDao = new DisastersDao();