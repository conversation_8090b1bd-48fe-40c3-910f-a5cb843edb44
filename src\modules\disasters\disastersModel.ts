"use strict";

const mongoose = require("mongoose");
import { Document, model, Model, Schema } from "mongoose";
import { DB_MODEL_REF } from "@config/constant";

export interface Disasters extends Document {
    disasterCategory: number;
    disasterYear: string;
    disasterFromDate: Date;
    disasterToDate: Date;
    country: string;
    state: string;
    city: string;
}

const disastersSchema: Schema = new mongoose.Schema({
    _id: {
        type: Schema.Types.ObjectId,
        required: true,
        auto: true,
    },
    disasterCategory: {
        type: Number,
        required: true
    },
    disasterYear: {
        type: String,
        required: true,
    },
    disasterFromDate: {
        type: Date,
        required: true
    },
    disasterToDate: {
        type: Date,
        required: true
    },
    country: {
        type: String,
        required: true,
    },
    state: {
        type: String,
        required: true,
    },
    city: {
        type: String,
        required: true,
    },
}, {
    versionKey: false,
    timestamps: true
});

// Export categories
export const disasters: Model<Disasters> = model<Disasters>(DB_MODEL_REF.DISASTER, disastersSchema);