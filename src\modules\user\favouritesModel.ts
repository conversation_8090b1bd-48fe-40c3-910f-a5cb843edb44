"use strict";

const mongoose = require("mongoose");
import { Document, model, Model, Schema } from "mongoose";

import {
	DB_MODEL_REF, STATUS
} from "@config/index";

export interface IFavourite extends Document {
	userId: string;
	wishId: boolean;
    created: number;
	status: string;

}
const favouriteSchema: Schema = new mongoose.Schema({
	_id: { type: Schema.Types.ObjectId, required: true, auto: true },
    userId: { type: Schema.Types.ObjectId, required: true },
	wishId: { type: Schema.Types.ObjectId, required: true },
	status: {
        type: String,
        enum: [STATUS.ACTIVE, STATUS.IN_ACTIVE,STATUS.DELETED,STATUS.REPORTED],
        default: STATUS.ACTIVE
    },
	created: { type: Number, default: Date.now }
}, {
	versionKey: false,
	timestamps: true
});

favouriteSchema.index({ created: -1 });

// Export user
export const favourites: Model<IFavourite> = model<IFavourite>(DB_MODEL_REF.FAVOURITES, favouriteSchema);