"use strict";

const mongoose = require("mongoose");
import { Document, model, Model, Schema } from "mongoose";

import { DB_MODEL_REF } from "@config/constant";

export interface IAdminBanner extends Document {
	
	message: string;
	status: string;
	created: number;
    title: string;
    userId: string;

}

const adminBannerSchema: Schema = new mongoose.Schema({
    _id: { type: Schema.Types.ObjectId, required: true, auto: true },
    userId: {
		type: Schema.Types.ObjectId,
		ref: DB_MODEL_REF.USER,
		required: false
	},
	title: { type: String, required: true },
	message: { type: String, required: false },
	// status: {
	// 	type: String,
	// 	required: true,
	// 	enum: [STATUS.SEND.TYPE,STATUS.SCHEDULE.TYPE,STATUS.DRAFT.TYPE,]
	// },
	created: { type: Number, default: Date.now }
}, {
	versionKey: false,
	timestamps: true
});

adminBannerSchema.index({ userId: 1 });
adminBannerSchema.index({ title: 1 });
adminBannerSchema.index({ platform: 1 });

// Export notification schema
export const admin_banners: Model<IAdminBanner> = model<IAdminBanner>(DB_MODEL_REF.ADMIN_BANNER, adminBannerSchema);