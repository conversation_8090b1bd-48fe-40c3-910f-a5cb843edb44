"use strict";
const mongoose = require("mongoose");
import { Document, model, Model, Schema } from "mongoose";
import { DB_MODEL_REF, STATUS, WISH_TAG_TYPE,WISH_TYPE } from "@config/constant";
export interface TagWishes extends Document {
    wishId: string;
    userId: string;
    communityId:string;
    type:string;
    countryCode: string;
	phoneNumber: string;
    status: string;
    createdBy:string 
    created: number;
}

const tagWishesSchema: Schema = new mongoose.Schema({
    _id: { type: Schema.Types.ObjectId, required: true, auto: true },
    wishId: { type: Schema.Types.ObjectId, required: true },
    userId: { type: Schema.Types.ObjectId, required: true },
    communityId: { type: Schema.Types.ObjectId, required: false },
    wishType: { type: String, enum: [WISH_TYPE.OTHER, WISH_TYPE.MYSELF], },
    type: {
        type: String,
        enum: [WISH_TAG_TYPE.CONTACTS, WISH_TAG_TYPE.COHOST,WISH_TAG_TYPE.HOST,WISH_TAG_TYPE.INVITED,WISH_TAG_TYPE.INVITED_HOST],
        default: WISH_TAG_TYPE.CONTACTS,
        
    },
    countryCode: { type: String, required: false },
	phoneNumber: { type: String, required: false },
    status: {
        type: String,
        enum: [STATUS.ACTIVE,STATUS.PENDING, STATUS.IN_ACTIVE, STATUS.DELETED, STATUS.BLOCKED],
        default: STATUS.ACTIVE
    },
    oldStatus: {
        type: String,
        enum: [STATUS.ACTIVE, STATUS.IN_ACTIVE],
        default: STATUS.ACTIVE
    },
    lastBlessTime: { type: Date, required: false },
    contactId: { type: Schema.Types.ObjectId, required: false },
    created: { type: Number, default: Date.now },

    isDuplicate:{ type: Boolean, required: false, default: false }, 
    isDuplicateExist:{ type: Boolean, required: false, default: false }, 
    communityIdList: {type: Array, required: false},
}, {
    versionKey: false,
    timestamps: true
});

tagWishesSchema.index({ userId: 1 });
tagWishesSchema.index({ status: 1 });
tagWishesSchema.index({ created: -1 });

// Export categories
export const tagwishes: Model<TagWishes> = model<TagWishes>(DB_MODEL_REF.TAGWISHES, tagWishesSchema);