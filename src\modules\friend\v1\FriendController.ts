"use strict";

import { 
    FRIEND_REQUEST_STATUS, 
    MESSAGES, 
    MESSAGES_NOTIFICATION, 
    TITLE_NOTIFICATION, 
    TYPE_NOTIFICATION 
} from "@config/constant";
import { friendDaoV1 } from "@modules/friend/index";
import { userDaoV1 } from "@modules/user/index";
import { notificationManager } from "@utils/NotificationManager";
import { toObjectId } from "@utils/appUtils";

export class FriendController {
    /**
     * @function sendFriendRequest
     * @description send friend request to another user
     */
    async sendFriendRequest(params: UserRequest.FriendRequest, tokenData: TokenData) {
        try {
            // Check if user exists
            const user = await userDaoV1.findUserById(tokenData.userId);
            if (!user) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
            
            // Check if friend exists
            const friend = await userDaoV1.findUserById(params.userId);
            if (!friend) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
            
            // Check if already friends or request pending
            const existingRequest = await friendDaoV1.isAlreadyAFriend(params, tokenData.userId);
            if (existingRequest) {
                return Promise.reject(MESSAGES.ERROR.FRIEND_REQUEST_ALREADY_SENT);
            }
            
            // Create friend request
            params.status = FRIEND_REQUEST_STATUS.REQUEST_PENDING;
            params.friendId = { 
                _id: params.userId,
                name: friend.firstName + (friend.lastName ? ' ' + friend.lastName : ''),
                profilePicture: friend.profilePicture,
                userType: friend.userType || 'USER'
            };
            params.userId = tokenData.userId;
            
            const result = await friendDaoV1.addFriend(params);
            
            // Send notification to the friend
            const pushArray = [{
                userId: params.userId,
                deviceToken: friend.deviceToken
            }];
            
            const pushParams = {
                "message": MESSAGES_NOTIFICATION.FRIEND_REQUEST.replace('[NAME]', user.name || ''),
                "title": TITLE_NOTIFICATION.FRIEND_REQUEST,
                "type": TYPE_NOTIFICATION.FRIEND_REQUEST
            };
            
            await notificationManager.PublishNotification(pushArray, result._id, pushParams);
            
            return result;
        } catch (error) {
            throw error;
        }
    }
    
    /**
     * @function respondToFriendRequest
     * @description accept or reject a friend request
     */
    async respondToFriendRequest(params: UserRequest.FriendResponse, tokenData: TokenData) {
        try {
            // Check if user exists
            const user = await userDaoV1.findUserById(tokenData.userId);
            if (!user) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
            
            // Check if request exists
            const request = await friendDaoV1.findFriendRequestById(params.requestId);
            if (!request) return Promise.reject(MESSAGES.ERROR.FRIEND_REQUEST_NOT_FOUND);
            
            // Verify the request is for this user
            if (request.friendId._id.toString() !== tokenData.userId) {
                return Promise.reject(MESSAGES.ERROR.UNAUTHORIZED_ACCESS);
            }
            
            // Update request status
            request.status = params.accept ? 
                FRIEND_REQUEST_STATUS.REQUEST_ACCEPTED : 
                FRIEND_REQUEST_STATUS.REQUEST_DECLINED;
                
            const result = await friendDaoV1.updateFriendRequest(request);
            
            // If accepted, send notification to the requester
            if (params.accept) {
                const requester = await userDaoV1.findUserById(request.userId);
                if (requester && requester.deviceToken) {
                    const pushArray = [{
                        userId: request.userId,
                        deviceToken: requester.deviceToken
                    }];
                    
                    const pushParams = {
                        "message": MESSAGES_NOTIFICATION.FRIEND_REQUEST_ACCEPTED.replace('[NAME]', user.name || ''),
                        "title": TITLE_NOTIFICATION.FRIEND_REQUEST_ACCEPTED,
                        "type": TYPE_NOTIFICATION.FRIEND_REQUEST_ACCEPTED
                    };
                    
                    await notificationManager.PublishNotification(pushArray, result._id, pushParams);
                }
            }
            
            return result;
        } catch (error) {
            throw error;
        }
    }
    
    /**
     * @function getFriendRequests
     * @description get list of pending friend requests
     */
    async getFriendRequests(params: ListingRequest, tokenData: TokenData) {
        try {
            const user = await userDaoV1.findUserById(tokenData.userId);
            if (!user) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
            
            const requests = await friendDaoV1.getPendingFriendRequests(tokenData.userId, params);
            return MESSAGES.SUCCESS.LIST(requests);
        } catch (error) {
            throw error;
        }
    }
    
    /**
     * @function getFriendsList
     * @description get list of friends
     */
    async getFriendsList(params: ListingRequest, tokenData: TokenData) {
        try {
            const user = await userDaoV1.findUserById(tokenData.userId);
            if (!user) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
            
            const friends = await friendDaoV1.getFriendsList(tokenData.userId, params);
            return MESSAGES.SUCCESS.LIST(friends);
        } catch (error) {
            throw error;
        }
    }

    /**
     * @function removeFriend
     * @description remove a user from friends list
     */
    async removeFriend(params: UserRequest.RemoveFriend, tokenData: TokenData) {
        try {
            // Check if user exists
            const user = await userDaoV1.findUserById(tokenData.userId);
            if (!user) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
            
            // Check if friend exists
            const friend = await userDaoV1.findUserById(params.friendId);
            if (!friend) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
            
            // Check if they are actually friends
            const friendshipQuery = {
                "$or": [
                    { "$and": [{ "userId": tokenData.userId }, { "friendId._id": params.friendId }, { "status": FRIEND_REQUEST_STATUS.REQUEST_ACCEPTED }] },
                    { "$and": [{ "userId": params.friendId }, { "friendId._id": tokenData.userId }, { "status": FRIEND_REQUEST_STATUS.REQUEST_ACCEPTED }] }
                ]
            };
            
            const friendship = await friendDaoV1.findOne("friends", friendshipQuery, {});
            if (!friendship) {
                return Promise.reject(MESSAGES.ERROR.NOT_FRIENDS);
            }
            
            // Update the friendship status to removed
            const result = await friendDaoV1.updateOne(
                "friends", 
                { _id: friendship._id }, 
                { "$set": { status: FRIEND_REQUEST_STATUS.UN_FRIEND } }, 
                {}
            );
            
            return MESSAGES.SUCCESS.FRIEND_REMOVED;
        } catch (error) {
            throw error;
        }
    }
}

export const friendController = new FriendController();



