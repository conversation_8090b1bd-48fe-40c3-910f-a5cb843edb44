"use strict";
const mongoose = require("mongoose");
import { Document, model, Model, Schema } from "mongoose";
import { DB_MODEL_REF, STATUS, BLESSING_CREATED_BY, BLESSING_TYPE } from "@config/constant";
export interface BlessingLibrary extends Document {
    name: string;
    blessingType: string;
    language: string;
    audioFile:string;
    voiceover:string;
    isGlobalBlessing:boolean;
    type:string;
    status: string;
    createdBy:string
    created: number;
}
const blessingLibrarySchema: Schema = new mongoose.Schema({
    _id: { type: Schema.Types.ObjectId, required: true, auto: true },
    image: { type: String, required: true },
    name: { type: String, required: true },
    blessingType: {
        type: String,
        enum: [BLESSING_TYPE.MEDITATION, BLESSING_TYPE.PRAYER],
        required: true
    },
    language: { type: String, required: true },
    audioFile: { type: String, required: true },
    voiceover: { type: String, required: true },
    intension: { type: String, required: true },
    authorName: { type: String, required: true },
    // isGlobalBlessing: { type: Boolean, default: false },
    // type: {
    //     type: String,
    //     enum: [BLESSING_CREATED_BY.ADMIN, BLESSING_CREATED_BY.USER],
    //     default: BLESSING_CREATED_BY.USER
    // },
    status: {
        type: String,
        enum: [STATUS.UN_PUBLISHED, STATUS.IN_ACTIVE, STATUS.ACTIVE, STATUS.DELETED],
        default: STATUS.UN_PUBLISHED
    },
    created: { type: Number, default: Date.now }
}, {
    versionKey: false,
    timestamps: true
});
blessingLibrarySchema.index({ userId: 1 });
blessingLibrarySchema.index({ status: 1 });
blessingLibrarySchema.index({ created: -1 });

// Export blessing library
export const blessing_library: Model<BlessingLibrary> = model<BlessingLibrary>(DB_MODEL_REF.BLESSING_LIBRARY, blessingLibrarySchema);