"use strict";

const mongoose = require("mongoose");
import { Document, model, Model, Schema } from "mongoose";
import { DB_MODEL_REF, STATUS, BLESSING_PERFORM_TYPE, FLAG_TYPE,GRATITUDE_TYPE } from "@config/constant";
import { users } from "@modules/models";
export interface Gratitudes extends Document {
    gratitudeNumber: number;
    blessingId: string;
    wishId: string;
    blessingDetail:object;
    userId: string;
    blessingCreatorId: string;
    listenGratitudeId: string;
    listenAudio: string;
    listenDuration?: string;
    audio?: string;
    audioLength?:string;
    notes: string;
    userDetail;
    status: string;
    gratitudeType:string;
    createdBy: string
    created: number;
}
const userDetailSchema = new Schema({
	profilePicture: { type: String, required: false, default: "" },
	firstName: { type: String, required: false },
	lastName: { type: String, required: false },
	_id: false
})
const unflagHistorySchema = new Schema({
    adminId: { type: Schema.Types.ObjectId, required: true },
    _id: false
}, {
    versionKey: false,
    timestamps: true
});
const geoSchema: Schema = new mongoose.Schema({
	type: { type: String, default: "Point" },
	address: { type: String, required: false },
	coordinates: { type: [Number], index: "2dsphere", required: false }, // [longitude, latitude]
    city: { type: String, required: false },
    country: { type: String, required: false },
    state: { type: String, required: false }
}, {
	_id: false
});
const gratitudesSchema: Schema = new mongoose.Schema({
    _id: { type: Schema.Types.ObjectId, required: true, auto: true },
    gratitudeNumber: { type: Number, required: false },
    blessingId: { type: Schema.Types.ObjectId, required: false },
    wishId: { type: Schema.Types.ObjectId, required: true },
    blessingDetail:{
        blessingNumber: { type: String, required: false },
    },
    wishDetail:{
        wishNumber: { type: String, required: false },
    },
    userId: { type: Schema.Types.ObjectId, required: true },
    userDetail:userDetailSchema, 
    blessingCreatorId:{ type: Schema.Types.ObjectId, required: false },
    audio: { type: String, required: false },
    audioLength:{type:Number,required:false},
    notes: { type: String, required: false },
    type: {
        type: String,
        enum: [BLESSING_PERFORM_TYPE.AUDIO, BLESSING_PERFORM_TYPE.TEXT],
        default: BLESSING_PERFORM_TYPE.TEXT,
    },
    gratitudeType:{
        type: String,
        enum: [GRATITUDE_TYPE.GLOBAL, GRATITUDE_TYPE.PERSONALISED],
        default: GRATITUDE_TYPE.GLOBAL,
    },
    status: {
        type: String,
        enum: [STATUS.ACTIVE, STATUS.IN_ACTIVE, STATUS.DELETED],
        default: STATUS.ACTIVE
    },
    isFlagged: { type: Boolean, required: false, default: false }, // for admin (if admin unflags the wish from admin panel)
    flagStatus: {
        type: String,
        required: false,
        enum: [FLAG_TYPE.FLAGGED, FLAG_TYPE.UN_FLAGGED, STATUS.ACTIVE, STATUS.DELETED],
        default: STATUS.ACTIVE
    }, // for admin (status to show in admin flagged wishes module)
    unflagHistory: [unflagHistorySchema],
    reportCount: { type: Number, required: true, default: 0 },
    deleteReason: { type: String, required: false },
    deletedAt: { type: Date, required: false },
    location: geoSchema,
    created: { type: Number, default: Date.now }
}, {
    versionKey: false,
    timestamps: true
});
gratitudesSchema.pre<Gratitudes>('save', async function (next) {
    if (this.isNew) {
        const lastEntry = await gratitudes.findOne({}, {}, { sort: { createdAt: -1 } });
        const lastKeyValue = lastEntry ? (lastEntry.gratitudeNumber == undefined) ? 0 : lastEntry.gratitudeNumber : 0;
        this.gratitudeNumber = lastKeyValue + 1;
    }
    next();
});
gratitudesSchema.post<Gratitudes>('save', async function (doc) {
    const updateGratitudeCount = await users.updateOne({ _id: doc.userId }, { $inc: { gratitudeCount: 1 } }, {});
});

gratitudesSchema.index({ userId: 1 });
gratitudesSchema.index({ status: 1 });
gratitudesSchema.index({ created: -1 });


// Export categories
export const gratitudes: Model<Gratitudes> = model<Gratitudes>(DB_MODEL_REF.GRATITUDES, gratitudesSchema);