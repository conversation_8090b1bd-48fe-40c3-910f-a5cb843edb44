version: 0.2
  
phases:
  install:
    runtime-versions:
      docker: 20

  pre_build:
    commands:
      # Installing dependencies
      - yum install -y jq
      # Preparing Amazon ECR service
      - echo Logging in to Amazon ECR...
      - aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com

  build:
    commands:
      # Preparing .env file.
      - |
        secret_value=$(aws secretsmanager get-secret-value --secret-id "$ENVIRONMENT/wishwell/secrets" --output text --query "SecretString" --region $AWS_DEFAULT_REGION)
        echo "$secret_value" | jq -r 'to_entries[] | "\(.key)=\"\(.value)\""' > $ENV_FILE_NAME
      - cat $ENV_FILE_NAME
      # Building backend image
      - echo Building backend image...
      - docker build -t $ENVIRONMENT-ww-$AWS_DEFAULT_REGION-ecr-backend:latest -t $ENVIRONMENT-ww-$AWS_DEFAULT_REGION-ecr-backend:$CODEBUILD_BUILD_NUMBER-$CODEBUILD_RESOLVED_SOURCE_VERSION -f Dockerfile.$ENVIRONMENT .
      # Tagging the image with the latest tag and with the CodeBuild number + Commit ID
      - docker tag $ENVIRONMENT-ww-$AWS_DEFAULT_REGION-ecr-backend:latest $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$ENVIRONMENT-ww-$AWS_DEFAULT_REGION-ecr-backend:latest
      - docker tag $ENVIRONMENT-ww-$AWS_DEFAULT_REGION-ecr-backend:$CODEBUILD_BUILD_NUMBER-$CODEBUILD_RESOLVED_SOURCE_VERSION $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$ENVIRONMENT-ww-$AWS_DEFAULT_REGION-ecr-backend:$CODEBUILD_BUILD_NUMBER-$CODEBUILD_RESOLVED_SOURCE_VERSION

  post_build:
    commands: 
      # Pushing image to ECR repository
      - echo Pushing docassemble-app image...
      - docker push --all-tags $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$ENVIRONMENT-ww-$AWS_DEFAULT_REGION-ecr-backend
      # List all images in the ECR repository
      - echo Listing images in ECR repository...
      - images=$(aws ecr describe-images --repository-name $ENVIRONMENT-ww-$AWS_DEFAULT_REGION-ecr-backend --query 'reverse(sort_by(imageDetails,& imagePushedAt))[:]' --output json)    
      # Extract the image digest for the last 5 images
      - last_5_images=$(echo "$images" | jq -r '.[].imageDigest' | head -n 5)    
      # Iterate over the images and delete those that are not in the last 5
      - echo Deleting old images from ECR repository...
      - |
        for image in $(echo "$images" | jq -r '.[].imageDigest'); do
          if [[ ! "$last_5_images" =~ "$image" ]]; then
            aws ecr batch-delete-image --repository-name $ENVIRONMENT-ww-$AWS_DEFAULT_REGION-ecr-backend --image-ids imageDigest=$image
          fi
        done
     # #- aws codebuild start-build --project-name $ENVIRONMENT-ww-ca-central-1-codb-be-cd
     # - aws codebuild start-build --project-name $ENVIRONMENT-ww-ca-central-1-codb-be-cd
      - curl -LO https://storage.googleapis.com/kubernetes-release/release/v1.16.0/bin/linux/amd64/kubectl
      - chmod +x ./kubectl
      - export PATH=$PWD/:$PATH
      - yum install -y jq
      - aws sts get-caller-identity  --output text
      - aws configure set aws_access_key_id $ID
      - aws configure set aws_secret_access_key $KEY
      - aws eks update-kubeconfig --name $EKS_CLUSTER_NAME 
      - kubectl get nodes
      - kubectl rollout restart deployment $deployment -n $ns