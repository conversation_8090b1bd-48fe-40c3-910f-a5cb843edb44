"use strict";
const mongoose = require("mongoose");
import { Document, model, Model, Schema } from "mongoose";
import { users } from "@modules/models";
import { DB_MODEL_REF, STATUS, WISH_CREATED_BY,WISH_TYPE, FLAG_TYPE } from "@config/constant";
export interface Wish<PERSON> extends Document {
    wishNumber: number;
    title: string;
    description: string;
    isGlobalWish: boolean;
    intension:string;  
    userId:string;
    hostId:string;
    type:string;   
    status: string;
    userDetail;
    image: string;
    createdBy:string
    rejectReason:string;
    created: number;
}
const userDetailSchema = new Schema({
	profilePicture: { type: String, required: false, default: "" },
	firstName: { type: String, required: false },
	lastName: { type: String, required: false },
	_id: false
})
const unflagHistorySchema = new Schema({
    adminId: { type: Schema.Types.ObjectId, required: true },
    _id: false
}, {
    versionKey: false,
    timestamps: true
});
const geoSchema: Schema = new mongoose.Schema({
	type: { type: String, default: "Point" },
	address: { type: String, required: false },
	coordinates: { type: [Number], index: "2dsphere", required: false }, // [longitude, latitude]
    city: { type: String, required: false },
    country: { type: String, required: false },
    state: { type: String, required: false }
}, {
	_id: false
});
const wishesSchema: Schema = new mongoose.Schema({
    _id: { type: Schema.Types.ObjectId, required: true, auto: true },
    wishNumber: { type: Number, required: false },
    title: { type: String, required: false },
    description: { type: String, required: false },
    isGlobalWish: { type: Boolean, default: false },
    visibility: { type: String, enum: ["PUBLIC", "PRIVATE"], default: "PUBLIC" },
    intension: { type: String, required: true },
    userId: { type: Schema.Types.ObjectId, required: true },
    hostId: { type: Schema.Types.ObjectId, required: false },
    communityId: [{ type: Schema.Types.ObjectId, required: false }],
    wishType: { type: String, enum: [WISH_TYPE.OTHER, WISH_TYPE.MYSELF], },
    totalBlessings: { type: Number, required: false, default: 0 },
    blessingTime: { type: String, required: false, default: "" },
    userDetail:userDetailSchema, 
    image: { type: String, required: false, default: "" },
    isFlagged: { type: Boolean, required: false, default: false }, // for admin (if admin unflags the wish from admin panel)
    flagStatus: {
        type: String,
        required: false,
        enum: [FLAG_TYPE.FLAGGED, FLAG_TYPE.UN_FLAGGED, STATUS.ACTIVE],
        default: STATUS.ACTIVE
    }, // for admin (status to show in admin flagged wishes module)
    unflagHistory: [unflagHistorySchema],
    reportCount: { type: Number, required: true, default: 0 },
    createdBy: {
        type: String,
        enum: [WISH_CREATED_BY.USER, WISH_CREATED_BY.ADMIN],
        default: WISH_CREATED_BY.USER
    },
    status: {
        type: String,
        enum: [STATUS.ACTIVE, STATUS.IN_ACTIVE, STATUS.UN_PUBLISHED,STATUS.DELETED, STATUS.PENDING, STATUS.CLOSED, STATUS.BLOCKED],
        default: STATUS.ACTIVE
    },
    oldStatus: {
        type: String,
        enum: [STATUS.ACTIVE, STATUS.IN_ACTIVE, STATUS.PENDING, STATUS.CLOSED],
        default: STATUS.ACTIVE
    },
    rejectReason: { type: String, required: false },
    declineReason: { type: String, required: false },
    declinedAt: { type: Date, required: false },
    deleteReason: { type: String, required: false },
    deletedAt: { type: Date, required: false },
    hostLastBlessTime: { type: Date, required: false },
    userLastBlessTime: { type: Date, required: false },  //creator
    userRemovedWish: { type: Array, required: false ,default: [] },
    isGlobalPinned: { type: Boolean, required: false, default: false }, // for admin (if admin pin/unpin global wish)
    location: geoSchema,
    disasterCategory: { type: Number, required: false },
    familyName: { type: String, required: false },
    closeDate: { type: Date, required: false },
    created: { type: Number, default: Date.now },
}, {
    versionKey: false,
    timestamps: true
});

wishesSchema.index({ userId: 1 });
wishesSchema.index({ status: 1 });
wishesSchema.index({ created: -1 });
wishesSchema.pre<Wishes>('save', async function (next) {
    if (this.isNew) {
        const lastEntry = await wishes.findOne({}, {}, { sort: { createdAt: -1 } });
        const lastKeyValue = lastEntry ? (lastEntry.wishNumber == undefined) ? 0 : lastEntry.wishNumber : 0;
        this.wishNumber = lastKeyValue + 1;
    }
    next();
});
wishesSchema.post<Wishes>('save', async function (doc) {
    if(doc.isGlobalWish == false) {
        const updateWishCount = await users.updateOne({ _id: doc.userId }, { $inc: { wishCount: 1 } }, {});
    }
});

// Export wishes
export const wishes: Model<Wishes> = model<Wishes>(DB_MODEL_REF.WISHES, wishesSchema);