"use strict";

const mongoose = require("mongoose");
import { Document, model, Model, Schema } from "mongoose";

import { DB_MODEL_REF } from "@config/constant";

export interface IRoles extends Document {
	
	roles: string;
	status: string;
	created: number;
    userId: string;

}

const rolesSchema: Schema = new mongoose.Schema({
    _id: { type: Schema.Types.ObjectId, required: true, auto: true },
    userId: {
		type: Schema.Types.ObjectId,
		ref: DB_MODEL_REF.USER,
		required: false
	},
	roles: { type: String, required: true },
	// status: {
	// 	type: String,
	// 	required: true,
	// 	enum: [STATUS.SEND.TYPE,STATUS.SCHEDULE.TYPE,STATUS.DRAFT.TYPE,]
	// },
	created: { type: Number, default: Date.now }
}, {
	versionKey: false,
	timestamps: true
});

rolesSchema.index({ userId: 1 });
rolesSchema.index({ roles: 1 });

// Export notification schema
export const roles: Model<IRoles> = model<IRoles>(DB_MODEL_REF.ROLE, rolesSchema);