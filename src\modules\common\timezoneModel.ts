"use strict";
const mongoose = require("mongoose");
import { Document, model, Model, Schema } from "mongoose";
import {
	DB_MODEL_REF
} from "@config/index";
export interface Itimezone extends Document {
	userId?: string;
	offset: number;
	date: string;
	startDate: string;
	endDate: string;
	offsetStartDate: string;
	offsetEndDate: string;
}

const timezoneSchema: Schema = new mongoose.Schema({
	_id: { type: Schema.Types.ObjectId, required: false, auto: true },
	userId: { type: Schema.Types.ObjectId, required: false },
	offset: { type: Number, required: false },
	date: { type: String, required: false },
	localDate: { type: String, required: false },
	startDate: { type: String, required: false },
	endDate: { type: String, required: false },
	offsetStartDate: { type: String, required: false },
	offsetEndDate: { type: String, required: false },
	created: { type: Number, default: Date.now }
}, {
	versionKey: false,
	timestamps: true
});

timezoneSchema.index({ userId: 1 });
// Export timezone
export const timezones: Model<Itimezone> = model<Itimezone>(DB_MODEL_REF.TIMEZONE, timezoneSchema);