"use strict";
const util = require('util');

import { DEEPLINK_TYPE, IMESSAGE_TEXT, MESSAGES_NOTIFICATION, STATUS, TITLE_NOTIFICATION, TYPE_NOTIFICATION } from "@config/constant";
import { SERVER } from "@config/environment";
import { baseDao } from "@modules/baseDao";
import { userDaoV1 } from "@modules/user";
import { notificationManager } from "@utils/NotificationManager";

export class WishesReminderController {

    // Array to store phone numbers of non-users
    _nonUsersData = [];

    /**
     * @function checkNotification
     * @description checking the status of reminders and sending them accordingly
     */
    async checkNotification() {
        try {

            console.log('ENTERED FUNCTION');

            this._nonUsersData = [];

            // const findAsync = util.promisify(userDaoV1.find.bind(userDaoV1));
            const findAsync = util.promisify(baseDao.find.bind(baseDao));

            const currentDate = new Date();

            const fiveDaysAgo = new Date();
            fiveDaysAgo.setDate(fiveDaysAgo.getDate() - 5);

            const tagwishes = await findAsync('tagwishes', 
            {
                "createdAt": { $gte: fiveDaysAgo }, 
                status: 'ACTIVE',
                $or: [
                    { userId: { $exists: false } },
                    { userId: null }
                ]
                // $or: [
                //     { "notificationRemindersStatus.reminder24Hours": false },
                //     { "notificationRemindersStatus.reminder72Hours": false },
                //     { "notificationRemindersStatus.reminder120Hours": false }
                // ]
            }); 

            const members = await findAsync('members', {
                "createdAt": { $gte: fiveDaysAgo }, 
                $or: [
                    { "userId": { $exists: false } },
                    { "userId": null },
                ]
            });

            console.log(`tagwishes results:\n\n ${tagwishes}`);
            console.log(`tagwishes results:\n\n ${members}`);

            if(!tagwishes && !members) {
                console.log('error getting tagwishes and members');
                return 'error getting tagwishes and members';
            }

            if(tagwishes && tagwishes.length === 0 && members && members.length === 0) {
                console.log('Tagwish and Members length = 0');
                return "No wishes or community invites to non-users within last 5 days";
            }

            if(!tagwishes) {
                console.log('error getting tagwishes');
            }

            // console.log(`tagwishes results:\n\n ${tagwishes}`);
            if(tagwishes.length === 0) {
                console.log('Tagwish length = 0');
                // return "No wishes within last 5 days";
            }

            if(!members) {
                console.log('error getting members');
            }

            if(members.length === 0) {
                console.log('Members length = 0');
                // return "No wishes within last 5 days";
            }

            /* 
                looping through the tagwishes results to find out if any of who the wishes are sent to aren't app users
                and storing their info into the nonUsersData array if so.
            */
            for(const tagwish of tagwishes) {
                const { countryCode, phoneNumber, wishId, communityIdList, wishType } = tagwish;

                // search for users in the 'users' collection
                const user = await baseDao.findOne('users', { countryCode, phoneNumber });
                const wish = await baseDao.findOne('wishes', { "_id": wishId  });
                const contact = await baseDao.findOne('contacts', { phoneNumber });

                if(contact) {
                    console.log('entered contact if');
                    console.log(`contact = ${contact.phoneNumber}`);
                }

                const notificatonReminders = {
                    reminder24Hours: wish.notificationReminder24Hours,
                    reminder72Hours: wish.notificationReminder72Hours,
                    reminder120Hours: wish.notificationReminder120Hours,
                }

                const notificatonRemindersStatus = {
                    reminder24Hours: wish.notificationReminder24HoursStatus,
                    reminder72Hours: wish.notificationReminder72HoursStatus,
                    reminder120Hours: wish.notificationReminder120HoursStatus,
                }

                // console.log(`notificationReminders: \n\n ${notificatonReminders}`);
                // console.log(`notificationRemindersStatus: \n\n ${notificatonRemindersStatus}`);

                // let community, communityName: null | String;
                let community, communityNames = [];

                if(tagwish.communityIdList && tagwish.communityIdList.length > 0) {
                    // community = await userDaoV1.findOne('communities', { "_id": tagwish.communityIdList[0] });
                    for(const item of tagwish.communityIdList) {
                        community = await baseDao.findOne('communities', { "_id": item });
                        if(community) {
                            communityNames.push(community.name);
                        }
                    }
                }

                // if(community) {
                //     communityName = community.name;
                // }

                // console.log(`wish hostId: ${wish.hostId}`);

                // if no user is found and the contact info isn't already in the array, add contact info to array
                if(!user && !this._doesExistInNonUsers(countryCode, phoneNumber)) {

                    // /* TEMPORARY CODE FOR TESTING PURPOSES */
                    // this._nonUsersData.push({
                    //     countryCode,
                    //     phoneNumber,
                    //     "fullPhoneNumber": countryCode+phoneNumber,
                    //     wishId: wish._id,
                    //     wishType,
                    //     userId: wish.userId._id,
                    //     communityIdList,
                    //     communityName,
                    //     notificatonReminders,
                    //     notificatonRemindersStatus,
                    //     // isActiveUser: false,
                    // });
                    // /* ENDS HERE */ 


                    // checking the reminders status
                    if(notificatonRemindersStatus && !notificatonRemindersStatus.reminder24Hours) {
                        console.log("FIRST IF ENTER 24");
                        if(notificatonReminders && notificatonReminders.reminder24Hours instanceof Date) {
                            console.log("SECOND IF ENTER 24");
                            const reminderDate = new Date(notificatonReminders.reminder24Hours);
                            console.log(`REMINDER DATE: ${reminderDate}`);
                            // Comparing the current date with the reminder date
                            if(currentDate > reminderDate) {
                                // adding contact info to array
                                console.log('THIRD IF ENTER 24')                
                                this._nonUsersData.push({
                                    countryCode,
                                    phoneNumber,
                                    fullPhoneNumber: countryCode+phoneNumber,
                                    isCommunityInvite: false,
                                    wishId: wish._id,
                                    wishType,
                                    userId: wish.userId._id,
                                    communityIdList,
                                    communityNames,
                                    notificatonReminders,
                                    notificatonRemindersStatus,
                                    contactName: contact.name ? contact.name : "",
                                });
                                // updating the status of the reminder in the collection
                                const updateResult = await baseDao.updateOne(
                                    'wishes', 
                                    { _id: wish._id }, 
                                    { $set: { 'notificationReminder24HoursStatus': true } },
                                    { /* options */ }
                                    );
                                
                                if(updateResult) {
                                    console.log('Update Successful');
                                } else {
                                    console.log('Document not found or not updated');
                                }
                            }
                        }
                    } else if(notificatonRemindersStatus && !notificatonRemindersStatus.reminder72Hours) {
                        console.log("FIRST IF ENTER 72");
                        if(notificatonReminders && notificatonReminders.reminder72Hours instanceof Date) {
                            console.log("SECOND IF ENTER 72");
                            const reminderDate = new Date(notificatonReminders.reminder72Hours);
                            // Comparing the current date with the reminder date
                            if(currentDate > reminderDate) {
                                console.log("THIRD IF ENTER 72");
                                // adding contact info to array
                                this._nonUsersData.push({
                                    countryCode,
                                    phoneNumber,
                                    fullPhoneNumber: countryCode+phoneNumber,
                                    isCommunityInvite: false,
                                    wishId: wish._id,
                                    wishType,
                                    userId: wish.userId._id,
                                    communityIdList,
                                    communityNames,
                                    notificatonReminders,
                                    notificatonRemindersStatus,
                                    contactName: contact.name ? contact.name : "",
                                });
                                console.log(`wishId = ${wish._id}`);
                                // updating the status of the reminder in the collection
                                const updateResult = await baseDao.updateOne(
                                    'wishes', 
                                    { _id: wish._id }, 
                                    { $set: { 'notificationReminder72HoursStatus': true } },
                                    { /* options */ }
                                    );
                                
                                if(updateResult) {
                                    console.log('Update Successful');
                                } else {
                                    console.log('Document not found or not updated');
                                }
                            }
                        }
                    } else if(notificatonRemindersStatus && !notificatonRemindersStatus.reminder120Hours) {
                        console.log("FIRST IF ENTER 120");
                        if(notificatonReminders && notificatonReminders.reminder120Hours instanceof Date) {
                            console.log("SECOND IF ENTER 120");
                            const reminderDate = new Date(notificatonReminders.reminder120Hours);
                            // Comparing the current date with the reminder date
                            if(currentDate > reminderDate) {
                                console.log("THIRD IF ENTER 120");
                                // adding contact info to array
                                this._nonUsersData.push({
                                    countryCode,
                                    phoneNumber,
                                    fullPhoneNumber: countryCode+phoneNumber,
                                    isCommunityInvite: false,
                                    wishId: wish._id,
                                    wishType,
                                    userId: wish.userId._id,
                                    communityIdList,
                                    communityNames,
                                    notificatonReminders,
                                    notificatonRemindersStatus,
                                    contactName: contact.name ? contact.name : "",
                                });
                                console.log(`wishId = ${wish._id}`);
                                // updating the status of the reminder in the collection
                                const updateResult = await baseDao.updateOne(
                                    'wishes', 
                                    { _id: wish._id }, 
                                    { $set: { 'notificationReminder120HoursStatus': true } },
                                    { /* options */ }
                                    );
                                
                                if(updateResult) {
                                    console.log('Update Successful');
                                } else {
                                    console.log('Document not found or not updated');
                                }
                            }
                        }
                    } 
                }
            }

            for(const member of members) {
                const { countryCode, phoneNumber, communityId } = member;

                // search for users in the 'users' collection
                const user = await baseDao.findOne('users', { countryCode, phoneNumber });
                const community = await baseDao.findOne('communities', { "_id": communityId  });
                const contact = await baseDao.findOne('contacts', { phoneNumber });

                if(contact) {
                    console.log('entered contact if');
                    console.log(`contact = ${contact.phoneNumber}`);
                }

                const notificatonReminders = {
                    reminder24Hours: member.notificationReminder24Hours,
                    reminder72Hours: member.notificationReminder72Hours,
                    reminder120Hours: member.notificationReminder120Hours,
                }

                const notificatonRemindersStatus = {
                    reminder24Hours: member.notificationReminder24HoursStatus,
                    reminder72Hours: member.notificationReminder72HoursStatus,
                    reminder120Hours: member.notificationReminder120HoursStatus,
                }

                // console.log(`notificationReminders: \n\n ${notificatonReminders}`);
                // console.log(`notificationRemindersStatus: \n\n ${notificatonRemindersStatus}`);

                // let community, communityName: null | String;
                // let community, communityNames = [];

                // if(tagwish.communityIdList && tagwish.communityIdList.length > 0) {
                //     // community = await userDaoV1.findOne('communities', { "_id": tagwish.communityIdList[0] });
                //     for(const item of tagwish.communityIdList) {
                //         community = await baseDao.findOne('communities', { "_id": item });
                //         if(community) {
                //             communityNames.push(community.name);
                //         }
                //     }
                // }

                // if(community) {
                //     communityName = community.name;
                // }

                // console.log(`wish hostId: ${wish.hostId}`);

                // if no user is found and the contact info isn't already in the array, add contact info to array
                if(!user && !this._doesExistInNonUsers(countryCode, phoneNumber)) {

                    // /* TEMPORARY CODE FOR TESTING PURPOSES */
                    // this._nonUsersData.push({
                    //     countryCode,
                    //     phoneNumber,
                    //     "fullPhoneNumber": countryCode+phoneNumber,
                    //     wishId: wish._id,
                    //     wishType,
                    //     userId: wish.userId._id,
                    //     communityIdList,
                    //     communityName,
                    //     notificatonReminders,
                    //     notificatonRemindersStatus,
                    //     // isActiveUser: false,
                    // });
                    // /* ENDS HERE */ 


                    // checking the reminders status
                    if(notificatonRemindersStatus && !notificatonRemindersStatus.reminder24Hours) {
                        console.log("FIRST IF ENTER 24");
                        if(notificatonReminders && notificatonReminders.reminder24Hours instanceof Date) {
                            console.log("SECOND IF ENTER 24");
                            const reminderDate = new Date(notificatonReminders.reminder24Hours);
                            console.log(`REMINDER DATE: ${reminderDate}`);
                            // Comparing the current date with the reminder date
                            if(currentDate > reminderDate) {
                                // adding contact info to array
                                console.log('THIRD IF ENTER 24')                
                                this._nonUsersData.push({
                                    countryCode,
                                    phoneNumber,
                                    fullPhoneNumber: countryCode+phoneNumber,
                                    isCommunityInvite: true,
                                    communityName: community.name,
                                    userId: community.userId,
                                    notificatonReminders,
                                    notificatonRemindersStatus,
                                    contactName: contact.name ? contact.name : "",
                                });
                                // updating the status of the reminder in the collection
                                const updateResult = await baseDao.updateOne(
                                    'members', 
                                    { _id: member._id }, 
                                    { $set: { 'notificationReminder24HoursStatus': true } },
                                    { /* options */ }
                                    );
                                
                                if(updateResult) {
                                    console.log('Update Successful');
                                } else {
                                    console.log('Document not found or not updated');
                                }
                            }
                        }
                    } else if(notificatonRemindersStatus && !notificatonRemindersStatus.reminder72Hours) {
                        console.log("FIRST IF ENTER 72");
                        if(notificatonReminders && notificatonReminders.reminder72Hours instanceof Date) {
                            console.log("SECOND IF ENTER 72");
                            const reminderDate = new Date(notificatonReminders.reminder72Hours);
                            // Comparing the current date with the reminder date
                            if(currentDate > reminderDate) {
                                console.log("THIRD IF ENTER 72");
                                // adding contact info to array
                                this._nonUsersData.push({
                                    countryCode,
                                    phoneNumber,
                                    fullPhoneNumber: countryCode+phoneNumber,
                                    isCommunityInvite: true,
                                    communityName: community.name,
                                    userId: community.userId,
                                    notificatonReminders,
                                    notificatonRemindersStatus,
                                    contactName: contact.name ? contact.name : "",
                                });
                                // console.log(`wishId = ${wish._id}`);
                                // updating the status of the reminder in the collection
                                const updateResult = await baseDao.updateOne(
                                    'members', 
                                    { _id: member._id }, 
                                    { $set: { 'notificationReminder72HoursStatus': true } },
                                    { /* options */ }
                                    );
                                
                                if(updateResult) {
                                    console.log('Update Successful');
                                } else {
                                    console.log('Document not found or not updated');
                                }
                            }
                        }
                    } else if(notificatonRemindersStatus && !notificatonRemindersStatus.reminder120Hours) {
                        console.log("FIRST IF ENTER 120");
                        if(notificatonReminders && notificatonReminders.reminder120Hours instanceof Date) {
                            console.log("SECOND IF ENTER 120");
                            const reminderDate = new Date(notificatonReminders.reminder120Hours);
                            // Comparing the current date with the reminder date
                            if(currentDate > reminderDate) {
                                console.log("THIRD IF ENTER 120");
                                // adding contact info to array
                                this._nonUsersData.push({
                                    countryCode,
                                    phoneNumber,
                                    fullPhoneNumber: countryCode+phoneNumber,
                                    isCommunityInvite: true,
                                    communityName: community.name,
                                    userId: community.userId,
                                    notificatonReminders,
                                    notificatonRemindersStatus,
                                    contactName: contact.name ? contact.name : "",
                                });
                                // console.log(`members = ${member._id}`);
                                // updating the status of the reminder in the collection
                                const updateResult = await baseDao.updateOne(
                                    'members', 
                                    { _id: member._id }, 
                                    { $set: { 'notificationReminder120HoursStatus': true } },
                                    { /* options */ }
                                    );
                                
                                if(updateResult) {
                                    console.log('Update Successful');
                                } else {
                                    console.log('Document not found or not updated');
                                }
                            }
                        }
                    } 
                }
            }

            // return this._nonUsersData;
            
            // SENDING NOTIFICATION CODE
            // wishtype MYSELF: 1st scenario // wishtype OTHER: 2nd scenario
            console.log('ABOUT TO ENTER NOTIFICATION FOR LOOP');
            for(const item of this._nonUsersData){
                console.log('ENTERED FOR LOOP');
                console.log(`item = ${item}`);
                if(item.isCommunityInvite) {
                    console.log('ENTERED COMMUNITYNAMES IF CHECK');
                    for(const community of item.communityNames) {
                        let imessageText = IMESSAGE_TEXT.COMMUNITY_INVITE_REMINDER.replace('[COMMUNITY]', `(${community})`).replace('[LINK]', 'https://api.staging.wishwellvillage.com/api/v1/common/deeplink?type=SMS_INVITE').replace('[NAME]', item.contactName);
                        let pushParamsContact = {"message":MESSAGES_NOTIFICATION.REMINDER_COMMUNITY_INVITE, "title":TITLE_NOTIFICATION.REMINDER, "imessageNumber": item.phoneNumber, "imessageText": imessageText, "type":TYPE_NOTIFICATION.NOTIFICATION_REMINDER};
                        await notificationManager.PublishNotification([item], item.wishId, pushParamsContact);
                    }
                }
                else if(item.wishType === "MYSELF") {
                    console.log('ENTERED MYSELF IF CHECK');
                    let imessageText = IMESSAGE_TEXT.MYSELF_APP_INVITE.replace('[LINK]', 'https://api.staging.wishwellvillage.com/api/v1/common/deeplink?type=SMS_INVITE').replace('[NAME]', item.contactName);
                    let pushParamsContact = {"message":MESSAGES_NOTIFICATION.REMINDER_MYSELF_APP_INVITE, "title":TITLE_NOTIFICATION.REMINDER, "imessageNumber": item.phoneNumber, "imessageText": imessageText, "type":TYPE_NOTIFICATION.NOTIFICATION_REMINDER};
                    await notificationManager.PublishNotification([item], item.wishId, pushParamsContact);
                }
                else if(item.wishType === "OTHER") {
                    console.log('ENTERED OTHER IF CHECK');
                    let imessageText = IMESSAGE_TEXT.OTHERS_APP_INVITE.replace('[LINK]', 'https://api.staging.wishwellvillage.com/api/v1/common/deeplink?type=SMS_INVITE').replace('[NAME]', item.contactName);
                    let pushParamsContact = {"message":MESSAGES_NOTIFICATION.REMINDER_OTHERS_APP_INVITE, "title":TITLE_NOTIFICATION.REMINDER, "imessageNumber": item.phoneNumber, imessageText,"type":TYPE_NOTIFICATION.NOTIFICATION_REMINDER};
                    await notificationManager.PublishNotification([item], item.wishId, pushParamsContact);
                }
            }
            
            // console.log(`Printing data from array \n\n ${this._nonUsersData}`);
            return this._nonUsersData;
        } catch(err) {
            console.log(err);
            return err.message;
        }      
    }

    /**
     * @function checkNotificationV2
     * @description checking the status of reminders and sending them accordingly
     */
    async checkNotificationV2() {
        try {
            this._nonUsersData = [];

            const currentDate = new Date();

            const sixDaysAgo = new Date();
            sixDaysAgo.setDate(sixDaysAgo.getDate() - 6);

            const reminders = await baseDao.find('reminders', 
            {
                "createdAt": { $gte: sixDaysAgo }, 
                "status": STATUS.ACTIVE
            }, {}); 

            console.log('REMINDERS = ', reminders);

            if(reminders) {
                for(const reminder of reminders) {
                    const { countryCode, phoneNumber } = reminder;

                    // search for users in the 'users' collection
                    const user = await baseDao.findOne('users', { countryCode, phoneNumber, status: STATUS.UN_BLOCKED });   
                    
                    if(user) {
                        await baseDao.updateOne(
                            'reminders', 
                            { _id: reminder._id }, 
                            { $set: { 'status': STATUS.CLOSED } },
                            { /* options */ }
                            );
                    }

                    if(!user && !this._doesExistInNonUsers(countryCode, phoneNumber)) {
                        // const contact = await baseDao.findOne('contacts', { phoneNumber });
                        const contact = await baseDao.findOne('contacts', {
                            $or: [
                              { phoneNumber }, // phoneNumber = phoneNumber
                              { phoneNumber: countryCode + phoneNumber } // phoneNumber = countryCode + phoneNumber
                            ]
                          });

                        console.log('CONTACT = ', contact);

                        if(contact) {
                            console.log('entered contact if');
                            console.log(`contact = ${contact.phoneNumber}`);
                        }

                        const notificatonReminders = {
                            reminder24Hours: reminder.notificationReminder24Hours,
                            reminder72Hours: reminder.notificationReminder72Hours,
                            reminder120Hours: reminder.notificationReminder120Hours,
                        }
        
                        const notificatonRemindersStatus = {
                            reminder24Hours: reminder.notificationReminder24HoursStatus,
                            reminder72Hours: reminder.notificationReminder72HoursStatus,
                            reminder120Hours: reminder.notificationReminder120HoursStatus,
                        }

                        // checking the reminders status
                    if(notificatonRemindersStatus && !notificatonRemindersStatus.reminder24Hours) {
                        console.log("FIRST IF ENTER 24");
                        if(notificatonReminders && notificatonReminders.reminder24Hours instanceof Date) {
                            console.log("SECOND IF ENTER 24");
                            const reminderDate = new Date(notificatonReminders.reminder24Hours);
                            console.log(`REMINDER DATE: ${reminderDate}`);
                            // Comparing the current date with the reminder date
                            if(currentDate > reminderDate) {
                                // adding contact info to array
                                console.log('THIRD IF ENTER 24')             
                                this._nonUsersData.push({
                                    userId: reminder.userId,
                                    tagWishId: reminder.tagWishId,
                                    wishType: reminder.wishType,
                                    communityId: reminder.communityId,
                                    communityName: reminder.communityName,
                                    countryCode: reminder.countryCode,
                                    phoneNumber: reminder.phoneNumber,
                                    isCommunityInvite: reminder.isCommunityInvite,
                                    contactName: contact ? contact.name : "",
                                    notificatonReminders,
                                    notificatonRemindersStatus
                                });
                                // updating the status of the reminder in the collection
                                const updateResult = await baseDao.updateOne(
                                    'reminders', 
                                    { _id: reminder._id }, 
                                    { $set: { 'notificationReminder24HoursStatus': true } },
                                    { /* options */ }
                                    );
                                
                                if(updateResult.n) {
                                    console.log('Update Successful');
                                } else {
                                    console.log('Document not found or not updated');
                                }
                            }
                        }
                    } else if(notificatonRemindersStatus && !notificatonRemindersStatus.reminder72Hours) {
                        console.log("FIRST IF ENTER 72");
                        if(notificatonReminders && notificatonReminders.reminder72Hours instanceof Date) {
                            console.log("SECOND IF ENTER 72");
                            const reminderDate = new Date(notificatonReminders.reminder72Hours);
                            // Comparing the current date with the reminder date
                            if(currentDate > reminderDate) {
                                console.log("THIRD IF ENTER 72");
                                // adding contact info to array
                                this._nonUsersData.push({
                                    userId: reminder.userId,
                                    tagWishId: reminder.tagWishId,
                                    wishType: reminder.wishType,
                                    communityId: reminder.communityId,
                                    communityName: reminder.communityName,
                                    countryCode: reminder.countryCode,
                                    phoneNumber: reminder.phoneNumber,
                                    isCommunityInvite: reminder.isCommunityInvite,
                                    contactName: contact ? contact.name : "",
                                    notificatonReminders,
                                    notificatonRemindersStatus
                                });
                                // console.log(`wishId = ${wish._id}`);
                                // updating the status of the reminder in the collection
                                const updateResult = await baseDao.updateOne(
                                    'reminders', 
                                    { _id: reminder._id }, 
                                    { $set: { 'notificationReminder72HoursStatus': true } },
                                    { /* options */ }
                                    );
                                
                                if(updateResult.n) {
                                    console.log('Update Successful');
                                } else {
                                    console.log('Document not found or not updated');
                                }
                            }
                        }
                    } else if(notificatonRemindersStatus && !notificatonRemindersStatus.reminder120Hours) {
                        console.log("FIRST IF ENTER 120");
                        if(notificatonReminders && notificatonReminders.reminder120Hours instanceof Date) {
                            console.log("SECOND IF ENTER 120");
                            const reminderDate = new Date(notificatonReminders.reminder120Hours);
                            // Comparing the current date with the reminder date
                            if(currentDate > reminderDate) {
                                console.log("THIRD IF ENTER 120");
                                // adding contact info to array
                                this._nonUsersData.push({
                                    userId: reminder.userId,
                                    tagWishId: reminder.tagWishId,
                                    wishType: reminder.wishType,
                                    communityId: reminder.communityId,
                                    communityName: reminder.communityName,
                                    countryCode: reminder.countryCode,
                                    phoneNumber: reminder.phoneNumber,
                                    isCommunityInvite: reminder.isCommunityInvite,
                                    contactName: contact ? contact.name : "",
                                    notificatonReminders,
                                    notificatonRemindersStatus
                                });
                                // console.log(`members = ${member._id}`);
                                // updating the status of the reminder in the collection
                                const updateResult = await baseDao.updateOne(
                                    'reminders', 
                                    { _id: reminder._id }, 
                                    { $set: {
                                        'notificationReminder120HoursStatus': true,
                                        'status': STATUS.IN_ACTIVE
                                        } 
                                    },
                                    { /* options */ }
                                    );
                                
                                if(updateResult.n) {
                                    console.log('Update Successful');
                                } else {
                                    console.log('Document not found or not updated');
                                }
                            }
                        }
                    } 
                        
                    }
                }

                const shareLink = `${SERVER.APP_URL}${SERVER.API_BASE_URL}/v1/common/deeplink?type=${DEEPLINK_TYPE.SMS_INVITE}`;
                for(const item of this._nonUsersData){
                    console.log('ENTERED FOR LOOP');
                    console.log(`item = ${item}`);
                    if(item.isCommunityInvite) {
                        console.log('ENTERED COMMUNITYNAMES IF CHECK');   
                        let notificationMessage = MESSAGES_NOTIFICATION.REMINDER_COMMUNITY_INVITE.replace('[NAME]', item.contactName);                   
                        let imessageText = IMESSAGE_TEXT.COMMUNITY_INVITE_REMINDER.replace('[COMMUNITY]', `(${item.communityName})`).replace('[LINK]', shareLink).replace('[NAME]', item.contactName);
                        let pushParamsContact = {"message":notificationMessage, "title":TITLE_NOTIFICATION.REMINDER, "imessageNumber": item.phoneNumber, "imessageText": imessageText, "type":TYPE_NOTIFICATION.NOTIFICATION_REMINDER};
                        await notificationManager.PublishNotification([item], item.wishId, pushParamsContact);
                    }
                    else if(item.wishType === "MYSELF") {
                        console.log('ENTERED MYSELF IF CHECK');
                        let notificationMessage = MESSAGES_NOTIFICATION.REMINDER_MYSELF_APP_INVITE.replace('[NAME]', item.contactName);  
                        let imessageText = IMESSAGE_TEXT.MYSELF_APP_INVITE.replace('[LINK]', shareLink).replace('[NAME]', item.contactName);
                        let pushParamsContact = {"message":notificationMessage, "title":TITLE_NOTIFICATION.REMINDER, "imessageNumber": item.phoneNumber, "imessageText": imessageText, "type":TYPE_NOTIFICATION.NOTIFICATION_REMINDER};
                        await notificationManager.PublishNotification([item], item.wishId, pushParamsContact);
                    }
                    else if(item.wishType === "OTHER") {
                        console.log('ENTERED OTHER IF CHECK');
                        let notificationMessage = MESSAGES_NOTIFICATION.REMINDER_OTHERS_APP_INVITE.replace('[NAME]', item.contactName);
                        let imessageText = IMESSAGE_TEXT.OTHERS_APP_INVITE.replace('[LINK]', shareLink).replace('[NAME]', item.contactName);
                        let pushParamsContact = {"message":notificationMessage, "title":TITLE_NOTIFICATION.REMINDER, "imessageNumber": item.phoneNumber, imessageText,"type":TYPE_NOTIFICATION.NOTIFICATION_REMINDER};
                        await notificationManager.PublishNotification([item], item.wishId, pushParamsContact);
                    }
                }
                
                // console.log(`Printing data from array \n\n ${this._nonUsersData}`);
                console.log('USER DATA = ', this._nonUsersData);
                return `Reminder Notifications Sent: ${this._nonUsersData.length}`;

            }

        } catch(error) {
            console.error(error);
            return error.message;
        }
    }

    /**
     * @function clearInviteWishes
     * @description clearing wish(es) data if invite is not accepted after 10 days
     */
    async clearInviteWishes() {
        const nonUsersData = [];

        const findAsync = util.promisify(userDaoV1.find.bind(userDaoV1));

        const tenDaysAgo = new Date();
        tenDaysAgo.setDate(tenDaysAgo.getDate() - 10);

        const tagwishes = await findAsync('tagwishes', 
        {
            "createdAt": { $lte: tenDaysAgo }, 
            status: 'ACTIVE',
        }); 

        for(const tagwish of tagwishes) {
            const { phoneNumber, countryCode } = tagwish;
            // search for users in the 'users' collection
            const user = await userDaoV1.findOne('users', { countryCode, phoneNumber });
            if(!user) {
                nonUsersData.push(tagwish.wishId);
            }
        }

        for(const wishId of nonUsersData) {
            const updateResult = await userDaoV1.updateOne(
                'wishes', 
                { _id: wishId }, 
                { 
                    $unset: { 
                        wishNumber: 1,
                        title: 1,
                        description: 1,
                        isGlobalWish: 1,
                        intension: 1,
                        communityId: 1,
                        wishType: 1,
                        totalBlessings: 1,
                        blessingTime: 1,
                        image: 1,
                        isFlagged: 1,
                        flagStatus: 1,
                        unflagHistory: 1,
                        reportCount: 1,
                        status: 1,
                        userDetail: 1,
                        oldStatus: 1,
                        hostLastBlessTime: 1,
                        userLastBlessTime: 1,
                        isGlobalPinned: 1,
                        location: 1,
                        closeDate: 1,
                        notificationReminder24Hours: 1,
                        notificationReminder72Hours: 1,
                        notificationReminder120Hours: 1,
                        notificationReminder24HoursStatus: 1,
                        notificationReminder72HoursStatus: 1,
                        notificationReminder120HoursStatus: 1,
                    } 
                },
                { /* options */ }
                );
        }
    }

    /**
     * @function clearInviteWishesV2
     * @description clearing wish(es) data if invite is not accepted after 10 days
     */
    async clearInviteWishesV2() {
        try {
            const nonUsersData = [];

            const findAsync = util.promisify(baseDao.find.bind(baseDao));

            const tenDaysAgo = new Date();
            tenDaysAgo.setDate(tenDaysAgo.getDate() - 10);

            const reminders = await findAsync('reminders', 
            {
                "createdAt": { $lte: tenDaysAgo }, 
                "status": STATUS.IN_ACTIVE,
            }); 

            for(const reminder of reminders) {
                if(!reminder.isCommunityInvite) {
                    const { phoneNumber, countryCode } = reminder;
                    // search for users in the 'users' collection
                    const user = await userDaoV1.findOne('users', { countryCode, phoneNumber });
                    if(!user) {
                        nonUsersData.push({
                            reminderId: reminder._id,
                            wishId: reminder.wishId,
                            tagWishId: reminder.tagWishId
                        });
                    }
                }
            }

            let updatedResults = [];
            for(let item of nonUsersData) {
                const wishUpdate = await userDaoV1.updateOne(
                    'wishes', 
                    { _id: item.wishId }, 
                    { 
                        $unset: { 
                            // wishNumber: 1,
                            title: 1,
                            description: 1,
                            isGlobalWish: 1,
                            intension: 1,
                            communityId: 1,
                            wishType: 1,
                            totalBlessings: 1,
                            blessingTime: 1,
                            image: 1,
                            isFlagged: 1,
                            flagStatus: 1,
                            unflagHistory: 1,
                            reportCount: 1,
                            // status: 1,
                            userDetail: 1,
                            oldStatus: 1,
                            hostLastBlessTime: 1,
                            userLastBlessTime: 1,
                            isGlobalPinned: 1,
                            location: 1,
                            closeDate: 1,
                        },
                        $set: {
                            declinedAt: new Date(Date.now()),
                            declineReason: STATUS.DECLINED,
                            status: STATUS.DELETED
                        }
                    },
                    { /* options */ }
                    );

                if(wishUpdate.n) {
                    const tagWishUpdate = await baseDao.updateOne(
                        'tagwishes', 
                        { _id: item.tagWishId }, 
                        { $set: {
                            'status': STATUS.DELETED
                            } 
                        },
                        { /* options */ }
                        );
                        
                    if(tagWishUpdate.n) {
                        const reminderUpdate = await baseDao.updateOne(
                            'reminders', 
                            { _id: item.reminderId }, 
                            { $set: {
                                'status': STATUS.DELETED
                                } 
                            },
                            { /* options */ }
                            );
                            updatedResults.push({
                                ...item,
                                status: 'UPDATE SUCCESSFUL'
                            });
                    }
                }
            }
            return `Wishes Cleared: ${updatedResults.length}`;
        } catch(error) {
            console.error(error);
            return error.message;
        }
    }

    /**
     * @function _doesExistInNonUsers
     * @description Check if an object with the same countryCode and phoneNumber exists in nonUsersNumbers array
     * @param {string} countryCode - The country code to check
     * @param {string} phoneNumber - The phone number to check
     * @returns {boolean} - True if the object exists, false otherwise
     */
    _doesExistInNonUsers(countryCode, phoneNumber) {
        return this._nonUsersData.some(item => item.countryCode === countryCode && item.phoneNumber === phoneNumber);
    }
}

export const wishesReminderController = new WishesReminderController();