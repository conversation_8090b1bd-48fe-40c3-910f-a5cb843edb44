"use strict";
import { Request, ResponseToolkit } from "@hapi/hapi";
import * as <PERSON><PERSON> from "joi";
import { failActionFunction } from "@utils/appUtils";
import { wishesControllerV1 } from "@modules/wishes/index";
import {
	MESSAGES,
	SWAGGER_DEFAULT_RESPONSE_MESSAGES,
	SERVER,
	WISH_INTESION,
	REGEX,
	WISH_TYPE,
	ACTION_TYPE,
	STATUS
	
} from "@config/index";
import { responseHandler } from "@utils/ResponseHandler";
import { authorizationHeaderObj } from "@utils/validator";
export const wishesRoute = [
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/wishes`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const payload: WishesRequest.Add = request.payload;
				const result = await wishesControllerV1.addWishes(payload,tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "wishes"],
			description: "Add Wishes",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {  
				headers: authorizationHeaderObj,
				payload: Joi.object({
					description: Joi.string().trim().required(),
					wishType: Joi.string().trim().required().valid(WISH_TYPE.MYSELF,WISH_TYPE.OTHER), 
					hostId: Joi.string().trim().regex(REGEX.MONGO_ID).optional(),
					hostDetail:Joi.object({
						userId: Joi.string().trim().regex(REGEX.MONGO_ID).optional(),  // this userId is contactUserId from contact	Model.
						countryCode: Joi.string().trim().optional(),
						phoneNumber: Joi.string().trim().optional(),
						isAppUser: Joi.boolean().required(),
						contactId: Joi.string().trim().regex(REGEX.MONGO_ID).optional(),  // this is contactId of non-app user from contact table
						name:Joi.string().trim().optional()
							
				    }).optional(),
					isGlobalWish: Joi.boolean().default(false).required(),
					intension: Joi.string().trim().required().valid(WISH_INTESION.HEALTH,WISH_INTESION.LOVE,WISH_INTESION.PEACE,WISH_INTESION.ABUNDANCE),
					image: Joi.string().trim().allow("").optional(),
					communityId: Joi.array().items(Joi.string().trim().regex(REGEX.MONGO_ID).optional()).optional(),
					tagContacts: Joi.array().items(
						Joi.object({
						    userId: Joi.string().trim().regex(REGEX.MONGO_ID).optional(),  // this userId is contactUserId from contact	Model.
							contactId: Joi.string().trim().regex(REGEX.MONGO_ID).optional(),  // this is contactId of non-app user from contact table
							countryCode: Joi.string().trim().optional(),
						    phoneNumber: Joi.string().trim().optional(),
						    name: Joi.string().trim().optional(),
						    isAppUser: Joi.boolean().required(),
					  }).optional(),).optional(),
					cohosts: Joi.array().items(Joi.string()).optional(),  
					location:Joi.object({
						address: Joi.string().trim().allow("").optional(),
						coordinates: Joi.array().required(),
						city:Joi.string().required(),
						country:Joi.string().required(),
						state:Joi.string().required()
					}).optional(), 
				}).or("communityId", "tagContacts"),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}  
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/wishes/requested-wishes`,   
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const query: WishesRequest.WishList = request.query;
				const result = await wishesControllerV1.getWishes(query,tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				console.log(error, "ERROR");
				throw error;			
			}
		},
		options: {
			tags: ["api", "wishes"],
			description: "requested Wish List",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				query: Joi.object({
					userId: Joi.string().trim().regex(REGEX.MONGO_ID).required(),
					pageNo: Joi.number().optional().description("Page no"),
					limit: Joi.number().optional().description("limit"),
					searchKey: Joi.string().optional().description("Search by name"),
					communityId: Joi.string().optional()
					// Joi.alternatives()
                    //         .try(Joi.array().items(Joi.string().trim()), Joi.string())
                    //         .optional(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/wishes/received-wishes`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const query: WishesRequest.WishList = request.query;
				const result = await wishesControllerV1.receivedWishList(query,tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
	
			}
		},
		options: {
			tags: ["api", "wishes"],
			description: "received Wish List",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				query: Joi.object({
					userId: Joi.string().trim().regex(REGEX.MONGO_ID).required(),
					pageNo: Joi.number().optional().description("Page no"),
					limit: Joi.number().optional().description("limit"),
					searchKey: Joi.string().optional().description("Search"),
					communityId: Joi.string().optional()
					// Joi.alternatives()
                    //         .try(Joi.array().items(Joi.string().trim()), Joi.string())
                    //         .optional(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/wishes/wish-detail`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const query: WishesRequest.WishDetail = request.query;
				const result = await wishesControllerV1.wishDetail(query,tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);

			}
		},
		options: {
			tags: ["api", "wishes"],
			description: "Wish Details",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				query: Joi.object({
					wishId: Joi.string().trim().regex(REGEX.MONGO_ID).required(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/wishes/pinned-wishes`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const payload: WishesRequest.PinnedWishes = request.payload;
				const result = await  wishesControllerV1.pinnedWishes(payload,tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "wishes"],
			description: "Add/Remove pinned wishes",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
					subjectId: Joi.string().trim().regex(REGEX.MONGO_ID).required(),
					type:Joi.string().valid(ACTION_TYPE.ADD,ACTION_TYPE.REMOVE).required()
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/wishes/pinned-wishes`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const query: ListingRequest = request.query;
				const result = await wishesControllerV1.getPinnedWishes(query,tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "wishes"],
			description: "Pinned Wish List",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				query: Joi.object({}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/wishes/delete-wish`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const payload: WishesRequest.DeleteWish = request.payload;
				const result = await  wishesControllerV1.deleteWish(payload,tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "wishes"],
			description: "Delete wish",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
					wishId: Joi.string().trim().regex(REGEX.MONGO_ID).required(),
					status: Joi.string().valid(STATUS.CLOSED).optional(),

				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "PUT",
		path: `${SERVER.API_BASE_URL}/v1/wishes/edit-wish`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const payload: WishesRequest.EditWish = request.payload;
				const result = await  wishesControllerV1.editWish(payload,tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "wishes"],
			description: "Edit/Modify wish",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
					wishId: Joi.string().trim().regex(REGEX.MONGO_ID).required(),
					description: Joi.string().trim().optional(),
					intension: Joi.string().trim().optional().valid(WISH_INTESION.HEALTH,WISH_INTESION.LOVE,WISH_INTESION.PEACE,WISH_INTESION.ABUNDANCE),
					image: Joi.string().trim().allow("").optional(),
					addCommunityId: Joi.array().items(Joi.string().trim().regex(REGEX.MONGO_ID).optional()).optional(),
					removeCommunityId: Joi.array().items(Joi.string().trim().regex(REGEX.MONGO_ID).optional()).optional(),
					addTagContacts: Joi.array().items(
						Joi.object({
						    userId: Joi.string().trim().regex(REGEX.MONGO_ID).optional(),  // this userId is contactUserId from contact	Model.
							contactId: Joi.string().trim().regex(REGEX.MONGO_ID).optional(),  // this is contactId of non-app user from contact table
							countryCode: Joi.string().trim().optional(),
						    phoneNumber: Joi.string().trim().optional(),
						    isAppUser: Joi.boolean().required(),
					  }).optional(),).optional(),
					removeTagContacts: Joi.array().items(
						Joi.object({
						    userId: Joi.string().trim().regex(REGEX.MONGO_ID).optional(),  // this userId is contactUserId from contact	Model.
							contactId: Joi.string().trim().regex(REGEX.MONGO_ID).optional(),  // this is contactId of non-app user from contact table
							countryCode: Joi.string().trim().optional(),
						    phoneNumber: Joi.string().trim().optional(),
						    isAppUser: Joi.boolean().required(),
					  }).optional(),).optional(),
					addCohosts: Joi.array().items(Joi.string()).optional(),  
					removeCohosts: Joi.array().items(Joi.string()).optional(),  

				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/wishes/edit-wish-detail`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const query: WishesRequest.EditWishDetail = request.query;
				const result = await wishesControllerV1.editWishDetail(query,tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);

			}
		},
		options: {
			tags: ["api", "wishes"],
			description: "Edit Wish Detail",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				query: Joi.object({
					wishId: Joi.string().trim().regex(REGEX.MONGO_ID).required(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/wishes/host-invite`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const payload: WishesRequest.AcceptReject = request.payload;
				const result = await  wishesControllerV1.acceptRejectWish(payload,tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "wishes"],
			description: "Host accept/decline the wish",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
					wishId: Joi.string().trim().regex(REGEX.MONGO_ID).required(),
					type: Joi.string().valid(ACTION_TYPE.ACCEPT,ACTION_TYPE.REJECT).required(),
					rejectReason: Joi.string().trim().optional()

				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/wishes/wish-request`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const query: WishesRequest.WishList = request.query;
				const result = await wishesControllerV1.wishRequest(query,tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
	
			}
		},
		options: {
			tags: ["api", "wishes"],
			description: "Wish request",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				query: Joi.object({
					userId: Joi.string().trim().regex(REGEX.MONGO_ID).required(),
					status: Joi.string().valid(STATUS.PENDING).required(),
					pageNo: Joi.number().optional().description("Page no"),
					limit: Joi.number().optional().description("limit"),
					//searchKey: Joi.string().optional().description("Search"),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/wishes/remove-wish`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const payload: WishesRequest.RemoveWish = request.payload;
				const result = await  wishesControllerV1.removeWish(payload,tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "wishes"],
			description: "Delete wish",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
					wishId: Joi.string().trim().regex(REGEX.MONGO_ID).required(),
				//	status: Joi.string().valid(STATUS.CLOSED).optional(),

				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "PUT",
		path: `${SERVER.API_BASE_URL}/v1/admin/wish-status/{id}`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const params: WishesRequest.WishStatus = request.params;
				const payload: WishesRequest.WishStatus = request.payload;
				const result = await wishesControllerV1.updateGlobalWishStatus({ ...params, ...payload });
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin wish"],
			description: "Update Global Wish  Status",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				params: Joi.object({
					id: Joi.string().required(),
				}),
				payload: Joi.object({
					status: Joi.string().required().valid(STATUS.ACTIVE, STATUS.CLOSED)
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "PUT",
		path: `${SERVER.API_BASE_URL}/v1/admin/pin-global-wish/{id}`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const params: WishesRequest.WishStatus = request.params;
				const payload: WishesRequest.WishStatus = request.payload;
				const result = await wishesControllerV1.pinGlobalWish({ ...params, ...payload });
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin wish"],
			description: "Pin Global Wish",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				params: Joi.object({
					id: Joi.string().required(),
				}),
				payload: Joi.object({
					isGlobalPinned: Joi.boolean().required().valid(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/wishes/wish-location`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const query: WishesRequest.MapLocation = request.query;
				const result = await wishesControllerV1.wishLocation(tokenData,query);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
	
			}
		},
		options: {
			tags: ["api", "wishes"],
			description: "get the location of Wish ",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				query: Joi.object({
					type:Joi.number().optional().description("0->none, 1->12 hrs, 2->24hrs, 3->7Days, 4->30 days")
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
];