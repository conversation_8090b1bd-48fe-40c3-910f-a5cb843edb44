"use strict";

const mongoose = require("mongoose");
import { Document, model, Model, Schema } from "mongoose";

import { DB_MODEL_REF, STATUS, REPORT_TYPE, FLAG_TYPE } from "@config/constant";

export interface Report extends Document {
    userId: string;
    subjectId: string;
    type: string;
    created: number;
    status: string;
}

const reportSchema: Schema = new mongoose.Schema({
    _id: { type: Schema.Types.ObjectId, required: true, auto: true },
    userId: { type: Schema.Types.ObjectId, required: true },
    subjectId: { type: Schema.Types.ObjectId, required: true },
    ownerId: { type: Schema.Types.ObjectId, required: true },
    description: { type: String, required: false, default: '' },
    reportType: { type: String, required: false, default: '' },
    type: {
        type: String,
        required: true,
        enum: [REPORT_TYPE.WISH, REPORT_TYPE.BLESSING, REPORT_TYPE.GRATITUDE, REPORT_TYPE.COMMUNITY]
    },
    status: {
        type: String,
        enum: [STATUS.ACTIVE, STATUS.REPORTED, STATUS.DELETED],
        default: STATUS.REPORTED
    },
    created: { type: Number, default: Date.now }
}, {
    versionKey: false,
    timestamps: true
});

// Export reports
export const reports: Model<Report> = model<Report>(DB_MODEL_REF.REPORTS, reportSchema);