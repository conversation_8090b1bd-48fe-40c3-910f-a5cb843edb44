"use strict";
import { Request, ResponseToolkit } from "@hapi/hapi";
import * as <PERSON><PERSON> from "joi";
import { failActionFunction } from "@utils/appUtils";
import { communitiesControllerV2 } from "@modules/community/index";
import { communitiesControllerV1 } from "@modules/community/index";
import {
	SWAGGER_DEFAULT_RESPONSE_MESSAGES,
	SERVER,
	REGEX,
	WISH_TAG_TYPE
} from "@config/index";
import { responseHandler } from "@utils/ResponseHandler";
import { authorizationHeaderObj } from "@utils/validator";
export const communitiesRoute = [
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v2/community`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const payload: CommunitiesRequest.Add = request.payload;
				const result = await communitiesControllerV2.addCommunities(payload, tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "communities v2"],
			description: "Create Community",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
					name: Joi.string().trim().required(),
					purpose: Joi.string().trim().required(),
					image: Joi.string().trim().allow("").required(),
					visibility: Joi.string().valid("PUBLIC", "PRIVATE").default("PUBLIC").optional(),
					members: Joi.array().items(
						Joi.object({
							userId: Joi.string().trim().regex(REGEX.MONGO_ID).optional(),
							contactId: Joi.string().trim().regex(REGEX.MONGO_ID).required(),  // this is contactId of non-app user from contact table
							countryCode: Joi.string().trim().optional(),
							phoneNumber: Joi.string().trim().optional(),
							isAppUser: Joi.boolean().required(),
						}).required(),).required(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "PUT",
		path: `${SERVER.API_BASE_URL}/v2/community/edit-community`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const payload: CommunitiesRequest.EditCommunity = request.payload;
				const result = await communitiesControllerV2.editCommunity(payload, tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "communities v2"],
			description: "Edit/Modify Community",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
					communityId: Joi.string().trim().regex(REGEX.MONGO_ID).required(),
					name: Joi.string().trim().optional(),
					purpose: Joi.string().trim().optional(),
					image: Joi.string().trim().allow("").optional(),
					visibility: Joi.string().valid("PUBLIC", "PRIVATE").default("PUBLIC").optional(),
					addMembers: Joi.array().items(
						Joi.object({
							userId: Joi.string().trim().regex(REGEX.MONGO_ID).optional(),
							contactId: Joi.string().trim().regex(REGEX.MONGO_ID).optional(),  // this is contactId of non-app user from contact table
							countryCode: Joi.string().trim().optional(),
							phoneNumber: Joi.string().trim().optional(),
							isAppUser: Joi.boolean().required(),
						}).optional(),).optional(),
					removeMembers: Joi.array().items(
						Joi.object({
							userId: Joi.string().trim().regex(REGEX.MONGO_ID).optional(),
							contactId: Joi.string().trim().regex(REGEX.MONGO_ID).optional(),  // this is contactId of non-app user from contact table
							countryCode: Joi.string().trim().optional(),
							phoneNumber: Joi.string().trim().optional(),
							isAppUser: Joi.boolean().required(),
						}).optional(),).optional(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v2/community`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const query: CommunitiesRequest.CommunityList = request.query;
				const result = await communitiesControllerV1.getCommunities(query, tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				console.log(error, "ERROR");
				throw error;
			}
		},
		options: {
			tags: ["api", "communities v2"],
			description: "Community List",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				query: Joi.object({
					type: Joi.string().valid(WISH_TAG_TYPE.INVITED).optional(),
					pageNo: Joi.number().optional().description("Page no"),
					limit: Joi.number().optional().description("limit"),
					searchKey: Joi.string().optional().description("Search by name"),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v2/community/public`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const result = await communitiesControllerV2.getPublicCommunities(request.query);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "communities v2"],
			description: "Get all public communities",
			auth: false,
			validate: {
				query: Joi.object({
					pageNo: Joi.number().optional(),
					limit: Joi.number().optional(),
					searchKey: Joi.string().optional()
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v2/community/public-community-detail`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const result = await communitiesControllerV2.getPublicCommunityDetail(request.query);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "communities v2"],
			description: "Get detail for a public community",
			auth: false,
			validate: {
				query: Joi.object({
					communityId: Joi.string().trim().regex(REGEX.MONGO_ID).required()
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v2/community/join-request`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const payload: CommunitiesRequest.JoinRequest = request.payload;
				const result = await communitiesControllerV2.requestToJoinCommunity(payload, tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "communities v2"],
			description: "Request to join a community",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
					communityId: Joi.string().trim().regex(REGEX.MONGO_ID).required()
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "PUT",
		path: `${SERVER.API_BASE_URL}/v2/community/join-request/respond`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const payload: CommunitiesRequest.JoinRequestResponse = request.payload;
				const result = await communitiesControllerV2.respondToJoinRequest(payload, tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "communities v2"],
			description: "Respond to a community join request",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
					communityId: Joi.string().trim().regex(REGEX.MONGO_ID).required(),
					userId: Joi.string().trim().regex(REGEX.MONGO_ID).required(),
					accept: Joi.boolean().required()
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v2/community/join-requests`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const query: CommunitiesRequest.JoinRequestsList = request.query;
				const result = await communitiesControllerV2.getJoinRequests(query, tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "communities v2"],
			description: "Get pending join requests for a community",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				query: Joi.object({
					communityId: Joi.string().trim().regex(REGEX.MONGO_ID).required(),
					pageNo: Joi.number().optional(),
					limit: Joi.number().optional()
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	}
];
