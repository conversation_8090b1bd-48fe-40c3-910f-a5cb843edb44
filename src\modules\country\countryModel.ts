"use strict";

import * as mongoose from "mongoose";
import {  Schema, Model, Document } from "mongoose";
import {
	DB_MODEL_REF
} from "@config/index";

export interface ICountry extends Document {
    state_id: string,
    country_code: string,
    state_code: string,
    state_name: string,
}

const countrySchema = new Schema({
    _id: { type: mongoose.Schema.Types.ObjectId, required: true, auto: true },
    country_code: { type: String, required: true },
    country_name: { type: String, default: '' },
    country_isd_code: { type: String },
    isZipcode: { type: String, default: "0" },
    position: { type: Number, default: 0 },
}, {
    versionKey: false,
    timestamps: true,

});

// Export countries
export const countries: Model<ICountry> = mongoose.model<ICountry>(DB_MODEL_REF.COUNTRIES, countrySchema);