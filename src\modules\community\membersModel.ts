"use strict";

const mongoose = require("mongoose");
import { Document, model, Model, Schema } from "mongoose";
import { DB_MODEL_REF, STATUS, WISH_TAG_TYPE } from "@config/constant";
export interface members extends Document {
    userId:string;
    communityId:string;
    status: string; 
    countryCode?: string;
	phoneNumber?: string;
    creatorId?:string;
    type?:string;
    created: number;
}

const membersSchema: Schema = new mongoose.Schema({
    _id: { type: Schema.Types.ObjectId, required: true, auto: true },
    userId: { type: Schema.Types.ObjectId, required: true },
    communityId: { type: Schema.Types.ObjectId, required: true },
    status: {
        type: String,
        enum: [STATUS.ACTIVE, STATUS.IN_ACTIVE, STATUS.UN_PUBLISHED,STATUS.DELETED],
        default: STATUS.ACTIVE
    },
    countryCode: { type: String, required: false },
	phoneNumber: { type: String, required: false },
    creatorId: { type: Schema.Types.ObjectId, required: false },
    type: {
        type: String,
        enum: [WISH_TAG_TYPE.CONTACTS, WISH_TAG_TYPE.COHOST,WISH_TAG_TYPE.HOST,WISH_TAG_TYPE.INVITED,WISH_TAG_TYPE.CREATOR],
        default: WISH_TAG_TYPE.CONTACTS,
        
    },
    contactId: { type: Schema.Types.ObjectId, required: false },
    created: { type: Number, default: Date.now },
}, {
    versionKey: false,
    timestamps: true
});

membersSchema.index({ userId: 1 });
membersSchema.index({ status: 1 });
membersSchema.index({ created: -1 });

// Export communities
export const members: Model<members> = model<members>(DB_MODEL_REF.MEMBERS, membersSchema);