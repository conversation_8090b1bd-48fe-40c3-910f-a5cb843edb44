"use strict";
import { Request, ResponseToolkit } from "@hapi/hapi";
import { responseHandler } from "@utils/ResponseHandler";

import {
	MESSAGES,
	SWAGGER_DEFAULT_RESPONSE_MESSAGES,
	SERVER,
	WISH_INTESION,
	REGEX,
	WISH_TYPE,
	ACTION_TYPE,
	STATUS
	
} from "@config/index";
import { wishesReminderController } from "./WishesReminderController";
import { authorizationHeaderObj } from "@utils/validator";
import { failActionFunction } from "@utils/appUtils";

export const wishesReminderRoute = [
    {
        method: "POST",
        path:  `${SERVER.API_BASE_URL}/v1/app-invite-reminder`,
        handler: async (request: Request | any, h: ResponseToolkit) => {
            try {
                const data = await wishesReminderController.checkNotificationV2();
                const results = {
                    "statusCode": 200,
			        data
                };
                return responseHandler.sendSuccess(h, results);
            } catch (error) {
                return responseHandler.sendError(request, error);
            }
        },
        config: {
            // tags: ["api", "wishesreminder"],
            // description: "Checks reminder for tagwishes",
            // auth: {
            //     strategies: ["UserAuth"]
            // },
            // validate: {
            //     headers: authorizationHeaderObj,
            //     failAction: failActionFunction
            // },
            plugins: {
                "hapi-swagger": {
                    responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
                }
            }
        }
    },
    {
        method: "POST",
        path:  `${SERVER.API_BASE_URL}/v1/clear-invite-wishes`,
        handler: async (request: Request | any, h: ResponseToolkit) => {
            try {
                const data = await wishesReminderController.clearInviteWishesV2();
                const results = {
                    "statusCode": 200,
			        data
                };
                return responseHandler.sendSuccess(h, results);
            } catch (error) {
                return responseHandler.sendError(request, error);
            }
        },
        config: {
            // tags: ["api", "wishesreminder"],
            // description: "Checks reminder for tagwishes",
            // auth: {
            //     strategies: ["UserAuth"]
            // },
            // validate: {
            //     headers: authorizationHeaderObj,
            //     failAction: failActionFunction
            // },
            plugins: {
                "hapi-swagger": {
                    responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
                }
            }
        }
    }
];