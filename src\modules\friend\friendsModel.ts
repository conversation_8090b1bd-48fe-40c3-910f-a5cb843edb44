"use strict";

const mongoose = require("mongoose");
import { Document, model, Model, Schema } from "mongoose";
import { DB_MODEL_REF, STATUS, FRIEND_REQUEST_STATUS } from "@config/constant";

export interface IFriend extends Document {
    userId: string;
    friendId: {
        _id: string;
        name: string;
        profilePicture?: string;
        userType: string;
    };
    status: string;
    created: number;
}

const friendSchema: Schema = new mongoose.Schema({
    _id: { type: Schema.Types.ObjectId, required: true, auto: true },
    userId: { type: Schema.Types.ObjectId, required: true, ref: DB_MODEL_REF.USER },
    friendId: {
        _id: { type: Schema.Types.ObjectId, required: true, ref: DB_MODEL_REF.USER },
        name: { type: String, required: true },
        profilePicture: { type: String, required: false },
        userType: { type: String, required: true }
    },
    status: {
        type: String,
        enum: [
            FRIEND_REQUEST_STATUS.REQUEST_SENT,
            FRIEND_REQUEST_STATUS.REQUEST_PENDING,
            FRIEND_REQUEST_STATUS.REQUEST_ACCEPTED,
            FRIEND_REQUEST_STATUS.REQUEST_DECLINED,
            FRIEND_REQUEST_STATUS.UN_FRIEND
        ],
        default: FRIEND_REQUEST_STATUS.REQUEST_PENDING
    },
    created: { type: Number, default: Date.now }
}, {
    versionKey: false,
    timestamps: true
});

// Create indexes for better query performance
friendSchema.index({ userId: 1, 'friendId._id': 1 }, { unique: true });
friendSchema.index({ status: 1 });
friendSchema.index({ created: -1 });

// Export friends model
export const friends: Model<IFriend> = model<IFriend>(DB_MODEL_REF.FRIEND, friendSchema);