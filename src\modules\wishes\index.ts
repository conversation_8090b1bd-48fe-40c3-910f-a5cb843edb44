"use strict";
export { wishesController as wishesControllerV1 } from "./v1/WishesController";
export { wishesDao as wishesDaoV1 } from "./v1/WishesDao";
export { wishes } from "./wishesModel";
export { wishesRoute } from "./v1/wishesRoute";
export { tagwishes } from "./tagWishesModel";
export { wishesController as wishesControllerV2 } from "./v2/WishesController";
export { wishesDao as wishesDaoV2 } from "./v2/WishesDao";
export { wishesRoute as wishRouteV2 } from "./v2/wishesRoute";