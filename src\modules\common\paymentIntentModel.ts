"use strict";
const mongoose = require("mongoose");
import { Document, model, Model, Schema } from "mongoose";
import {
	DB_MODEL_REF,
	PAYMENT_STATUS
} from "@config/index";
// import { userControllerV1 } from "@modules/user/index";
export interface IPayementintent extends Document {
	userId: string;
	stripeCutomerId: string;
	paymentIntentid: string;
	amount: string;
	amount_capturable: string;
	client_secret: string;
	capture_method: string;
	currency: string;
	status: string;
	intent_status:string;
}
const paymentIntentSchema: Schema = new mongoose.Schema({
	_id: { type: Schema.Types.ObjectId, required: true, auto: true },
	userId: {type: Schema.Types.ObjectId, required: true},
	paymentIntentid: {type: String, required: false},
	stripeCutomerId: {type: String, required: false},
	amount: {type: Number, required: true},
	currency: {type: String, required: true},
	amount_capturable: {type: String, required: true},
	client_secret: {type: String, required: true},
	capture_method: {type: String, required: true},
	status: {type: String, default: PAYMENT_STATUS.INITIATE},
	intent_status: {type: String, default: PAYMENT_STATUS.CREATED},
	created: { type: Number, default: Date.now }
}, {
	versionKey: false,
	timestamps: true
});
paymentIntentSchema.index({ paymentIntentid: 1 });
// Export user
export const payment_intents: Model<IPayementintent> = model<IPayementintent>(DB_MODEL_REF.PAYMET_INTENT, paymentIntentSchema);