"use strict";

const mongoose = require("mongoose");
import { Document, model, Model, Schema } from "mongoose";
import { DB_MODEL_REF, STATUS, WISH_TAG_TYPE,WISH_TYPE } from "@config/constant";

export interface Reminders extends Document {
    tagWishId?: string;
    wishId?: string;
    wishType?: string;
    communityId?: string;
    communityName?: string;
    memberId?: string;
    countryCode?: string;
    phoneNumber?: string;
    isCommunityInvite?: boolean;
}

const remindersSchema: Schema = new mongoose.Schema({
    _id: {
        type: Schema.Types.ObjectId,
        required: true,
        auto: true,
    },
    tagWishId: {
        type: Schema.Types.ObjectId,
        required: false,
    },
    wishId: {
        type: Schema.Types.ObjectId,
        required: false,
    },
    wishType: {
        type: String,
        required: false
    },
    communityId: {
        type: Schema.Types.ObjectId,
        required: false,
    },
    communityName: {
        type: String,
        required: false,
    },
    memberId: {
        type: Schema.Types.ObjectId,
        required: false,
    },
    countryCode: {
        type: String,
        required: false,
    },
    phoneNumber: {
        type: String,
        required: false,
    },
    isCommunityInvite: {
        type: Boolean,
        required: true,
    },
    userId: {
        type: Schema.Types.ObjectId,
        required: true
    },
    status: {
        type: String,
        default: STATUS.ACTIVE
    },
    notificationReminder24Hours: { type: Date, default: () => new Date(Date.now() + 24 * 60 * 60 * 1000) }, // 24 hours from now
    notificationReminder72Hours: { type: Date, default: () => new Date(Date.now() + 72 * 60 * 60 * 1000) }, // 72 hours from now
    notificationReminder120Hours: { type: Date, default: () => new Date(Date.now() + 120 * 60 * 60 * 1000) }, // 120 hours from now
    notificationReminder24HoursStatus: { type: Boolean, default: false },
    notificationReminder72HoursStatus: { type: Boolean, default: false },
    notificationReminder120HoursStatus: { type: Boolean, default: false },
}, {
    versionKey: false,
    timestamps: true
});

// Export categories
export const reminders: Model<Reminders> = model<Reminders>(DB_MODEL_REF.REMINDERS, remindersSchema);