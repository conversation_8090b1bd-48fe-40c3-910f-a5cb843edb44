"use strict";
import { BaseDao, baseDao } from "@modules/baseDao/BaseDao";
import { STATUS, USER_TYPE, WISH_TAG_TYPE } from "@config/constant";
import { escapeSpecial<PERSON>haracter } from "@utils/appUtils";
import { toObjectId } from "@utils/appUtils";
export class WishesDao extends BaseDao {
	/**
	 * @function findGlobalWishById
	 */
	async findGlobalWishById(wishId: string, project = {}) {
		try {
			const query: any = {};
			query._id = toObjectId(wishId);
			query.isGlobalWish = true;
			const projection = (Object.values(project).length) ? project : { createdAt: 0, updatedAt: 0 };
			return await this.findOne("wishes", query, projection);
		} catch (error) {
			throw error;
		}
	}
	/**    
	 * @function findWishById
	 */
	async findWishById(wishId: string, project = {}) {
		try {
			const query: any = {};
			query._id = toObjectId(wishId);
			query.status = { '$nin': [STATUS.DELETED, STATUS.IN_ACTIVE, STATUS.UN_PUBLISHED, STATUS.REJECTED] };
			const projection = (Object.values(project).length) ? project : { createdAt: 0, updatedAt: 0 };
			return await this.findOne("wishes", query, projection);
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function addWishes
	 */
	async addWishes(params: WishesRequest.Add) {
		try {
			return await this.save("wishes", params);
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function addTagWishes
	 */
	async addTagWishes(params) {
		try {
			return await this.insertMany("tagwishes", params, {});
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function wellLogSave
	 */
	async wellLogSave(params) {
		try {
			return await this.insertMany("well_logs", params, {});
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function getCohosts
	 */
	async getCohosts(cohosts) {
		try {
			const query: any = {};
			query['_id'] = { '$in': cohosts }
			query['status'] = { '$nin': [STATUS.DELETED, STATUS.IN_ACTIVE, STATUS.UN_PUBLISHED] }
			const projection = { _id: 1, name: 1, firstName: 1, lastName: 1, profilePicture: 1, phoneNumber: 1, countryCode: 1 };
			return await this.find("users", query, projection);
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function getTagWishes
	 */
	async getPublicWishes(params: WishesRequest.PublicWishList, reportedWishIds: string[]) {
		try {
			const aggPipe: any[] = [];
	
			// Base filters
			const match: any = {
				visibility: "PUBLIC",
				status: STATUS.ACTIVE,
				_id: { $nin: reportedWishIds }
			};
	
			// If searchKey exists
			if (params.searchKey) {
				const searchRegex = new RegExp(escapeSpecialCharacter(params.searchKey), "i");
				match["description"] = { $regex: searchRegex };
			}
	
			aggPipe.push({ "$match": match });
	
			// Lookup host details
			aggPipe.push({
				"$lookup": {
					"from": "users",
					"let": { "hostId": "$hostId" },
					"pipeline": [
						{
							"$match": {
								"$expr": { "$eq": ["$_id", "$$hostId"] }
							}
						},
						{
							"$project": {
								_id: 0,
								profilePicture: 1,
								firstName: 1,
								lastName: 1
							}
						}
					],
					"as": "hostDetail"
				}
			});
	
			aggPipe.push({
				"$unwind": {
					"path": "$hostDetail",
					"preserveNullAndEmptyArrays": true
				}
			});
	
			// Project fields we need
			aggPipe.push({
				"$project": {
					_id: 1,
					title: 1,
					description: 1,
					created: 1,
					intension: 1,
					image: 1,
					wishType: 1,
					isGlobalWish: 1,
					status: 1,
					visibility: 1,
					userDetail: 1,
					hostDetail: 1,
					totalBlessings: 1,
					location: 1
				}
			});
	
			aggPipe.push({ "$sort": { created: -1 } });
	
			const options = {}; // no additional options for now
	
			const response = await this.paginate("wishes", aggPipe, params.limit, params.pageNo, options, true);
	
			return response;
		} catch (error) {
			console.error("❌ Error in getPublicWishes DAO:", error);
			throw error;
		}
	}
	
}


export const wishesDao = new WishesDao();