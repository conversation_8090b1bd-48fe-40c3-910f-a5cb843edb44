"use strict";

import { BaseDao,baseDao } from "@modules/baseDao/BaseDao";
import { STATUS,FLAG_TYPE, REPORT_TYPE, DB_MODEL_REF, REPORTED_POST_SORT_CRITERIA } from "@config/constant";
import { escapeSpecial<PERSON>haracter } from "@utils/appUtils";
import { toObjectId } from "@utils/appUtils";
export class ReportDao extends BaseDao {

    /**
	 * @function reportWish
	 */
	async reportWish(params,gratitudeId?) {
		try {
			if(gratitudeId) {
				params.subjectId = toObjectId(gratitudeId);
				params.type=REPORT_TYPE.GRATITUDE;
			}
			return await this.save("reports", params);
		} catch (error) {
			throw error;
		}
	}

    /**
	 * @function flagWish
	 */
	async flagWish(params) {
		try {
			const query: any = {};
			const update: any = {};

			query["_id"] = params.subjectId;
			update["$set"] = {
				isFlagged: true,
				flagStatus: FLAG_TYPE.FLAGGED,
			}
            update["$inc"] = {
                reportCount: 1
            }
			return this.updateOne("wishes", query, update, {});
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function reportPinnedWish
	 */
	async reportPinnedWish(params: ReportRequest.ReportWish, userId: string) {
		try {
			const query: any = {};
			const update: any = {};

			query["userId"] = toObjectId(userId);
			query["subjectId"] = params.subjectId;
			query["status"] = STATUS.ACTIVE;
			update["$set"] = {
				status: STATUS.REPORTED
			};
			return this.updateMany("pinned_wishes", query, update, {});
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function reportFavouriteWish
	 */
	async reportFavouriteWish(params, userId: string) {
		try {
			const query: any = {};
			const update: any = {};

			query["userId"] = toObjectId(userId);
			query["wishId"] = params.subjectId;
			query["status"] = STATUS.ACTIVE;
			update["$set"] = {
				status: STATUS.REPORTED
			};
			return this.updateMany("favourites", query, update, {});
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function flagCommunity
	 */
	async flagCommunity(params) {
		try {
			const query: any = {};
			const update: any = {};

			query["_id"] = params.subjectId;
			update["$set"] = {
				isFlagged: true,
				flagStatus: FLAG_TYPE.FLAGGED,
			}
            update["$inc"] = {
                reportCount: 1
            }
			return this.updateOne("communities", query, update, {});
		} catch (error) {
			throw error;
		}
	}

	  /**
	 * @function flagBlessing
	 */
	  async flagBlessing(params) {
		try {
			const query: any = {};
			const update: any = {};

			query["_id"] = params.subjectId;
			update["$set"] = {
				isFlagged: true,
				flagStatus: FLAG_TYPE.FLAGGED,
			}
            update["$inc"] = {
                reportCount: 1
            }
			return this.updateOne("perform_blessings", query, update, {});
		} catch (error) {
			throw error;
		}
	}

	 /**
	 * @function flagGratitude
	 */
	 async flagGratitude(params,subjectId?) {
		try {
			const query: any = {};
			const update: any = {};

			query["_id"] = subjectId;
			update["$set"] = {
				isFlagged: true,
				flagStatus: FLAG_TYPE.FLAGGED,
			}
            update["$inc"] = {
                reportCount: 1
            }
			return this.updateOne("gratitudes", query, update, {});
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function reportedPosts
	 */
	async reportedPosts(params: ReportRequest.ReportedPosts) {
		try {
			let match: any = {};
			let aggPipe: any = [];
			let sort: any = {};

			match["ownerId"] = toObjectId(params.id);
			match["type"] = { $in: [ REPORT_TYPE.WISH, REPORT_TYPE.BLESSING, REPORT_TYPE.GRATITUDE ] };

			const wishLookup = {
				from: DB_MODEL_REF.WISHES,
				localField: "subjectId",
				foreignField: "_id",
				as: "wishDetail"
			}

			const blessingLookup = {
				from: DB_MODEL_REF.PERFORM_BLESSING,
				localField: "subjectId",
				foreignField: "_id",
				as: "blessingDetail"
			}

			const gratitudeLookup = {
				from: DB_MODEL_REF.GRATITUDES,
				localField: "subjectId",
				foreignField: "_id",
				as: "gratitudeDetail"
			}

			const project = {
				_id: 1,
				type: 1,
				subjectId: 1,
				description: 1,
				reportType: 1,
				createdAt: 1,
				"wishDetail.wishNumber": 1,
				"blessingDetail.blessingNumber": 1,
				"gratitudeDetail.gratitudeNumber": 1,
				"wishDetail.flagStatus": 1,
				"blessingDetail.flagStatus": 1,
				"gratitudeDetail.flagStatus": 1,
				"wishDetail.deleteReason": 1,
				"blessingDetail.deleteReason": 1,
				"gratitudeDetail.deleteReason": 1,
				"wishDetail.deletedAt": 1,
				"blessingDetail.deletedAt": 1,
				"gratitudeDetail.deletedAt": 1,
			}

			const group = {
				_id: {
					userId: "$userId",
					type: "$type"
				},
				reportCount: { $sum: 1 },
				subjectId: { $last: "$subjectId" },
				type: { $last: "$type" },
				createdAt: { $last: "$createdAt" },
				description: { $last: "$description" },
				reportType: { $last: "$reportType" },
				postDetail: { $last: { $ifNull: ["$wishDetail", { $ifNull: ["$blessingDetail", "$gratitudeDetail"] }] } }
			}

			aggPipe.push({ "$match": match });
			aggPipe.push(
				{ "$lookup": wishLookup },
				{ "$lookup": blessingLookup },
				{ "$lookup": gratitudeLookup }
			);
			aggPipe.push(
				{ "$unwind": { "path": "$wishDetail", "preserveNullAndEmptyArrays": true } },
				{ "$unwind": { "path": "$blessingDetail", "preserveNullAndEmptyArrays": true } },
				{ "$unwind": { "path": "$gratitudeDetail", "preserveNullAndEmptyArrays": true } }
			);
			aggPipe.push({ "$project": project });
			aggPipe.push({ "$group": group });
			aggPipe.push({
				"$addFields": {
					"postDetail.postNumber": {
						$cond: {
							if: { $ifNull: ["$postDetail.wishNumber", false] },
							then: "$postDetail.wishNumber",
							else: {
								$cond: {
									if: { $ifNull: ["$postDetail.gratitudeNumber", false] },
									then: "$postDetail.gratitudeNumber",
									else: "$postDetail.blessingNumber"
								}
							}
						}
					},
					"flagStatus": "$postDetail.flagStatus"
				}
			});
			aggPipe.push({ "$project": { "_id": 0, "postDetail.wishNumber": 0, "postDetail.blessingNumber": 0, "postDetail.gratitudeNumber": 0, "postDetail.flagStatus": 0 } })

			if (params.sortBy == 1) {
				params.sortBy = 1;
			} else {
				params.sortBy = -1;
			}

			if(params.sortCriteria == REPORTED_POST_SORT_CRITERIA.POST_NUMBER) {
				sort = { "postDetail.postNumber": params.sortBy }
			} else if (params.sortCriteria == REPORTED_POST_SORT_CRITERIA.TYPE) {
				sort = { "type": params.sortBy }
			} else if (params.sortCriteria == REPORTED_POST_SORT_CRITERIA.STATUS) {
				sort = { "flagStatus": params.sortBy }
			} else {
				sort = { "createdAt": params.sortBy }
			}

			aggPipe.push({ "$sort": sort });
			return this.paginate("reports", aggPipe, params.limit, params.pageNo, {}, true);
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function manageUserReportCount
	 */
	async manageUserReportCount(params: ReportRequest.Report,gratitudeId?) {
		try {
			const query: any = {};
			const update: any = {};

			query["_id"] = params.userId;

			if(params.type == REPORT_TYPE.WISH) {
				update["$inc"] = { wishReportCount: 1 }
			} else if (params.type == REPORT_TYPE.BLESSING) {
				if(gratitudeId){
					query["_id"] = gratitudeId;
					update["$inc"] = { gratitudeReportCount: 1 }
				}else{
					update["$inc"] = { blessingReportCount: 1 }
				}
			} else if (params.type == REPORT_TYPE.COMMUNITY) {
				update["$inc"] = { communityReportCount: 1 }
			} else {
				update["$inc"] = { gratitudeReportCount: 1 }
			}
			return this.updateOne("users", query, update, {});
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function reportedWishes
	 * @description here are getting the reported wishes.
	 */
	async reportedWishes(type,userId) {
		try {
			let reportedIds = await baseDao.distinct("reports","subjectId", { type: type, status: STATUS.REPORTED, userId: toObjectId(userId) });
			return reportedIds;
		} catch (error) {
			throw error;
		}
	}
	
}

export const reportDao = new ReportDao();