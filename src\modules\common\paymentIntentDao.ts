"use strict";
import { BaseDao } from "@modules/baseDao/BaseDao";
export class PaymentIntentDao extends BaseDao {

	/**
	 * @function createPaymentIntent
	 */
	async createPaymentIntent(params) {
		try {
			return await this.save("payment_intents", params);
		} catch (error) {
			throw error;
		}
	}

	/**
	* @function createPaymentLogs
	*/
		async createPaymentLogs(params) {
			try {
				return await this.save("payment_webhooks", params);
			} catch (error) {
				throw error;
			}
		}


	/**
	* @function getPaymentintent
	*/
		async getPaymentIntent(params) {
			try {
				let query ={
					"paymentIntentid":params.paymentIntentid
				}
				return await this.findOne("payment_intents", query, {});
			} catch (error) {
				throw error;
			}
		}

	/**
	* @function updatePaymentIntent
	*/
		async updatePaymentIntent(query, params) {
			try {
				return await this.updateOne("payment_intents", query, params, {});
			} catch (error) {
				throw error;
			}
		}

}

export const paymentIntentDao = new PaymentIntentDao();