"use strict";

const mongoose = require("mongoose");
import { Document, model, Model, Schema } from "mongoose";

import { DB_MODEL_REF, STATUS } from "@config/constant";

export interface IAdminNotification extends Document {
	
	message: string;
	status: string;
	created: number;
    title: string;
    platform: string;
    image: string;

}

const adminNotificationSchema: Schema = new mongoose.Schema({
    _id: { type: Schema.Types.ObjectId, required: true, auto: true },
	title: { type: String, required: true },
	platform: { type: String, required: true },
	message: { type: String, required: false },
    image:  { type: String, required: false },
	status: {
		type: String,
		required: true
	},
	created: { type: Number, default: Date.now }
}, {
	versionKey: false,
	timestamps: true
});

adminNotificationSchema.index({ title: 1 });
adminNotificationSchema.index({ platform: 1 });

// Export notification schema
export const admin_notifications: Model<IAdminNotification> = model<IAdminNotification>(DB_MODEL_REF.ADMIN_NOTIFICATION, adminNotificationSchema);