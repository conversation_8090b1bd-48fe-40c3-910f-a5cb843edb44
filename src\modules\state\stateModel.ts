"use strict";

import * as mongoose from "mongoose";
import {  Schema, Model, Document } from "mongoose";

import { DB_MODEL_REF } from "@config/index";

export interface IState extends Document {
    state_id: string,
    country_code: string,
    state_code: string,
    state_name: string,
}

const stateSchema = new Schema({
    _id: { type: mongoose.Schema.Types.ObjectId, required: true, auto: true },
    state_id: { type: String, required: true },
    state_code: { type: String, default: '' },
    state_name: { type: String, default: '' },
    country_code: { type: String, default: '' }
}, {
    versionKey: false,
    timestamps: true,

});

// Export states
export const states: Model<IState> = mongoose.model<IState>(DB_MODEL_REF.STATES, stateSchema);