"use strict";
import {
	FIELD_REQUIRED as EN_FIELD_REQUIRED,
	SERVER_IS_IN_MAINTENANCE as EN_SERVER_IS_IN_MAINTENANCE,
	LINK_EXPIRED as EN_LINK_EXPIRED,
	USER_DEACTIVATED_BY_ADMIN as EN_USER_DEACTIVATED_BY_ADMIN
} from "../../locales/en.json";
const SWAGGER_DEFAULT_RESPONSE_MESSAGES = [
	{ code: 200, message: "OK" },
	{ code: 400, message: "Bad Request" },
	{ code: 401, message: "Unauthorized" },
	{ code: 404, message: "Data Not Found" },
	{ code: 500, message: "Internal Server Error" }
];
import { SERVER } from "../config/environment";
const NOTIFICATION_DATA = {
	INCIDENT_REPORT: (userName, eventId, senderId, msg) => {
		return {
			"type": NOTIFICATION_TYPE.INCIDENT_REPORT,
			"activityId": eventId,
			"senderId": senderId,
			"message": msg,
			"body": NOTIFICATION_TITLE.INCIDENT_REPORT,
			"title": NOTIFICATION_TITLE.INCIDENT_REPORT
		};
	},
	NOTES_ADD: (userName, activityId, userId, tokenData, activityType, params) => {
		return {
			"type": NOTIFICATION_TYPE.ADD_NOTES,
			"activityId": activityId,
			"senderUserType": tokenData.userType,
			"senderUserName": userName,
			"senderUserProfilePic": tokenData.profilePicture,
			"senderId": tokenData.userId,
			"receiverId": userId,
			"activityType": activityType,
			"message": NOTIFICATION_MSG.ADDED_NOTES,
			"body": NOTIFICATION_MSG.ADDED_NOTES,
			"title": NOTIFICATION_TITLE.ADD_NOTES
		};
	},
	BROADCAST_NOTIFICATION:(userId,productId,wish)=>{

	},
	ADD_EDIT_EVENT:(userId,productId,wish,tag)=>{
	
		
	},
	
}

const HTTP_STATUS_CODE = {
	OK: 200,
	CREATED: 201,
	UPDATED: 202,
	NO_CONTENT: 204,
	BAD_REQUEST: 400,
	UNAUTHORIZED: 401,
	PAYMENY_REQUIRED: 402,
	ACCESS_FORBIDDEN: 403,
	FAV_USER_NOT_FOUND: 403,
	URL_NOT_FOUND: 404,
	METHOD_NOT_ALLOWED: 405,
	UNREGISTERED: 410,
	PAYLOAD_TOO_LARGE: 413,
	CONCURRENT_LIMITED_EXCEEDED: 429,
	// TOO_MANY_REQUESTS: 429,
	INTERNAL_SERVER_ERROR: 500,
	BAD_GATEWAY: 502,
	SHUTDOWN: 503,
	EMAIL_NOT_VERIFIED: 430,
	MOBILE_NOT_VERIFIED: 431,
	FRIEND_REQUEST_ERR: 432,
	INVALID_RESET_PASSWORD_TOKEN: 400

};

const USER_TYPE = {
	ADMIN: "ADMIN",
	SUB_ADMIN: "SUB_ADMIN",
	INVESTOR: "INVESTOR",
	USER: "USER",
};

const COMPASSION_MAP = {
	MAX_CIRCLE_SIZE: 20,
	MIN_CIRCLE_SIZE: 2,
	MAX_COUNT:10000
}

const DB_MODEL_REF = {
	ADMIN: "admins",
	CATEGORIES: "categories",
	CONTENT: "contents",
	LOGIN_HISTORY: "login_histories",
	NOTIFICATION: "notifications",
	VERSION: "versions",
	USER: "users",
	ROLE: "roles",
	ADMIN_NOTIFICATION: "admin_notifications",
	ADMIN_BANNER: "admin_banners",
	WISHES: "wishes",
	CONTACT: "contacts",
	CONTACTV2: "contacts_v2",
	BLESSING_LIBRARY: "blessing_library",
	TAGWISHES:"tagwishes",
	FAVOURITES:"favourites",
	PINNED_WISHES:"pinned_wishes",
	REPORTS: "reports",
	COUNTRIES:"countries",
	CITIES: "cities",
	STATES: "states",
	COMMUNITIES: "communities",
	MEMBERS:"members",
	PERFORM_BLESSING:"perform_blessings",
	WELL_LOGS:"well_logs",
	GRATITUDES:"gratitudes",
	WISHWELL_THANKS:"wishwell_thanks",
	PAYMET_INTENT: "payment_intents",
	PAYMET_WEBHOOK: "payment_webhooks",
	LOCATIONS:"locations",
	MOST_USED_LOCATIONS:"most_used_locations",
	TIMEZONE:"timezones",
	REMINDERS:"reminders",
	DISASTER: "disasters",
	DISASTER_CATEGORIES: "disaster_categories",
	FRIEND: "friends"
};
const MODULES = {
	BANNER_MANAGEMENT: "Banner Management",
	NOTIFICATION_MANAGEMENT: "Notification Management",
	CONTENT_MANAGEMENT: "Content Management"
};

const MODULES_ID = {
	BANNER_MANAGEMENT: "1",
	NOTIFICATION_MANAGEMENT: "2",
	CONTENT_MANAGEMENT: "3"
}
const DEVICE_TYPE = {
	ANDROID: "1",
	IOS: "2",
	WEB: "3",
	ALL: "4"
};
const GENDER = {
	MALE: "MALE",
	FEMALE: "FEMALE",
	OTHER: "OTHER"
};
const CATEGORIES_STAUS = {
	ADMIN: "ADMIN",
	USER: "USER"
};
const STATUS = {
	BLOCKED: "BLOCKED",
	UN_BLOCKED: "UN_BLOCKED",
	ACTIVE: "ACTIVE",
	DELETED: "DELETED",
	IN_ACTIVE: "IN_ACTIVE",
	UN_PUBLISHED: "UN_PUBLISHED",
	REJECTED: "REJECTED",
	PENDING:"PENDING",
	CLOSED:"CLOSED",
	REPORTED:"REPORTED",
	DECLINED:"DECLINED"
};
const VALIDATION_CRITERIA = {
	FIRST_NAME_MIN_LENGTH: 3,
	FIRST_NAME_MAX_LENGTH: 10,
	MIDDLE_NAME_MIN_LENGTH: 3,
	MIDDLE_NAME_MAX_LENGTH: 10,
	LAST_NAME_MIN_LENGTH: 3,
	LAST_NAME_MAX_LENGTH: 10,
	NAME_MIN_LENGTH: 3,
	COUNTRY_CODE_MIN_LENGTH: 1,
	COUNTRY_CODE_MAX_LENGTH: 4,
	PASSWORD_MIN_LENGTH: 8,
	PASSWORD_MAX_LENGTH: 40,
	LATITUDE_MIN_VALUE: -90,
	LATITUDE_MAX_VALUE: 90,
	LONGITUDE_MIN_VALUE: -180,
	LONGITUDE_MAX_VALUE: 180
};
const NOTIFICATION_MSG = {
	ADDED_NOTES: "Check supporters activity notes",
	
}
const NOTIFICATION_TITLE = {
	ADD_NOTES: "Add Notes",
	SUPPORT_CHAT: "Support Chat",
	INCIDENT_REPORT:"Incident Report"

	
}
const VALIDATION_MESSAGE = {
	invalidId: {
		pattern: "Invalid Id."
	},
	mobileNo: {
		pattern: "Please enter a valid mobile number."
	},
	email: {
		pattern: "Please enter email address in a valid format."
	},
	password: {
		required: "Please enter password.",
		pattern: "Please enter a valid password.",
		// pattern: `Please enter a proper password with minimum ${VALIDATION_CRITERIA.PASSWORD_MIN_LENGTH} character, which can be alphanumeric with special character allowed.`,
		minlength: `Password must be between ${VALIDATION_CRITERIA.PASSWORD_MIN_LENGTH}-${VALIDATION_CRITERIA.PASSWORD_MAX_LENGTH} characters.`,
		// maxlength: `Please enter a proper password with minimum ${VALIDATION_CRITERIA.PASSWORD_MIN_LENGTH} character, which can be alphanumeric with special character allowed.`
		maxlength: `Password must be between ${VALIDATION_CRITERIA.PASSWORD_MIN_LENGTH}-${VALIDATION_CRITERIA.PASSWORD_MAX_LENGTH} characters.`
	}
};
const MESSAGES = {
	ERROR: {
		UNAUTHORIZED_ACCESS: {
			"statusCode": HTTP_STATUS_CODE.UNAUTHORIZED,
			"type": "UNAUTHORIZED_ACCESS"
		},
		INTERNAL_SERVER_ERROR: {
			"statusCode": HTTP_STATUS_CODE.INTERNAL_SERVER_ERROR,
			"type": "INTERNAL_SERVER_ERROR"
		},
		BAD_TOKEN: {
			"statusCode": HTTP_STATUS_CODE.UNAUTHORIZED,
			"type": "BAD_TOKEN"
		},
		TOKEN_EXPIRED: {
			"statusCode": HTTP_STATUS_CODE.UNAUTHORIZED,
			"type": "TOKEN_EXPIRED"
		},
		TOKEN_GENERATE_ERROR: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "TOKEN_GENERATE_ERROR"
		},
		BLOCKED: {
			"statusCode": HTTP_STATUS_CODE.UNAUTHORIZED,
			"type": "BLOCKED"
		},
		INCORRECT_PASSWORD: {
			"statusCode": HTTP_STATUS_CODE.ACCESS_FORBIDDEN,
			"type": "INCORRECT_PASSWORD"
		},
		INCORRECT_PASSWORD_USER: {
			"statusCode": HTTP_STATUS_CODE.ACCESS_FORBIDDEN,
			"type": "INCORRECT_PASSWORD_USER"
		},
		LIMIT_EXCEEDED: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "LIMIT_EXCEEDED"
		},
		BLOCKED_MOBILE: {
			"statusCode": HTTP_STATUS_CODE.UNAUTHORIZED,
			"type": "BLOCKED_MOBILE"
		},
		SESSION_EXPIRED: {
			"statusCode": HTTP_STATUS_CODE.UNAUTHORIZED,
			"type": "SESSION_EXPIRED"
		},
		FAV_USER_NOT_FOUND: {
			"statusCode": HTTP_STATUS_CODE.FAV_USER_NOT_FOUND,
			"type": "FAV_NOT_FOUND"
		},
		INVALID_RESET_PASSWORD_TOKEN: {
			"statusCode": HTTP_STATUS_CODE.INVALID_RESET_PASSWORD_TOKEN,
			"type": "INVALID_RESET_PASSWORD_TOKEN"
		},
		ERROR: (value, code = HTTP_STATUS_CODE.BAD_REQUEST) => {
			return {
				"statusCode": code,
				"message": value,
				"type": "ERROR"
			};
		},
		FRIEND_ERROR: (value, code = HTTP_STATUS_CODE.FRIEND_REQUEST_ERR) => {
			return {
				"statusCode": code,
				"message": value,
				"type": "ERROR"
			};
		},
		FIELD_REQUIRED: (value, lang = "en") => {
			return {
				"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
				"message": lang === "en" ? EN_FIELD_REQUIRED.replace(/{value}/g, value) : EN_FIELD_REQUIRED.replace(/{value}/g, value),
				"type": "FIELD_REQUIRED"
			};
		},
		SOMETHING_WENT_WRONG: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "SOMETHING_WENT_WRONG"
		},
		SERVER_IS_IN_MAINTENANCE: (lang = "en") => {
			return {
				"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
				"message": lang === "en" ? EN_SERVER_IS_IN_MAINTENANCE : EN_SERVER_IS_IN_MAINTENANCE,
				"type": "SERVER_IS_IN_MAINTENANCE"
			};
		},
		LINK_EXPIRED: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"message": EN_LINK_EXPIRED,
			"type": "LINK_EXPIRED"
		},
		EMAIL_NOT_REGISTERED: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "EMAIL_NOT_REGISTERED"
		},
		USER_EMAIL_NOT_REGISTERED: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "USER_EMAIL_NOT_REGISTERED"
		},
		EMAIL_ALREADY_EXIST: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "EMAIL_ALREADY_EXIST"
		},
		EMAIL_REQUIRED: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "EMAIL_REQUIRED"
		},
		EMAIL_NOT_VERIFIED: (code = HTTP_STATUS_CODE.BAD_REQUEST) => {
			return {
				"statusCode": code,
				"type": "EMAIL_NOT_VERIFIED"
			}
		},
		USER_DELETED_BY_ADMIN:{
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "USER_DELETED_BY_ADMIN"
		},
		USER_DEACTIVATED_BY_ADMIN: (value, lang = "en") => {
			return {
				"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
				"message": lang === "en" ? EN_USER_DEACTIVATED_BY_ADMIN.replace(/{value}/g, value) : EN_USER_DEACTIVATED_BY_ADMIN.replace(/{value}/g, value),
				"type": "USER_DEACTIVATED_BY_ADMIN"
			};
		},
		INVALID_OLD_PASSWORD: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "INVALID_OLD_PASSWORD"
		},
		NEW_CONFIRM_PASSWORD: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "NEW_CONFIRM_PASSWORD"
		},
		SAME_OLD_PASSWORD: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "SAME_OLD_PASSWORD"
		},
		OTP_EXPIRED: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "OTP_EXPIRED"
		},
		INVALID_OTP: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "INVALID_OTP"
		},
		// user specific
		USER_NOT_FOUND: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "USER_NOT_FOUND"
		},
		PASSWORD_NOT_MATCH: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "PASSWORD_NOT_MATCH"
		},
		INVALID_CREDENTIALS: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "INVALID_CREDENTIALS"
		},
		REGISTRATION_PENDING: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "REGISTRATION_PENDING"
		},
		ADD_WISH_ATLEAST_ONE_CONTACT_OR_COMMUNITY: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "ADD_WISH_ATLEAST_ONE_CONTACT_OR_COMMUNITY"
		},
		ADD_WISH_ATLEAST_ONE_COMMUNITY: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "ADD_WISH_ATLEAST_ONE_COMMUNITY"
		},
		ADD_WISH_ATLEAST_ONE_CONTACT: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "ADD_WISH_ATLEAST_ONE_CONTACT"
		},
		EDIT_WISH_DUPLICATE_CONTACT: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "EDIT_WISH_DUPLICATE_CONTACT"
		},
		EDIT_WISH_DUPLICATE_COHOST: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "EDIT_WISH_DUPLICATE_COHOST"
		},
		WISH_NOT_FOUND: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "WISH_NOT_FOUND"
		},
		WISH_ALREADY_ADDED: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "WISH_ALREADY_ADDED"
		},
		WISH_ALREADY_REPORTED: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "WISH_ALREADY_REPORTED"
		},
		WISH_DOES_NOT_EXIST: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "WISH_DOES_NOT_EXIST"
		},
		COMMUNITY_NOT_FOUND: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "COMMUNITY_NOT_FOUND"
		},
		BLESSING_NOT_FOUND: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "BLESSING_NOT_FOUND"
		},
		GRATITUDE_NOT_FOUND: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "GRATITUDE_NOT_FOUND"
		},
		COMMUNITY_ALREADY_REPORTED: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "COMMUNITY_ALREADY_REPORTED"
		},
		BLESSING_ALREADY_REPORTED: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "BLESSING_ALREADY_REPORTED"
		},
		ALREADY_REPORTED: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "ALREADY_REPORTED"
		},
		COMMUNITY_DOES_NOT_EXIST: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "COMMUNITY_DOES_NOT_EXIST"
		},
		HOST_NOT_FOUND: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "HOST_NOT_FOUND"
		},
		PROFILE_NOT_COMPLETED: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "PROFILE_NOT_COMPLETED"
		},
		USER_DOES_NOT_EXIST: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "USER_DOES_NOT_EXIST"
		},
		DOCUMENT_NOT_APPROVED: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "DOCUMENT_NOT_APPROVED"
		},
		MOBILE_NO_NOT_VERIFIED: {
			"statusCode": HTTP_STATUS_CODE.MOBILE_NOT_VERIFIED,
			"type": "MOBILE_NO_NOT_VERIFIED"
		},
		MOBILE_NO_ALREADY_EXIST: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "MOBILE_NO_ALREADY_EXIST"
		},
		// content specific
		CONTENT_ALREADY_EXIST: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "CONTENT_ALREADY_EXIST"
		},
		CONTENT_NOT_FOUND: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "CONTENT_NOT_FOUND"
		},
		FAQ_ALREADY_EXIST: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "FAQ_ALREADY_EXIST"
		},
		// interest specific
		INTEREST_ALREADY_EXIST: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "INTEREST_ALREADY_EXIST"
		},
		INTEREST_NOT_FOUND: {
			statusCode: HTTP_STATUS_CODE.URL_NOT_FOUND,
			type: "INTEREST_NOT_FOUND"
		},
		CANT_BLOCK_INTEREST: {
			statusCode: HTTP_STATUS_CODE.BAD_REQUEST,
			type: "CANT_BLOCK_INTEREST"
		},
		// categories specific
		CATEGORY_ALREADY_EXIST: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "CATEGORY_ALREADY_EXIST"
		},
		CATEGORY_NOT_FOUND: {
			statusCode: HTTP_STATUS_CODE.URL_NOT_FOUND,
			type: "CATEGORY_NOT_FOUND"
		},
		CANT_BLOCK_CATEGORY: {
			statusCode: HTTP_STATUS_CODE.BAD_REQUEST,
			type: "CANT_BLOCK_CATEGORY"
		},
		// version Specific
		VERSION_ALREADY_EXIST: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "VERSION_ALREADY_EXIST"
		},
		INVALID_ADMIN: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "INVALID_ADMIN"
		},
		ROLE_ALREADY_EXIST: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "ROLE_ALREADY_EXIST"
		},
		INVALID_ROLE_ID: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "INVALID_ROLE_ID"
		},
		INVALID_SUB_ADMIN: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "INVALID_SUB_ADMIN"
		},
		ROLE_IS_BLOCKED: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "ROLE_IS_BLOCKED"
		},
		INVALID_WISH_ID: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "INVALID_WISH_ID"
		},
		DELETE_WISH_NOT_ALLOWED: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "DELETE_WISH_NOT_ALLOWED"
		},
		INVALID_USER_ID: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "INVALID_USER_ID"
		},
		COUNTRY_ID_NOT_FOUND: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "COUNTRY_ID_NOT_FOUND"
		},
		STATE_ID_NOT_FOUND: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "STATE_ID_NOT_FOUND"
		},
		PHONE_NUMBER_ALREADY_EXISTS: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "PHONE_NUMBER_ALREADY_EXISTS"
		},
		CANNOT_DELETE_INVESTOR: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "CANNOT_DELETE_INVESTOR"
		},
		BLESSING_ALREADY_PERFORM: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "BLESSING_ALREADY_PERFORM"
		},
		INVALID_GUIDED_BLESSING: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "INVALID_GUIDED_BLESSING"
		},
		GUIDED_BLESSING_UPDATE_INVALID: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "GUIDED_BLESSING_UPDATE_INVALID"
		},
		GUIDED_BLESSING_DELETE_INVALID: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "GUIDED_BLESSING_DELETE_INVALID"
		},
		GUIDED_BLESSING_STATUS_INVALID: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "GUIDED_BLESSING_STATUS_INVALID"
		},
		INVALID_PERFORM_BLESSING: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "INVALID_PERFORM_BLESSING"
		},
		INVESTOR_DELETED_BY_ADMIN: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "INVESTOR_DELETED_BY_ADMIN"
		},
		INVESTOR_DEACTIVATED_BY_ADMIN: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "INVESTOR_DEACTIVATED_BY_ADMIN"
		},
		WISH_PUBLISH_INVALID: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "WISH_PUBLISH_INVALID"
		},
		WISH_PIN_NOT_ACTIVE: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "WISH_PIN_NOT_ACTIVE"
		},
		WISH_PIN_COUNT_EXCEED: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "WISH_PIN_COUNT_EXCEED"
		},
		INVALID_FLAGGED_GRATITUDE: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "INVALID_FLAGGED_GRATITUDE"
		},
		PASSWORD_ALREADY_SET: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "PASSWORD_ALREADY_SET"
		},
		PASSWORD_NOT_SET:{
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "PASSWORD_NOT_SET"
		},
		EMAIL_NOT_VERIFIED_YET:{
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "EMAIL_NOT_VERIFIED_YET"
		},
		PASSWORD_NOT_SET_YET:{
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "PASSWORD_NOT_SET_YET"
		},
		LOCATION_NOT_FOUND:{
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "LOCATION_NOT_FOUND"
		},
		FRIEND_REQUEST_ALREADY_SENT: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "FRIEND_REQUEST_ALREADY_SENT"
		},
		FRIEND_REQUEST_NOT_FOUND: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"type": "FRIEND_REQUEST_NOT_FOUND"
		},
		NOT_FRIENDS: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"message": "You are not friends with this user",
			"type": "NOT_FRIENDS"
		},
		COMMUNITY_NOT_PUBLIC: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"message": "This community is not public and does not accept join requests",
			"type": "COMMUNITY_NOT_PUBLIC"
		},
		ALREADY_MEMBER: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"message": "You are already a member of this community",
			"type": "ALREADY_MEMBER"
		},
		JOIN_REQUEST_ALREADY_SENT: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"message": "You have already sent a join request to this community",
			"type": "JOIN_REQUEST_ALREADY_SENT"
		},
		JOIN_REQUEST_NOT_FOUND: {
			"statusCode": HTTP_STATUS_CODE.BAD_REQUEST,
			"message": "Join request not found",
			"type": "JOIN_REQUEST_NOT_FOUND"
		},
		NOT_AUTHORIZED: {
			"statusCode": HTTP_STATUS_CODE.UNAUTHORIZED,
			"message": "You are not authorized to perform this action",
			"type": "NOT_AUTHORIZED"
		},
	},
	SUCCESS: {
		DEFAULT: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "DEFAULT"
		},
		DETAILS: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				"type": "DEFAULT",
				"data": data
			};
		},
		LIST: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				"type": "DEFAULT",
				...data
			};
		},
		SET_NEW_PASSWORD: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				"type": "SET_NEW_PASSWORD",
				"data": data
			};
		},
		CREATE_BLESSING: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				"type": "CREATE_BLESSING",
				"data": data
			};
		},
		BLESSING_LIST: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				"type": "BLESSING_LIST",
				"data": data
			};
		},
		CREATE_WISH: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				"type": "CREATE_WISH",
				"data": data
			};
		},
		COMMON_DROPDOWN: (data) => {
			return {
			  statusCode: HTTP_STATUS_CODE.OK,
			  type: "COMMON_DROPDOWN",
			  data: data,
			};
		},
		SEND_OTP: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "SEND_OTP"
		},
		MAIL_SENT: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "MAIL_SENT"
		},
		VERIFY_OTP: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				"type": "VERIFY_OTP",
				"data": data
			};
		},
		RESET_PASSWORD: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "RESET_PASSWORD"
		},
		MATCH_PASSWORD: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "MATCH_PASSWORD"
		},
		MAKE_PUBLIC_SHIFT: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "MAKE_PUBLIC_SHIFT"
		},
		CHANGE_PASSWORD: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "CHANGE_PASSWORD"
		},
		CHANGE_PASSWORD_USER: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "CHANGE_PASSWORD_USER"
		},
		EDIT_PROFILE: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "EDIT_PROFILE"
		},
		// admin specific
		ADMIN_LOGIN: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				"type": "ADMIN_LOGIN",
				"data": data
			};
		},
		LOGOUT: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "LOGOUT"
		},
		// notification specific
		NOTIFICATION_DELETED: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "NOTIFICATION_DELETED"
		},
		// content specific
		ADD_CONTENT: {
			"statusCode": HTTP_STATUS_CODE.CREATED,
			"type": "ADD_CONTENT"
		},
		DELETE_FAQ: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "DELETE_FAQ"
		},
		EDIT_CONTENT: {
			"statusCode": HTTP_STATUS_CODE.UPDATED,
			"type": "EDIT_CONTENT"
		},
		// user specific
		SIGNUP: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				"type": "SIGNUP",
				"data": data
			};
		},
		LOGIN: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				"type": "LOGIN",
				"data": data
			};
		},
		USER_LOGOUT: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "USER_LOGOUT"
		},
		BLOCK_USER: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "BLOCK_USER"
		},
		UNBLOCK_USER: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "UNBLOCK_USER"
		},
		VERIFICATION_APPROVED: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "VERIFICATION_APPROVED"
		},
		VERIFICATION_REJECTED: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "VERIFICATION_REJECTED"
		},
		ADD_PHOTO: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "ADD_PHOTO"
		},
		SET_INTEREST: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "SET_INTEREST"
		},
		EDIT_DISABILITY_DETAILS: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "EDIT_DISABILITY_DETAILS"
		},
		EMAIL_UPLOAD: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "EMAIL_UPLOAD"
		},
		EDIT_PAYMENT_DETAILS: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "EDIT_PAYMENT_DETAILS"
		},
		EDIT_EMERGENCY_CONTACT: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "EDIT_EMERGENCY_CONTACT"
		},
		EDIT_NDIS_PLAN: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "EDIT_NDIS_PLAN"
		},
		LIKE_USER: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "LIKE_USER"
		},
		UNLIKE_USER: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "UNLIKE_USER"
		},
		PROFILE_SETTINGS:(data)=> {
			return{
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "PROFILE_SETTINGS",
			"data":data
			}
		},
		PROFILE_IMAGE: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "PROFILE_IMAGE"
		},
		RATE_SUPPORTER: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "RATE_SUPPORTER"
		},
		// version specific
		ADD_VERSION: {
			"statusCode": HTTP_STATUS_CODE.CREATED,
			"type": "ADD_VERSION"
		},
		DELETE_VERSION: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "DELETE_VERSION"
		},
		EDIT_VERSION: {
			"statusCode": HTTP_STATUS_CODE.UPDATED,
			"type": "EDIT_VERSION"
		},
		// interest specific
		ADD_INTEREST: {
			"statusCode": HTTP_STATUS_CODE.CREATED,
			"type": "ADD_INTEREST"
		},
		BLOCK_INTEREST: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "BLOCK_INTEREST"
		},
		UNBLOCK_INTEREST: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "UNBLOCK_INTEREST"
		},
		EDIT_INTEREST: {
			"statusCode": HTTP_STATUS_CODE.UPDATED,
			"type": "EDIT_INTEREST"
		},
		// category specific
		ADD_CATEGORY: {
			"statusCode": HTTP_STATUS_CODE.CREATED,
			"type": "ADD_CATEGORY"
		},
		BLOCK_CATEGORY: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "BLOCK_CATEGORY"
		},
		UNBLOCK_CATEGORY: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "UNBLOCK_CATEGORY"
		},
		EDIT_WISH: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "EDIT_WISH"
		},
		EDIT_COMMUNITY: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "EDIT_COMMUNITY"
		},
		WISH_UPDATED: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "WISH_UPDATED"
		},
		COMMUNITY_UPDATED: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "COMMUNITY_UPDATED"
		},
		ROLE_CREATED: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.CREATED,

				"type": "ROLE_CREATED",
				"data": data
			};
		},
		ROLE_EDITED: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.UPDATED,

				"type": "ROLE_EDITED",
				"data": data
			};
		},
		ROLE_BLOCKED: {
			"statusCode": HTTP_STATUS_CODE.UPDATED,

			"type": "ROLE_BLOCKED"
		},
		ROLE_UNBLOCKED: {
			"statusCode": HTTP_STATUS_CODE.UPDATED,

			"type": "ROLE_UNBLOCKED"
		},
		ROLE_DELETED: {
			"statusCode": HTTP_STATUS_CODE.UPDATED,

			"type": "ROLE_DELETED"
		},
		ROLE_LIST: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,

				"type": "ROLE_LIST",
				"data": data
			};
		},
		ROLE_DETAILS: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				"message": "Role details get successfully",
				"type": "ROLE_DETAILS",
				"data": data
			};
		},
		SUB_ADMIN_CREATED: {
			"statusCode": HTTP_STATUS_CODE.CREATED,
			"message": "Sub admin registered successfully",
			"type": "SUB_ADMIN_CREATED",
		},
		SUB_ADMIN_EDITED: {
			"statusCode": HTTP_STATUS_CODE.UPDATED,
			"message": "Subadmin updated successfully",
			"type": "SUB_ADMIN_EDITED",
		},
		SUB_ADMIN_BLOCKED: {
			"statusCode": HTTP_STATUS_CODE.UPDATED,
			"message": "Subadmin inactivated successfully",
			"type": "SUB_ADMIN_BLOCKED"
		},
		SUB_ADMIN_UNBLOCKED: {
			"statusCode": HTTP_STATUS_CODE.UPDATED,
			"message": "Subadmin activated successfully",
			"type": "SUB_ADMIN_UNBLOCKED"
		},
		SUB_ADMIN_DELETED: {
			"statusCode": HTTP_STATUS_CODE.UPDATED,
			"message": "Subadmin deleted successfully",
			"type": "SUB_ADMIN_DELETED"
		},
		SUB_ADMIN_LIST: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				"message": "Sub admin list get successfully",
				"type": "SUB_ADMIN_LIST",
				"data": data
			};
		},
		SUB_ADMIN_DETAILS: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				"message": "Sub admin details get successfully",
				"type": "SUB_ADMIN_DETAILS",
				"data": data
			};
		},
		CONTACT_SYNC: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				type: "CONTACT_SYNCED",
				"data": data
			};
		},
		WISH_LIST: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				type: "WISH_LIST",
				"data": data
			};
		},
		COMMUNITY_LIST: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				type: "COMMUNITY_LIST",
				"data": data
			};
		},
		FLAGGED_WISH_LIST: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				type: "FLAGGED_WISH_LIST",
				"data": data
			};
		},
		WISH_CREATED: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "WISH_CREATED"
		},
		COMMUNITY_CREATED: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "COMMUNITY_CREATED"
		},
		UNFLAG_WISH: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "UNFLAG_WISH"
		},
		DELETE_FLAGGED_WISH: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "DELETE_FLAGGED_WISH"
		},
		UNFLAG_WISH_DETAILS: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				type: "UNFLAG_WISH_DETAILS",
				"data": data
			};
		},
		UNFLAG_WISH_REPORTS: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				type: "UNFLAG_WISH_REPORTS",
				"data": data
			};
		},
		WISH_DETAIL: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				type: "WISH_DETAIL",
				"data": data
			};
		},
		COMMUNITY_DETAIL: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				type: "COMMUNITY_DETAIL",
				"data": data
			};
		},
		USER_LIST: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				type: "USER_LIST",
				"data": data
			};
		},
		USER_DETAILS: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				type: "USER_DETAILS",
				"data": data
			};
		},
		INVESTOR_LIST: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				type: "INVESTOR_LIST",
				"data": data
			};
		},
		INVESTOR_DETAIL: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				type: "INVESTOR_DETAIL",
				"data": data
			};
		},
		EDIT_GLOBAL_WISH: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "EDIT_GLOBAL_WISH"
		},
		ACTIVATE_USER: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "ACTIVATE_USER"
		},
		DEACTIVATE_USER: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "DEACTIVATE_USER"
		},
		DELETE_USER: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "DELETE_USER"
		},
		DELETE_GLOBAL_WISH: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "DELETE_GLOBAL_WISH"
		},
		DELETE_WISH: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "DELETE_WISH"
		},
		REMOVE_WISH: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "REMOVE_WISH"
		},
		DELETE_COMMUNITY: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "DELETE_COMMUNITY"
		},
		CLOSE_WISH: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "DELETE_WISH"
		},
		DELETE_SYNC_CONTACT: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "DELETE_SYNC_CONTACT"
		},
		REPORT_WISH: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "REPORT_WISH"
		},
		REPORT_COMMUNITY: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "REPORT_COMMUNITY"
		},
		REPORT_BLESSING: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "REPORT_BLESSING"
		},
		REPORT: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "REPORT"
		},
		CREATE_INVESTOR: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "CREATE_INVESTOR"
		},
		INVESTOR_EDIT: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "INVESTOR_EDIT"
		},
		INVESTOR_UNBLOCKED: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "INVESTOR_UNBLOCKED"
		},
		INVESTOR_BLOCKED: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "INVESTOR_BLOCKED"
		},
		INVESTOR_DELETED: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "INVESTOR_DELETED"
		},
		BLESSSING_SUBMITTED: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "BLESSSING_SUBMITTED"
		},
		GRATITUDE_SUBMITTED: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "GRATITUDE_SUBMITTED"
		},
		GUIDED_BLESSING_DETAIL: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				type: "GUIDED_BLESSING_DETAIL",
				"data": data
			};
		},
		GUIDED_BLESSING_UPDATE: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "GUIDED_BLESSING_UPDATE"
		},
		GUIDED_BLESSING_DELETE: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "GUIDED_BLESSING_DELETE"
		},
		GUIDED_BLESSING_ACTIVATE: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "GUIDED_BLESSING_ACTIVATE"
		},
		GUIDED_BLESSING_DEACTIVATE: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "GUIDED_BLESSING_DEACTIVATE"
		},
		GUIDED_BLESSING_PUBLISH: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "GUIDED_BLESSING_PUBLISH"
		},
		FLAGGED_BLESSING_LIST: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				type: "FLAGGED_BLESSING_LIST",
				"data": data
			};
		},
		FLAGGED_PERFORM_BLESSING_DETAIL: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				type: "FLAGGED_PERFORM_BLESSING_DETAIL",
				"data": data
			};
		},
		FLAGGED_BLESSING_REPORT: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				type: "FLAGGED_BLESSING_REPORT",
				"data": data
			};
		},
		FLAGGED_COMMUNITY_REPORT: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				type: "FLAGGED_COMMUNITY_REPORT",
				"data": data
			};
		},
		UNFLAG_PERFORM_BLESSING: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "UNFLAG_PERFORM_BLESSING"
		},
		DELETE_PERFORM_BLESSING: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "DELETE_PERFORM_BLESSING"
		},
		USER_PROFILE_UPDATED: (data)=>{
			return {
			"statusCode": HTTP_STATUS_CODE.OK,
			"type": "USER_PROFILE_UPDATED",
			"data":data
			}
		},
		UNFLAG_COMMUNITY: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "UNFLAG_COMMUNITY"
		},
		DELETE_FLAGGED_COMMUNITY: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "DELETE_FLAGGED_COMMUNITY"
		},
		GLOBAL_WISH_PUBLISHED: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "GLOBAL_WISH_PUBLISHED"
		},
		GLOBAL_WISH_PINNED: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "GLOBAL_WISH_PINNED"
		},
		GLOBAL_WISH_UNPINNED: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "GLOBAL_WISH_UNPINNED"
		},
		GLOBAL_WISH_CLOSED: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "GLOBAL_WISH_CLOSED"
		},
		REPORTED_POSTS: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				type: "REPORTED_POSTS",
				"data": data
			};
		},
		THANKS_WISHWELL: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "THANKS_WISHWELL"
		},
		THANKS_WISHWELL_LISTING: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				type: "THANKS_WISHWELL_LISTING",
				"data": data
			};
		},
		FLAGGED_GRATITUDE_LIST: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				type: "FLAGGED_GRATITUDE_LIST",
				"data": data
			};
		},
		FLAGGED_GRATITUDE_DETAIL: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				type: "FLAGGED_GRATITUDE_DETAIL",
				"data": data
			};
		},
		FLAGGED_GRATITUDE_REPORT: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				type: "FLAGGED_GRATITUDE_REPORT",
				"data": data
			};
		},
		UNFLAG_GRATITUDE: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "UNFLAG_GRATITUDE"
		},
		DELETE_FLAGGED_GRATITUDE: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "DELETE_FLAGGED_GRATITUDE"
		},
		THANK_WISHWELL_TOOLTIP: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				type: "THANK_WISHWELL_TOOLTIP",
				"data": data
			};
		},
		THANK_WISHWELL_TOOLTIP_UPDATED: {
			"statusCode": HTTP_STATUS_CODE.OK,
			type: "THANK_WISHWELL_TOOLTIP_UPDATED"
		},
		EXPORTED: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				"type": "EXPORTED",
				"data": data
			};
		},
		USER_PASSWORD_SET: (data) => {
			return {
				"statusCode": HTTP_STATUS_CODE.OK,
				"type": "USER_PASSWORD_SET",
				"data": data
			};
		},
		EMAIL_EXISTS:(data)=> {
			return{
				"statusCode": HTTP_STATUS_CODE.OK,
				"type": "EMAIL_EXISTS",
				"data":data
			}
		},
		FORGOT_PASSWORD_EMAIL_SENT:(data)=> {
			return{
				"statusCode": HTTP_STATUS_CODE.OK,
				"type": "FORGOT_PASSWORD_EMAIL_SENT",
				"data":data
			}
		},
		FRIEND_REMOVED: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"message": "Friend removed successfully",
			"type": "FRIEND_REMOVED"
		},
		JOIN_REQUEST_SENT: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"message": "Join request sent successfully",
			"type": "JOIN_REQUEST_SENT"
		},
		JOIN_REQUEST_ACCEPTED: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"message": "Join request accepted successfully",
			"type": "JOIN_REQUEST_ACCEPTED"
		},
		JOIN_REQUEST_DECLINED: {
			"statusCode": HTTP_STATUS_CODE.OK,
			"message": "Join request declined successfully",
			"type": "JOIN_REQUEST_DECLINED"
		},
	}

};

const TEMPLATES = {
	EMAIL: {
		SUBJECT: {
			FORGOT_PASSWORD: "Reset Password Request",
			// RESET_PASSWORD: "Reset password link",
			// VERIFY_EMAIL: "Verify email address",
			WELCOME: "Welcome to Rcc!",
			ACCOUNT_BLOCKED: "Account Blocked",
			VERIFICATION_REJECTED: "Verification Process Rejected",
			UPLOAD_DOCUMENT: "Upload Document",
			INCIDENT_REPORT: "Incident Report",
			MAGIC_LINK: "Magic Link",
			THANKS_WISHWELL:"Thanks WishWell",
			THANKS_WISHWELL_GREETINGS:"Thanks WishWell Greeting Receive",
			INVESTOR_INVITATION: "Investor Invitation",
			VERIFY_USER_EMAIL: "Please verify your email"
		},
		// BCC_MAIL: [""],
		FROM_MAIL: process.env["SENDGRID_FROM_MAIL"]
	},
	SMS: {
		OTP: `Your Rcc Code is .`,
		THANKS: `Thanks, Rcc Team`
	}
};

const FIREBASE_TOKEN = {
	FIREBASE_ACCOUNT_KEY: "",//process.env["FIREBASE_ACCOUNT_KEY"],
	FIREBASE_DATABASE_URL: ""// process.env["DB_URL"]
}

const CONTENT_TYPE = {
	FAQ: "FAQ",
	PRIVACY_POLICY: "PRIVACY_POLICY",
	CONTACT_US: "CONTACT_US",
	ABOUT_US: "ABOUT_US",
	TERMS_AND_CONDITIONS: "TERMS_AND_CONDITIONS",
	COMMUNITY_GUIDELINES: "COMMUNITY_GUIDELINES",
	THANK_WISHWELL_TOOLTIP: "THANK_WISHWELL_TOOLTIP"
	// TERMS_OF_SERVICE_AGREEMENT: "TERMS_OF_SERVICE_AGREEMENT",
	// EMPLOYMENT_CONTRACT_AGREEMENT: "EMPLOYMENT_CONTRACT_AGREEMENT",
	// CODE_OF_CONDUCT: "CODE_OF_CONDUCT",
	// DWES_CHECK_SIGNED: "DWES_CHECK_SIGNED"
};

const MIME_TYPE = {
	XLSX: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
	CSV1: "application/vnd.ms-excel",
	CSV2: "text/csv",
	CSV3: "data:text/csv;charset=utf-8,%EF%BB%BF",
	XLS: "application/vnd.ms-excel"
};

const REGEX = {
	EMAIL: /[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,63}$/,
	// EMAIL: /^\w+([.-]\w+)*@\w+([.-]\w+)*\.\w{2,5}$/i,
	// EMAIL: /^(([^<>()\[\]\\.,;:\s@']+(\.[^<>()\[\]\\.,;:\s@']+)*)|('.+'))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
	/* URL: /^(http?|ftp|https):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|\
		int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/, */
	URL: /^(https?|http|ftp|torrent|image|irc):\/\/(-\.)?([^\s\/?\.#-]+\.?)+(\/[^\s]*)?$/i,
	SSN: /^(?!***********|***********)(?!666|000|9\d{2})\d{3}-(?!00)\d{2}-(?!0{4})\d{4}$/, // US SSN
	ZIP_CODE: /^[0-9]{5}(?:-[0-9]{4})?$/,
	PASSWORD: /(?=[^A-Z]*[A-Z])(?=[^a-z]*[a-z])(?=.*[@#$%^&+=])(?=[^0-9]*[0-9]).{8,16}/, // password is between 8 and 16 characters long and includes at least one uppercase letter, one lowercase letter, one digit, and one special character
	PASSWORD_V2: /(?=[^A-Z]*[A-Z])(?=[^a-z]*[a-z])(?=(.*[\d@#$%^&+=])).{8,16}/, // checks for at least one uppercase letter, at least one lowercase letter, and then either at least one digit or one special character
	PASSWORD_V3: /^(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()-_=+{}[\]\\|;:'",.<>?/]).{8,40}$/, // checks for atleast one uppercase, one special & one number
	COUNTRY_CODE: /^\d{1,4}$/,
	MOBILE_NUMBER: /^\d{6,16}$/,
	STRING_REPLACE: /[-+ ()*_$#@!{}|\/^%`~=?,.<>:;'"]/g,
	SEARCH: /[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,
	MONGO_ID: /^[a-f\d]{24}$/i
};
const VERSION_UPDATE_TYPE = {
	NORMAL: "NORMAL", // skippable
	FORCEFULLY: "FORCEFULLY"
};
const NOTIFICATION_TYPE = {
	INCIDENT_REPORT: "INCIDENT_REPORT",
	ADD_NOTES: "ADD_NOTE",
	PERFORM_BLESS:"PERFORM_BLESS",
	CREATE_WISHES:"CREATE_WISHES",
	TAG_WISHES:"TAG_WISHES",
	GRATITUDES:"GRATITUDES",
	GLOBAL_GRATITUDES:"GLOBAL_GRATITUDES",
	EVENT: "1",
	DELETE_WISH_ADMIN: "DELETE_WISH_ADMIN",
	DELETE_BLESSING_ADMIN: "DELETE_BLESSING_ADMIN",
	DELETE_GRATITUDE_ADMIN: "DELETE_GRATITUDE_ADMIN",
	DELETE_COMMUNITY_ADMIN: "DELETE_COMMUNITY_ADMIN",
	FRIEND_REQUEST: "FRIEND_REQUEST",
	FRIEND_REQUEST_ACCEPTED: "FRIEND_REQUEST_ACCEPTED",
	COMMUNITY_JOIN_REQUEST: "COMMUNITY_JOIN_REQUEST",
	COMMUNITY_JOIN_ACCEPTED: "COMMUNITY_JOIN_ACCEPTED",
	COMMUNITY_JOIN_DECLINED: "COMMUNITY_JOIN_DECLINED"
};
const GRAPH_TYPE = {
	DAILY: "DAILY",
	WEEKLY: "WEEKLY",
	MONTHLY: "MONTHLY",
	YEARLY: "YEARLY"
};
const MONTHS = [
	{ index: 1, day: 31, week: 5 },
	{ index: 2, day: 28, week: 4 },
	// { index: 2, day: 29, week: 5 },
	{ index: 3, day: 31, week: 5 },
	{ index: 4, day: 30, week: 5 },
	{ index: 5, day: 31, week: 5 },
	{ index: 6, day: 30, week: 5 },
	{ index: 7, day: 31, week: 5 },
	{ index: 8, day: 31, week: 5 },
	{ index: 9, day: 30, week: 5 },
	{ index: 10, day: 31, week: 5 },
	{ index: 11, day: 30, week: 5 },
	{ index: 12, day: 31, week: 5 }
];
const MONTH_NAME = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sept", "Oct", "Nov", "Dec"];
const WEEK_NAME = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
const JOB_SCHEDULER_TYPE = {
	AUTO_SESSION_EXPIRE: "auto_session_expire",
	WISHES_REQUEST_MESSAGE: "wishes_request_message",
	WISHES_REQUEST_HOST: "wishes_request_host"

};
const LANGUAGES = [{
	"code": "en",
	"id": 38,
	"isSelected": false,
	"name": "English"
}];
const TOKEN_TYPE = {
	USER_LOGIN: "USER_LOGIN", // login/signup
	ADMIN_LOGIN: "ADMIN_LOGIN",
	ADMIN_OTP_VERIFY: "ADMIN_OTP_VERIFY",
	VERIFY_EMAIL: "VERIFY_EMAIL"
};
const timeZones = [
	"Asia/Kolkata"
];


const UPDATE_TYPE = {
	BLOCK_UNBLOCK: "BLOCK_UNBLOCK",
	APPROVED_DECLINED: "APPROVED_DECLINED",
	ABOUT_ME: "ABOUT_ME",
	EDIT_PROFILE: "EDIT_PROFILE",
	SET_PROFILE_PIC: "SET_PROFILE_PIC"
};

const QUEUE_NAME = {
	DELAY_NON_DELAY: "delay-non-delay",
	STEPS_POINTS_SUMMARY_EVERYDAY: "steps-points-summary-everyday",
	AUTO_COMPLETE_CLASS_BOOKING: "auto-complete-class-booking",
	AUTO_INCOMPLETE_CLASS_BOOKING: "auto-incomplete-class-booking",
	POINTS_DISTRIBUTION: "points-distribution",
	CORPORATE_POINTS_DISTRIBUTION: "corporate-points-distribution",
	PUSH_NOTIFIACTION_IOS: "-push-notification-ios-v9",
	PUSH_NOTIFIACTION_ANDROID: "-push-notification-android-v9",
	PUSH_NOTIFIACTION_WEB: "-push-notification-web-v9",
	DATABASE_INSERT: "-data-base-insertion-v9",
	COUPON_CODE_ASSIGNED: "coupon-code-assigned"
};


const DISTANCE_MULTIPLIER = {
	MILE_MULTIPLIER: 0.0006213727366498,
	KM_TO_MILE: 0.621372737,
	MILE_TO_METER: 1609.34,
	METER_TO_MILE: 0.000621371,
	METER_TO_KM: 0.001
};

const fileUploadExts = [
	".mp4", ".flv", ".mov", ".avi", ".wmv",
	".jpg", ".jpeg", ".png", ".gif", ".svg",
	".mp3", ".aac", ".aiff", ".m4a", ".ogg"
];


const FRIEND_REQUEST_STATUS = {
	REQUEST_SENT: "REQUEST_SENT",
	REQUEST_PENDING: "REQUEST_PENDING",
	REQUEST_ACCEPTED: "REQUEST_ACCEPTED",
	REQUEST_DECLINED: "REQUEST_DECLINED",
	UN_FRIEND: "UN_FRIEND"
};

const WEB_ENDPOINTS = {
	MAGIC_LINK: SERVER.APP_URL

};

const DEEPLINK_TYPE = {

	MAGIC_LINK: "MAGIC_LINK",
	SMS_INVITE:"SMS_INVITE",
	COM_MAP:"COM_MAP",
	FORGOT_PASSWORD:"FORGOT_PASSWORD",
	VERIFY_USER_EMAIL: "VERIFY_USER_EMAIL",
	WISH_SHARE: "WISH_SHARE"
};

const MAIL_SENDING_TYPE = {
	SES: "SES",
	SENDGRID: "SENDGRID",
	SMTP: "SMTP",
};

const LOGIN_TYPE = {
	FACEBOOK: "facebook",
	GOOGLE: "google",
	NORMAL: "normal",
	APPLE: 'apple'
};
const WISH_CREATED_BY = {
	ADMIN: "ADMIN",
	USER: "USER"
};
const WISH_INTESION = {
	LOVE: "LOVE",
	PEACE: "PEACE",
	HEALTH: "HEALTH",
	ABUNDANCE: "ABUNDANCE"
}
const BLESSING_CREATED_BY = {
	ADMIN: "ADMIN",
	USER: "USER"
}
const BLESSING_TYPE = {
	MEDITATION: "MEDITATION",
	PRAYER: "PRAYER"
}
const BLESSING_VOICEOVER = {
	MASCULINE: "MASCULINE",
	FEMININE: "FEMININE"
}
const BLESSING_SORT_CRITERIA = {
	NAME: "name",
	CREATED_AT: "createdAt",
	STATUS: "status",
	LANGUAGE: "language",
	VOICEOVER: "voiceover",
	INTENSION: "intension",
	AUTHOR_NAME: "authorName"
}
const WISH_TAG_TYPE = {
	CONTACTS: "CONTACTS",
	COHOST: "COHOST",
	INVITED: "INVITED",
	HOST: "HOST",
	CREATOR:"CREATOR",
	INVITED_HOST: "INVITED_HOST",
	JOIN_REQUEST: "JOIN_REQUEST"
};
const WISH_TYPE = {
	MYSELF: "MYSELF",
	OTHER: "OTHER"
};
const DASH_EXP_TYPE = {
	MONTH: "MONTH",
	DATE: "DATE"
}
const WISH_SORT_CRITERIA = {
	TITLE: "title",
	INTENSION: "intension",
	CREATED_AT: "createdAt"
}
const REPORT_TYPE = {
	WISH: "WISH",
	BLESSING: "BLESSING",
	GRATITUDE: "GRATITUDE",
	COMMUNITY: "COMMUNITY"
}
const FLAG_TYPE = {
	FLAGGED: "FLAGGED",
	UN_FLAGGED: "UN_FLAGGED",
	DELETED: "DELETED"
}
const USER_SORT_CRITERIA = {
	NAME: "name",
	STATUS: "status",
	CREATED_AT: "createdAt"
}
const INVESTOR_SORT_CRITERIA = {
	NAME: "name",
	EMAIL: "email",
	USER_TYPE: "userType",
	STATUS: "status",
	CREATED_AT: "createdAt",
	LAST_SEEN: "lastSeen"
}
const FLAGGED_BLESSING_SORT_CRITERIA = {
	BLESSING_NUMBER: "BLESSING_NUMBER",
	BLESSED_BY: "BLESSED_BY",
	WISH_NUMBER: "WISH_NUMBER",
	STATUS: "STATUS",
	CREATED_AT: "CREATED_AT",
	REPORT_COUNT: "REPORT_COUNT"
}
const FLAGGED_WISH_SORT_CRITERIA = {
	WISH_NUMBER: "WISH_NUMBER",
	WISH_OWNER: "WISH_OWNER",
	INTENTION: "INTENTION",
	CREATED_AT: "CREATED_AT",
	STATUS: "STATUS"
}
const COMMUNITY_SORT_CRITERIA = {
	NAME: "NAME",
	CREATED_BY: "CREATED_BY",
	CREATED_AT: "CREATED_AT",
	STATUS: "STATUS",
	REPORT_COUNT: "REPORT_COUNT"
}
const COMMUNITY_LISTING_TYPE = {
	ALL: "ALL",
	FLAGGED: "FLAGGED",
	USER: "USER"
}
const REPORT_TYPE_CRITERIA = {
	SUSPICIOUS: "Suspicious",
	SPAM: "It's Spam",
	HATE_SPEECH_OR_SYMBOL: "Hate Speech or Symbols",
	FALSE_INFORMATION: "False Information"

}
const PUSH_NOTIFICATIONS = {         // for push notification
	CONTACT_SYNC: {

		NO_REGISTATION: {
			TITLE: "No Registration",
			MESSAGE: "User not registered",
		},
		SYNC: {
			TITLE: "Contact Sync",
			MESSAGE: "Contact sync successfully.",
		},

	},
	ASSIGN_JOB: {
		TITLE: "Employees Assigned",
		DESCRIPTION: " has assigned  employees for ",
	},
}
const ACTION_TYPE = {
	ADD: "ADD",
	REMOVE: "REMOVE",
	ACCEPT: "ACCEPT",
	REJECT: "REJECT"
};
const LOCATION_TYPE = {
	COUNTRIES: "COUNTRIES",
	STATES: "STATES",
	CITIES: "CITIES"
};
const REGISTRATION_FLAG = {
	WISH_INVITED:"WISH_INVITED",
	DEFAULT:"DEFAULT",
	WISH_CREATED:"WISH_CREATED"
}
const HOME_LIST = {
	FAVOURITE:"FAVOURITE",
	SUGGESTED:"SUGGESTED",
	LEAST_BLESSED:"LEAST_BLESSED"
}
const BLESSING_PERFORM_TYPE = 
{
	AUDIO:"AUDIO",
	TEXT:"TEXT",
	NONE:"NONE"
}
const GRATITUDE_TYPE = 
{
	PERSONALISED:"PERSONALISED",
	GLOBAL:"GLOBAL"
}
const REPORTED_POST_SORT_CRITERIA = {
	POST_NUMBER: "POST_NUMBER",
	TYPE: "TYPE",
	DATE: "DATE",
	STATUS: "STATUS"
}
const FLAGGED_GRATITUDE_SORT_CRITERIA = {
	GRATITUDE_NUMBER: "GRATITUDE_NUMBER",
	WISH_NUMBER: "WISH_NUMBER",
	GRATITUDE_BY: "GRATITUDE_BY",
	GRATITUDE_DATE: "GRATITUDE_DATE",
	STATUS: "STATUS"
}
const THANK_WISHWELL_LISTING_TYPE = {
	ALL: "ALL",
	USER: "USER"
}
const THANK_WISHWELL_SORT_CRITERIA = {
	SERIAL_NUMBER: "SERIAL_NUMBER",
	NOTE_OWNER: "NOTE_OWNER",
	DATE: "DATE",
	LOCATION: "LOCATION",
	DONATION: "DONATION",
	CONSENT: "CONSENT"
}
const GLOBAL_WISH_PIN_COUNT = 3;
const ADMIN_REPORT_REDIRECT = {
	   WISH_REPORT_ENDPOINT:"/flagged-posts/flagged-wish-detail/",
	   BLESSING_REPORT_ENDPOINT:"/flagged-posts/flagged-blessing-detail/",
	   COMMUNITY_REPORT_ENDPOINT:"/communities/community-detail/",
	   GRATITUDE_REPORT_ENDPOINT:"/flagged-posts/flagged-gratitude-detail/"
}
const SMS_CONTENT = {
	DEAR_USER:"Dear ",
	TAG_CONTACT_ON_WISH_FOR_SELF:"I am rallying our community to wish me well. Please check out my wish and support by giving one minute of your time and attention: ",
	// TAG_CONTACT_ON_WISH_FOR_SELF1:" I created a Wish on WishWell and am rallying for your support. Please click here to see my wish and gift one minute of your attention.",
	TAG_CONTACT_ON_WISH_FOR_SELF1:"[NAME] is asking for your support with their Wish '[DESCRIPTION]'. Please click the link and download WishWell to offer your support by doing a one-minute blessing. [URL]",
	// TAG_CONTACT_ON_WISH_FOR_OTHERS1:"I am rallying our community to wish ",
	TAG_CONTACT_ON_WISH_FOR_OTHERS3:" well. Please check out my wish and support by giving one minute of your time and attention: ",
	TAG_CONTACT_ON_WISH_FOR_OTHERS1:" I created a Wish on WishWell for ",
	TAG_CONTACT_ON_WISH_FOR_OTHERS2:" Please click here to download and gift one minute of your attention to my wish for ",
	// COMMUNITY_INVITE1: "Hi, will you join ",
	// COMMUNITY_INVITE2:" on WishWell where we can harness the power of unified focus when our members need support? \n",
	COMMUNITY_INVITE1: "You are invited to be a part of ",
	COMMUNITY_INVITE2:" on WishWell and support our community. \n Click here to join.\n",
	WITH_GRAT:"With gratitude, ",
	// HOST_WISH_REQUEST:" I am rallying to support you on WishWell. `\n` Please click here to see my Wish and approve or edit the details. `\n` "
	HOST_WISH_REQUEST:"[NAME] has created a wish for you on WishWell. Please click the link to download WishWell and approve the wish so that others can support you. [URL] `\n` "

}
const WISH_SHARE = {
	WISH_SHARE_CONTENT1: "I am rallying our community to wish me well. Please check out my wish and support by giving one minute of your time and attention",
	WISH_SHARE_CONTENT2: "I am rallying our community to wish ",
	WISH_SHARE_CONTENT3: " well. Please check out my wish and support by giving one minute of your time and attention",
	WISH_SHARE_CONTENT4: "I am rallying our community to wish me well. Please check out my wish and support by giving one minute of your time and attention",
	WISH_SHARE_CONTENT5: "I am rallying our community to wish ",
	WISH_SHARE_CONTENT6: " well. Please check out my wish and support by giving one minute of your time and attention"
}
const SMS_CONTENT_ADMIN = {
	DELETE_USER: (data) => {
		return `Your account on Wishwell has been deleted by admin due to ${data}\n\nWe regret to inform you that you will not be able to log in to your account, nor will you be able to create a new account using your existing credentials. This decision was not made lightly. WishWell is a safe space to spread compassion and provide support for others and your actions have shown a pattern of abuse of this platform.\n\nIf you believe you have received this message in error, please reach <NAME_EMAIL> to request support.`
	},
	DEACTIVATE_USER: (data) => {
		return `Your account on WishWell has been deactivated by admin due to ${data}\n\nYou will not be able to log in to your account until you have been reactivated. Please review the community guidelines and keep WishWell a safe space to spread compassion and provide support for others.\n\nIf you believe you have received this message in error, please reach <NAME_EMAIL> for support.`
	},
	REACTIVATE_USER: `Your account on Wishwell has been reactivated by admin. You may now log in to your account and continue providing love and support to others. Please review the community guidelines and keep WishWell a safe space to spread compassion and provide support for others.`
}
const ADMIN_NOTIFICATION_TYPE = {
	DELETE_WISH_ADMIN: {
		TYPE: "DELETE_WISH_ADMIN",
		TITLE: "Wish Deleted",
		BODY_PREFIX: "Your wish has been deleted by admin due to",
		BODY_SUFFIX: "Please review the community guidelines and keep WishWell a safe space to spread compassion and provide support for others."
	},
	DELETE_BLESSING_ADMIN: {
		TYPE: "DELETE_BLESSING_ADMIN",
		TITLE: "Blessing Deleted",
		BODY_PREFIX: "Your blessing has been deleted by admin due to",
		BODY_SUFFIX: "Please review the community guidelines and keep WishWell a safe space to spread compassion and provide support for others."
	},
	DELETE_GRATITUDE_ADMIN: {
		TYPE: "DELETE_GRATITUDE_ADMIN",
		TITLE: "Gratitude Deleted",
		BODY_PREFIX: "Your gratitude has been deleted by admin due to",
		BODY_SUFFIX: "Please review the community guidelines and keep WishWell a safe space to spread compassion and provide support for others."
	},
	DELETE_COMMUNITY_ADMIN: {
		TYPE: "DELETE_COMMUNITY_ADMIN",
		TITLE: "Community Deleted",
		BODY_PREFIX: "Your community has been deleted by admin due to",
		BODY_SUFFIX: "Please review the community guidelines and keep WishWell a safe space to spread compassion and provide support for others."
	}
}

const ADMIN_EXPORT_SHEETS = {
	SHEET1_WISH: [
		{ header: "Wish number", key: "Wish number", width: 12, outlineLevel: 1 },
		{ header: "Creator ID", key: "Creator ID", width: 32, outlineLevel: 1 },
		// { header: "Wish Type", key: "Wish Type", width: 16, outlineLevel: 1 },
		{ header: "Host ID", key: "Host ID", width: 32, outlineLevel: 1 },
		{ header: "Wish intention", key: "Wish intention", width: 16, outlineLevel: 1 },
		{ header: "Creator Location", key: "Creator Location", width: 32, outlineLevel: 1 },
		{ header: "Creation Date", key: "Creation Date", width: 16, outlineLevel: 1 },
		{ header: "Count of Users Tagged", key: "Count of Users Tagged", width: 20, outlineLevel: 1 },
		{ header: "Count of Communities Tagged", key: "Count of Communities Tagged", width: 20, outlineLevel: 1 },
		{ header: "Count of Co-Hosts Tagged", key: "Count of Co-Hosts Tagged", width: 20, outlineLevel: 1 },
		{ header: "Host Location", key: "Host Location", width: 32, outlineLevel: 1 },
		{ header: "Close Date", key: "Close Date", width: 16, outlineLevel: 1 }
	],
	SHEET2_BLESSING: [
		{ header: "Blessing number", key: "Blessing number", width: 12, outlineLevel: 1 },
		{ header: "Associated Wish number", key: "Associated Wish number", width: 12, outlineLevel: 1 },
		{ header: "Creator ID", key: "Creator ID", width: 32, outlineLevel: 1 },
		{ header: "Creator Location", key: "Creator Location", width: 32, outlineLevel: 1 },
		{ header: "Recipient Location", key: "Recipient Location", width: 32, outlineLevel: 1 },
		{ header: "Creation Date", key: "Creation Date", width: 16, outlineLevel: 1 },
		{ header: "Audio/Note", key: "Audio/Note", width: 12, outlineLevel: 1 }
	],
	SHEET3_GRATITUDE: [
		{ header: "Gratitude number", key: "Gratitude number", width: 12, outlineLevel: 1 },
		{ header: "Associated Wish number", key: "Associated Wish number", width: 12, outlineLevel: 1 },
		{ header: "Creator ID", key: "Creator ID", width: 32, outlineLevel: 1 },
		{ header: "Creator Location", key: "Creator Location", width: 32, outlineLevel: 1 },
		{ header: "Recipient ID", key: "Recipient ID", width: 32, outlineLevel: 1 },
		{ header: "Recipient Location", key: "Recipient Location", width: 32, outlineLevel: 1 },
		{ header: "Creation Date", key: "Creation Date", width: 16, outlineLevel: 1 },
		{ header: "Audio/Note", key: "Audio/Note", width: 32, outlineLevel: 1 }
	],
	SHEET4_USER: [
		{ header: "User ID", key: "User ID", width: 32, outlineLevel: 1 },
		// { header: "User email", key: "User email", width: 32, outlineLevel: 1 },
		{ header: "User Name", key: "User Name", width: 20, outlineLevel: 1 },
		{ header: "Invitation Type", key: "Invitation Type", width: 12, outlineLevel: 1 },
		{ header: "User email", key: "User email", width: 32, outlineLevel: 1 },
		{ header: "Invitation Type", key: "Invitation Type", width: 12, outlineLevel: 1 },
		{ header: "User Name", key: "User Name", width: 20, outlineLevel: 1 },
		{ header: "Invitation Type", key: "Invitation Type", width: 12, outlineLevel: 1 },
		{ header: "User email", key: "User email", width: 32, outlineLevel: 1 },
		{ header: "Invitation Type", key: "Invitation Type", width: 12, outlineLevel: 1 },
		{ header: "User Name", key: "User Name", width: 20, outlineLevel: 1 },
		{ header: "Invitation Type", key: "Invitation Type", width: 12, outlineLevel: 1 },
		{ header: "User email", key: "User email", width: 32, outlineLevel: 1 },
		{ header: "Invitation Type", key: "Invitation Type", width: 12, outlineLevel: 1 },
		{ header: "User Name", key: "User Name", width: 20, outlineLevel: 1 },
		{ header: "Invitation Type", key: "Invitation Type", width: 12, outlineLevel: 1 },
		{ header: "User email", key: "User email", width: 32, outlineLevel: 1 },
		{ header: "Invitation sent date", key: "Invitation sent date", width: 16, outlineLevel: 1 },
		{ header: "Status", key: "Status", width: 16, outlineLevel: 1 },
		{ header: "Count of Items flagged", key: "Count of Items flagged", width: 16, outlineLevel: 1 },
		{ header: "Referral User’s ID", key: "Referral User’s ID", width: 32, outlineLevel: 1 },
		{ header: "Member Since", key: "Member Since", width: 16, outlineLevel: 1 },
		{ header: "Count of Thank WW Notes", key: "Count of Thank WW Notes", width: 16, outlineLevel: 1 },
		{ header: "Count of Wishes Created", key: "Count of Wishes Created", width: 16, outlineLevel: 1 },
		{ header: "Count of Blessings Performed", key: "Count of Blessings Performed", width: 16, outlineLevel: 1 },
		{ header: "Count of Gratitude Given", key: "Count of Gratitude Given", width: 16, outlineLevel: 1 },
		{ header: "Count of Communities Created", key: "Count of Communities Created", width: 16, outlineLevel: 1 },
		{ header: "Count of Communities Joined", key: "Count of Communities Joined", width: 16, outlineLevel: 1 }
	],
	SHEET5_COMMUNITY: [
		{ header: "Community Number", key: "Community Number", width: 16, outlineLevel: 1 },
		{ header: "Community Name", key: "Community Name", width: 32, outlineLevel: 1 },
		{ header: "Creator ID", key: "Creator ID", width: 32, outlineLevel: 1 },
		{ header: "Creation Date", key: "Creation Date", width: 16, outlineLevel: 1 },
		{ header: "Number times reported", key: "Number times reported", width: 20, outlineLevel: 1 },
		{ header: "Status", key: "Status", width: 16, outlineLevel: 1 },
		{ header: "Count of Members", key: "Count of Members", width: 20, outlineLevel: 1 },
		{ header: "Wishes Tagged to", key: "Wishes Tagged to", width: 20, outlineLevel: 1 },
		{ header: "Blessings Performed by Community Members on Wishes Tagged to", key: "Blessings Performed by Community Members on Wishes Tagged to", width: 32, outlineLevel: 1 }
	],
	SHEET6_THANK: [
		{ header: "Thank WW Number", key: "Thank WW Number", width: 12, outlineLevel: 1 },
		{ header: "Creator ID", key: "Creator ID", width: 32, outlineLevel: 1 },
		{ header: "Creation Date", key: "Creation Date", width: 16, outlineLevel: 1 },
		{ header: "Location", key: "Location", width: 60, outlineLevel: 1 },
		{ header: "Note type (Note/Audio)", key: "Note type (Note/Audio)", width: 12, outlineLevel: 1 },
		{ header: "Note", key: "Note", width: 32, outlineLevel: 1 },
		{ header: "Amount of Donation", key: "Amount of Donation", width: 12, outlineLevel: 1 },
		{ header: "Released", key: "Released", width: 12, outlineLevel: 1 }
	],
	SHEET7_LIBRARY: [
		{ header: "Name", key: "Name", width: 20, outlineLevel: 1 },
		{ header: "Author", key: "Author", width: 20, outlineLevel: 1 },
		{ header: "Intention", key: "Intention", width: 20, outlineLevel: 1 },
		{ header: "Type", key: "Type", width: 20, outlineLevel: 1 },
		{ header: "Voiceover", key: "Voiceover", width: 20, outlineLevel: 1 },
		{ header: "Uses", key: "Uses", width: 20, outlineLevel: 1 },
		{ header: "Language", key: "Language", width: 20, outlineLevel: 1 }
	]
}

const MESSAGES_NOTIFICATION = {
	WISH_CREATED:"Please approve a wish that was created for you.",
	WISH_CREATED1:"Please support ",
	WISH_CREATED2:" with ",
	WISH_CREATED3:" by giving a minute of your attention.",
	GLOBAL_WISH_PUBLISHED: "Please click to give.",
	COMMUNITY_INVITE:"Received invite from community.",
	WISH_CREATED_HOST: "Please approve a wish that was created for you.",
	BLESSING_RECEIVED: "You have blessing(s). View your heart-felt support and feel the love.",
	GRATITUDE_RECEIVED: "You received gratitude for your blessings. View the loving appreciation.",
	COMMUNITY_INVITE1:"You\'re invited to join ",
	COMMUNITY_INVITE2:" to unify and support wishes.",
	// COMMUNITY_INVITE3:"[CREATOR_NAME] has created a compassionate community [COMMUNITY_NAME] on WishWell and is asking that you join. Please click the link to download WishWell and accept the community invite to share support with others. [URL]",
	// COMMUNITY_INVITE3:"[CREATOR_NAME] has created a compassionate community [COMMUNITY_NAME] on WishWell and is asking that you join.",
	COMMUNITY_INVITE3:"You have been invited to join [COMMUNITY_NAME] community. Please accept to take part in supporting others in the community.",
	WISH_REQUEST_DECLINED:"Wish request declined",
	WISH_REQUEST_ACCEPTED:"Wish request accepted",
	CONTACT_JOINED_APP: "Invite them to your wishes and communities so that you can receive their love.",
	REMINDER_OTHERS_APP_INVITE: "Your friend [NAME] has not joined WishWell. Tap to resend the invite to approve your Wish.",
	REMINDER_MYSELF_APP_INVITE: "Your friend [NAME] has not joined WishWell. Tap to resend the invite to support your Wish.",
	REMINDER_COMMUNITY_INVITE: "Your friend [NAME] has not joined WishWell. Tap to resend the invite to join your Community.",
	UPDATE_APP_1: "Upgrade to version [VERSION] for improved performance and new features.",
	UPDATE_APP_2: "Version [VERSION] is now available. Please update via the App Store for improved performance if you haven\'t updated to the new version already.",
	FRIEND_REQUEST: "[NAME] sent you a friend request",
	FRIEND_REQUEST_ACCEPTED: "[NAME] accepted your friend request"
}

const TITLE_NOTIFICATION = {
	WISH_HOST_CREATED: "A wish has been created for you",
	WISH_CREATED: "You have a Wish Request",
	GLOBAL_WISH_PUBLISHED: "A new Global Wish is asking for your attention.",
	BLESSING_RECEIVED: "You received a Blessing",
	GRATITUDE_RECEIVED: "You received gratitude",
	COMMUNITY_INVITE: "You have been invited to join [NAME] community. Please accept to take part in supporting others in the community.",
	WISH_REQUEST_DECLINED: "Request declined",
	WISH_REQUEST_ACCEPTED: "Request accepted",
	CONTACT_JOINED_APP: "Your friend [NAME] has joined WishWell",
	REMINDER: "Reminder",
	DAILY_REMINDER: "Did you gift today?",
	UPDATE_APP: "Update Available",
	FRIEND_REQUEST: "Friend Request",
	FRIEND_REQUEST_ACCEPTED: "Friend Request Accepted",
	COMMUNITY_JOIN_REQUEST: "Community Join Request",
	COMMUNITY_JOIN_ACCEPTED: "Community Join Request Accepted",
	COMMUNITY_JOIN_DECLINED: "Community Join Request Declined"
}

const TYPE_NOTIFICATION = {
	WISH_CREATE: "WISH_CREATE",
	WISH_CREATE_HOST: "WISH_CREATE_HOST",
	BLESSING_RECEIVED: "BLESSING_RECEIVED",
	GRATITUDE_RECEIVED_PERSONALISED: "GRATITUDE_RECEIVED_PERSONALISED",
	GRATITUDE_RECEIVED_GLOBAL: "GRATITUDE_RECEIVED_GLOBAL",
	COMMUNITY_INVITE: "COMMUNITY_INVITE",
	WISH_REQUEST_DECLINED: "WISH_REQUEST_DECLINED",
	WISH_REQUEST_ACCEPTED: "WISH_REQUEST_ACCEPTED",
	CONTACT_JOINED_APP: "CONTACT_JOINED_APP",
	NOTIFICATION_REMINDER: "NOTIFICATION_REMINDER",
	UPDATE_APP: "UPDATE_APP",
	FRIEND_REQUEST: "FRIEND_REQUEST",
	FRIEND_REQUEST_ACCEPTED: "FRIEND_REQUEST_ACCEPTED",
	COMMUNITY_JOIN_REQUEST: "COMMUNITY_JOIN_REQUEST",
	COMMUNITY_JOIN_ACCEPTED: "COMMUNITY_JOIN_ACCEPTED",
	COMMUNITY_JOIN_DECLINED: "COMMUNITY_JOIN_DECLINED"
}

const IMESSAGE_TEXT = {
	COMMUNITY_INVITE_REMINDER: "Hi [NAME], will you be a part of our community, [COMMUNITY], on WishWell? It\'s a place for us to give support and ask for help. Download the app to join: [LINK]. Thank you.",
	OTHERS_APP_INVITE: "Hi [NAME], will you approve a Wish I created for you? View it on WishWell by downloading the app: [LINK]. Thank you.",
	MYSELF_APP_INVITE: "Hi [NAME], will you give a minute of your time to support my Wish? View it on WishWell by downloading the app: [LINK]. Thank you."
}

const SQS_TYPES = {
	MAGIC_LINK: "MAGIC_LINK",
	WISH_CREATE_SMS:"WISH_CREATE_SMS",
	COMMUNITY_CREATE_SMS: "COMMUNITY_CREATE_SMS",
	COMMUNITY_EDIT_SMS: "COMMUNITY_EDIT_SMS",
	EDIT_USER:"EDIT_USER",
	FORGOT_PASSWORD:"FORGOT_PASSWORD",
	INVESTOR_CREATED:"INVESTOR_CREATED",
	REPORT_INCIDENT : "REPORT_INCIDENT",
	WISHWELL_THANKS : "WISHWELL_THANKS",
	VERIFY_USER_EMAIL: "VERIFY_USER_EMAIL"
}
const INVITATION_TYPE = {
	NONE: "NONE",
	COMMUNITY: "COMMUNITY",
	TAG: "TAG",
	HOST: "HOST"
}

const GEO_LOCATION_TYPE = {
	WISH:"WISH",
	BLESSING:"BLESSING",
	GRATITUDE:"GRATITUDE"
}

const APP_TOOL_TIP = [
    {
		"text":"Your compassion hub to support others through a one-minute blessing (meditation or prayer).",
		"type":1,
		"tooltip":"BLESSINGS"
    },
    {
		"text":"Add wishes to bless another. Gifting your time is a meaningful act of compassion.",
		"type":2,
		"tooltip":"ADD_WISH_TO_HOME_SCREEN"
    },
    {
		"text":"Quickly load Wishes for the day and make your time count.",
		"type":3,
		"tooltip":"QUICK_PIN"
    },
    {
		"text":"Home page>Stats",
		"type":4,
		"tooltip":"HOME_PAGE_STATS"
    },
    {
		"text":"Remove Wishes with long-press.",
		"type":5,
		"tooltip":"REMOVE_WISH_FROM_HOME_SCREEN"
    },
    {
		"text":"Create a Wish, Find Received, Requested and Global Wishes here",
		"type":6,
		"tooltip":"WISHES"
    },
    {
		"text":"Do you have a Wish? Ask for support and rally your community. If a Wish is made for another, it will be sent for approval.",
		"type":7,
		"tooltip":"CREATE_WISH"
    },
    {
		"text":"You are now a Co-Host. Click on three dots to be able to Rally friends.",
		"type":8,
		"tooltip":"COHOST"
    },
    {
		"text":"Compassion grows when witnessed. View the Map and see the good in the world.",
		"type":9,
		"tooltip":"MAP"
    },
    {
		"text":"Your feel-good feed of Wishes, Blessings, and Gratitude.",
		"type":10,
		"tooltip":"WELL"
    },
    {
		"text":"Your compassion visualized. Feel good by seeing the good.",
		"type":11,
		"tooltip":"ACCOUNT_STATS"
    },
    {
		"text":"Belong to your connected, compassionate community, supporting and uplifting each others Wishes.",
		"type":12,
		"tooltip":"COMMUNITIES"
    },
    {
		"text":"Help co-create a more compassionate world, share your story and give gratitude.",
		"type":13,
		"tooltip":"THANK_WISHWELL"
    },
    {
		"text":"Create your communities to unite people in your life. Community groups foster a sense of belonging, allowing you to safely request and offer emotional support.",
		"type":14,
		"tooltip":"CREATE_COMMUNITIES"
    },
	{
		"text":"Click to add your photo.",
		"type":15,
		"tooltip":"PROFILE_PICTURE"
    },
	{

		"text": "Set blessing type or turn on tips, this is where you customize your experience.",
		"type":16,
		"tooltip": "ACCOUNT_SETTINGS"
	},
	
]

const PAYMENT_STATUS = {
	INITIATE:"INITIATE",
	CREATED:"CREATED"
}

const PAYMENT_STATUS_RESPONSE = {
	REQUIRES_PAYMENT_METHOD: "requires_payment_method"
}
   
export {
	SWAGGER_DEFAULT_RESPONSE_MESSAGES,
	HTTP_STATUS_CODE,
	USER_TYPE,
	DB_MODEL_REF,
	DEVICE_TYPE,
	GENDER,
	STATUS,
	VALIDATION_CRITERIA,
	VALIDATION_MESSAGE,
	MESSAGES,
	CONTENT_TYPE,
	MIME_TYPE,
	REGEX,
	VERSION_UPDATE_TYPE,
	NOTIFICATION_TYPE,
	TEMPLATES,
	GRAPH_TYPE,
	MONTHS,
	MONTH_NAME,
	WEEK_NAME,
	JOB_SCHEDULER_TYPE,
	LANGUAGES,
	TOKEN_TYPE,
	timeZones,
	UPDATE_TYPE,
	DISTANCE_MULTIPLIER,
	NOTIFICATION_MSG,
	fileUploadExts,
	QUEUE_NAME,
	NOTIFICATION_DATA,
	CATEGORIES_STAUS,
	FRIEND_REQUEST_STATUS,
	NOTIFICATION_TITLE,
	FIREBASE_TOKEN,
	MODULES,
	MODULES_ID,
	WEB_ENDPOINTS,
	DEEPLINK_TYPE,
	MAIL_SENDING_TYPE,
	LOGIN_TYPE,
	PUSH_NOTIFICATIONS,
	WISH_CREATED_BY,
	WISH_INTESION,
	BLESSING_CREATED_BY,
	BLESSING_TYPE,
	BLESSING_VOICEOVER,
	BLESSING_SORT_CRITERIA,
	WISH_TAG_TYPE,
	WISH_TYPE,
	DASH_EXP_TYPE,
	WISH_SORT_CRITERIA,
	ACTION_TYPE,
	REPORT_TYPE,
	FLAG_TYPE,
	USER_SORT_CRITERIA,
	REPORT_TYPE_CRITERIA,
	LOCATION_TYPE,
	REGISTRATION_FLAG,
	HOME_LIST,
	INVESTOR_SORT_CRITERIA,
	BLESSING_PERFORM_TYPE,
	FLAGGED_BLESSING_SORT_CRITERIA,
	COMMUNITY_SORT_CRITERIA,
	COMMUNITY_LISTING_TYPE,
	FLAGGED_WISH_SORT_CRITERIA,
	GLOBAL_WISH_PIN_COUNT,
	REPORTED_POST_SORT_CRITERIA,
	FLAGGED_GRATITUDE_SORT_CRITERIA,
	GRATITUDE_TYPE,
	ADMIN_REPORT_REDIRECT,
	THANK_WISHWELL_LISTING_TYPE,
	THANK_WISHWELL_SORT_CRITERIA,
	SMS_CONTENT,
	SMS_CONTENT_ADMIN,
	ADMIN_NOTIFICATION_TYPE,
	ADMIN_EXPORT_SHEETS,
	MESSAGES_NOTIFICATION,
	TITLE_NOTIFICATION,
	TYPE_NOTIFICATION,
	SQS_TYPES,
	INVITATION_TYPE,
	GEO_LOCATION_TYPE,
	COMPASSION_MAP,
	APP_TOOL_TIP,
	PAYMENT_STATUS,
	WISH_SHARE,
	PAYMENT_STATUS_RESPONSE,
	IMESSAGE_TEXT,
};
