"use strict";
import { STATUS, TITLE_NOTIFICATION, TYPE_NOTIFICATION } from "@config/constant";
import { baseDao } from "@modules/baseDao";
import { notificationManager } from "@utils/NotificationManager";

export class DailyReminderController {
    async dailyReminder() {
        try {
            const activeUsers = await baseDao.find("users", {status: STATUS.UN_BLOCKED}, {'_id': 1});
            console.log('activeUsers = ', activeUsers);
    
            let pushParamsContact = {
                title: TITLE_NOTIFICATION.DAILY_REMINDER,
                type: TYPE_NOTIFICATION.NOTIFICATION_REMINDER
            };
            for(const activeUser of activeUsers) {
                await notificationManager.PublishNotification([{"userId":activeUser._id}], null, pushParamsContact);
            }
            return 'Daily Reminders Sent';
        } catch (error) {
            console.log("ENTERED ERROR CATCH BLOCK");
            console.log("ERROR = ", error);
        }
    }  
}

export const dailyReminderController = new DailyReminderController();