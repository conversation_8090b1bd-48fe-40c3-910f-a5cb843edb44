"use strict";
import { Request, ResponseToolkit } from "@hapi/hapi";
import * as Jo<PERSON> from "joi";
import { failActionFunction } from "@utils/appUtils";
import { communitiesControllerV1 } from "@modules/community/index";
import {
	MESSAGES,
	SWAGGER_DEFAULT_RESPONSE_MESSAGES,
	SERVER,
	REGEX,
    ACTION_TYPE,
    WISH_TAG_TYPE,
	COMMUNITY_SORT_CRITERIA,
	COMMUNITY_LISTING_TYPE,
	FLAG_TYPE
} from "@config/index";
import { responseHandler } from "@utils/ResponseHandler";
import { authorizationHeaderObj } from "@utils/validator";
export const communitiesRoute = [
    {
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/community`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const payload: CommunitiesRequest.Add = request.payload;
				const result = await communitiesControllerV1.addCommunities(payload,tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "communities"],
			description: "Create Community",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
                    name: Joi.string().trim().required(),
					purpose: Joi.string().trim().required(),
					image: Joi.string().trim().allow("").required(),
					members: Joi.array().items(
						Joi.object({
						    userId: Joi.string().trim().regex(REGEX.MONGO_ID).optional(),  
							contactId: Joi.string().trim().regex(REGEX.MONGO_ID).required(),  // this is contactId of non-app user from contact table
							countryCode: Joi.string().trim().optional(),
						    phoneNumber: Joi.string().trim().optional(),
						    isAppUser: Joi.boolean().required(),
					  }).required(),).required(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}  
	},
    {
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/community/creator-community`,  // communities creator list 
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const query: CommunitiesRequest.CommunityList = request.query;
				const result = await communitiesControllerV1.getCreatorCommunities(query,tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				console.log(error, "ERROR");
				throw error;			
			}
		},
		options: {
			tags: ["api", "communities"],
			description: "Community List for creator",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				query: Joi.object({
					userId: Joi.string().trim().regex(REGEX.MONGO_ID).required(),
					pageNo: Joi.number().optional().description("Page no"),
					limit: Joi.number().optional().description("limit"),
					searchKey: Joi.string().optional().description("Search by name"),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
    {
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/community`,  
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const query: CommunitiesRequest.CommunityList = request.query;
				const result = await communitiesControllerV1.getCommunities(query,tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				console.log(error, "ERROR");
				throw error;			
			}
		},
		options: {
			tags: ["api", "communities"],
			description: "Community List",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				query: Joi.object({
                    type:Joi.string().valid(WISH_TAG_TYPE.INVITED).optional(),
					pageNo: Joi.number().optional().description("Page no"),
					limit: Joi.number().optional().description("limit"),
					searchKey: Joi.string().optional().description("Search by name"),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
    {
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/community/community-detail`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const query: CommunitiesRequest.CommunityDetail = request.query;
				const result = await communitiesControllerV1.communityDetail(query,tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);

			}
		},
		options: {
			tags: ["api", "communities"],
			description: "Community Details",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				query: Joi.object({
					communityId: Joi.string().trim().regex(REGEX.MONGO_ID).required(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
    {
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/community/leave-community`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const payload: CommunitiesRequest.LeaveCommunity = request.payload;
				const result = await  communitiesControllerV1.leaveCommunity(payload,tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "communities"],
			description: "Leave a community",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
					communityId: Joi.string().trim().regex(REGEX.MONGO_ID).required(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
    {
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/community/accept-decline-community`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const payload: CommunitiesRequest.AcceptDecline = request.payload;
				const result = await  communitiesControllerV1.acceptDeclineCommunity(payload,tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "communities"],
			description: "Accept/decline community request",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
					communityId: Joi.string().trim().regex(REGEX.MONGO_ID).required(),
                    type: Joi.string().valid(ACTION_TYPE.ACCEPT,ACTION_TYPE.REJECT).required(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
    {
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/community/delete-community`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const payload: CommunitiesRequest.DeleteCommunity = request.payload;
				const result = await  communitiesControllerV1.deleteCommunity(payload,tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "communities"],
			description: "Delete communities",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
					communityId: Joi.string().trim().regex(REGEX.MONGO_ID).required(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	 {
		method: "PUT",
		path: `${SERVER.API_BASE_URL}/v1/community/edit-community`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const payload: CommunitiesRequest.EditCommunity = request.payload;
				const result = await  communitiesControllerV1.editCommunity(payload,tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "communities"],
			description: "Edit/Modify Community",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
					communityId: Joi.string().trim().regex(REGEX.MONGO_ID).required(),
                    name: Joi.string().trim().optional(),
					purpose: Joi.string().trim().optional(),
					image: Joi.string().trim().allow("").optional(),
					addMembers: Joi.array().items(
						Joi.object({
						    userId: Joi.string().trim().regex(REGEX.MONGO_ID).optional(),  
							contactId: Joi.string().trim().regex(REGEX.MONGO_ID).optional(),  // this is contactId of non-app user from contact table
							countryCode: Joi.string().trim().optional(),
						    phoneNumber: Joi.string().trim().optional(),
						    isAppUser: Joi.boolean().required(),
					  }).optional(),).optional(),
                    removeMembers: Joi.array().items(
						Joi.object({
						    userId: Joi.string().trim().regex(REGEX.MONGO_ID).optional(),  
							contactId: Joi.string().trim().regex(REGEX.MONGO_ID).optional(),  // this is contactId of non-app user from contact table
							countryCode: Joi.string().trim().optional(),
						    phoneNumber: Joi.string().trim().optional(),
						    isAppUser: Joi.boolean().required(),
					}).optional(),).optional(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/admin/communities`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const query: CommunitiesRequest.CommunityListing = request.query;
				const result = await communitiesControllerV1.communityListing(query);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin community"],
			description: "Community listing",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				query: Joi.object({
					userId: Joi.string().optional(),
					type: Joi.string().required().valid(COMMUNITY_LISTING_TYPE.ALL, COMMUNITY_LISTING_TYPE.FLAGGED, COMMUNITY_LISTING_TYPE.USER),
					pageNo: Joi.number().required(),
					limit: Joi.number().required(),
					sortCriteria: Joi.string().trim().valid(COMMUNITY_SORT_CRITERIA.NAME, COMMUNITY_SORT_CRITERIA.CREATED_BY, COMMUNITY_SORT_CRITERIA.CREATED_AT, COMMUNITY_SORT_CRITERIA.STATUS, COMMUNITY_SORT_CRITERIA.REPORT_COUNT).optional().default(COMMUNITY_SORT_CRITERIA.CREATED_AT),
					sortBy: Joi.number().valid(1, 0).optional(),
					searchKey: Joi.string().optional(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/admin/communities/{id}`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const params: CommunitiesRequest.CommunityId = request.params;
				const result = await communitiesControllerV1.communityDetailAdmin(params);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin community"],
			description: "Community details",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				params: Joi.object({
					id: Joi.string().required(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/admin/flagged-community-reports`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const query: CommunitiesRequest.CommunityListing = request.query;
				const result = await communitiesControllerV1.flaggedCommunityReports(query);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin community"],
			description: "Flagged community reports",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				query: Joi.object({
					communityId: Joi.string().required(),
					pageNo: Joi.number().required(),
					limit: Joi.number().required(),
					sortCriteria: Joi.string().trim().valid("createdAt").optional(),
					sortBy: Joi.number().valid(1, 0).optional(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "PUT",
		path: `${SERVER.API_BASE_URL}/v1/admin/communities/{id}`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const params: CommunitiesRequest.UnflagDeleteCommunity = request.params;
				const payload: CommunitiesRequest.UnflagDeleteCommunity = request.payload;
				const result = await communitiesControllerV1.unflagDeleteCommunity({ ...params, ...payload }, tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin community"],
			description: "Unflag/Delete Community",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				params: Joi.object({
					id: Joi.string().required(),
				}),
				payload: Joi.object({
					status: Joi.string().required().valid(FLAG_TYPE.UN_FLAGGED, FLAG_TYPE.DELETED),
					deleteReason: Joi.string().optional().allow("")
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
 ];