"use strict";
import { Request, ResponseToolkit } from "@hapi/hapi";
import * as Jo<PERSON> from "joi";
import { failActionFunction } from "@utils/appUtils";
import { friendController } from "./FriendController";
import {
    SWAGGER_DEFAULT_RESPONSE_MESSAGES,
    SERVER,
    REGEX
} from "@config/index";
import { responseHandler } from "@utils/ResponseHandler";
import { authorizationHeaderObj } from "@utils/validator";

export const friendRoute = [
    {
        method: "POST",
        path: `${SERVER.API_BASE_URL}/v1/friend/request`,
        handler: async (request: Request | any, h: ResponseToolkit) => {
            try {
                const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
                const payload: UserRequest.FriendRequest = request.payload;
                const result = await friendController.sendFriendRequest(payload, tokenData);
                return responseHandler.sendSuccess(h, result);
            } catch (error) {
                return responseHandler.sendError(request, error);
            }
        },
        config: {
            tags: ["api", "friend"],
            description: "Send Friend Request",
            auth: {
                strategies: ["UserAuth"]
            },
            validate: {
                headers: authorizationHeaderObj,
                payload: Joi.object({
                    userId: Joi.string().trim().regex(REGEX.MONGO_ID).required()
                }),
                failAction: failActionFunction
            },
            plugins: {
                "hapi-swagger": {
                    responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
                }
            }
        }
    },
    {
        method: "PUT",
        path: `${SERVER.API_BASE_URL}/v1/friend/request/respond`,
        handler: async (request: Request | any, h: ResponseToolkit) => {
            try {
                const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
                const payload: UserRequest.FriendResponse = request.payload;
                const result = await friendController.respondToFriendRequest(payload, tokenData);
                return responseHandler.sendSuccess(h, result);
            } catch (error) {
                return responseHandler.sendError(request, error);
            }
        },
        config: {
            tags: ["api", "friend"],
            description: "Respond to Friend Request",
            auth: {
                strategies: ["UserAuth"]
            },
            validate: {
                headers: authorizationHeaderObj,
                payload: Joi.object({
                    requestId: Joi.string().trim().regex(REGEX.MONGO_ID).required(),
                    accept: Joi.boolean().required()
                }),
                failAction: failActionFunction
            },
            plugins: {
                "hapi-swagger": {
                    responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
                }
            }
        }
    },
    {
        method: "GET",
        path: `${SERVER.API_BASE_URL}/v1/friend/requests`,
        handler: async (request: Request | any, h: ResponseToolkit) => {
            try {
                const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
                const query = request.query;
                const result = await friendController.getFriendRequests(query, tokenData);
                return responseHandler.sendSuccess(h, result);
            } catch (error) {
                return responseHandler.sendError(request, error);
            }
        },
        config: {
            tags: ["api", "friend"],
            description: "Get Pending Friend Requests",
            auth: {
                strategies: ["UserAuth"]
            },
            validate: {
                headers: authorizationHeaderObj,
                query: Joi.object({
                    skip: Joi.number().optional(),
                    limit: Joi.number().optional()
                }),
                failAction: failActionFunction
            },
            plugins: {
                "hapi-swagger": {
                    responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
                }
            }
        }
    },
    {
        method: "GET",
        path: `${SERVER.API_BASE_URL}/v1/friends`,
        handler: async (request: Request | any, h: ResponseToolkit) => {
            try {
                const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
                const query = request.query;
                const result = await friendController.getFriendsList(query, tokenData);
                return responseHandler.sendSuccess(h, result);
            } catch (error) {
                return responseHandler.sendError(request, error);
            }
        },
        config: {
            tags: ["api", "friend"],
            description: "Get Friends List",
            auth: {
                strategies: ["UserAuth"]
            },
            validate: {
                headers: authorizationHeaderObj,
                query: Joi.object({
                    skip: Joi.number().optional(),
                    limit: Joi.number().optional()
                }),
                failAction: failActionFunction
            },
            plugins: {
                "hapi-swagger": {
                    responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
                }
            }
        }
    }
];
