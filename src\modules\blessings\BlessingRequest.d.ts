declare namespace BlessingRequest {

	export interface Id {
		blessingId?: string;
		wishId?:string;
	}
	export interface Add {
		wishId?: string;
		listenBlessingId: string;
		listenAudio: string;
		listenDuration:string;
		audio: string;
		notes:string;
		type:string;
		userId?:string;
		audioLength?:number;
		location:Object;
		atSignup?:boolean;
	}
	export interface  GuidedBlessingDetail{
		id: string,
	}
	export interface EditGuidedBlessing extends GuidedBlessingDetail {
		image: string;
		name: string;
		blessingType: string;
		language: string;
		audioFile: string;
		voiceover: string;
		status?: string
	}
	export interface WishBlessings extends ListingRequest {
		wishId?:string
	}

	export interface FlaggedBlessing {
		blessingId?: string;
		pageNo: number;
		limit: number;
		sortBy?: number;
		sortCriteria?: string;
		searchKey?: string;
	}

	export interface UnflagDeletePerformBlessing {
		id: string;
		status: string;
		deleteReason?: string;
	}

	export interface WellLog extends ListingRequest{
		phoneNumber: string
	}
}