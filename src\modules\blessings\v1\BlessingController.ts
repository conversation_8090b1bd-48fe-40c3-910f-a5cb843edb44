"use strict";
import { FLAG_TYPE, MESSAGES, STATUS, REPORT_TYPE, MESSAGES_NOTIFICATION, TITLE_NOTIFICATION, TYPE_NOTIFICATION, ADMIN_NOTIFICATION_TYPE,GEO_LOCATION_TYPE } from "@config/constant";
import { blessingDaoV1 } from "@modules/blessings/index";
import { wishesDaoV1 } from "@modules/wishes/index";
import { userDaoV1 } from "@modules/user/index"
import { NOTIFICATION_TYPE } from "@config/constant";
import { baseDao } from "@modules/baseDao";
import { reportDaoV1 } from "@modules/reports/index";
import { toObjectId } from "@utils/appUtils";
import { notificationManager } from "@utils/NotificationManager";
import { commonControllerV1 } from "@modules/common/index";
const fetch = require('node-fetch');

export class BlessingController {
    /**
         * @function blessingPerform
         * @description user blessingPerform on wish
         *
         */
    async blessingPerform(params: BlessingRequest.Add, tokenData) {
        try {
            const step1 = await userDaoV1.findUserById(tokenData.userId);
            if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
            if(!step1.isProfileComplete) return Promise.reject(MESSAGES.ERROR.REGISTRATION_PENDING);
            let step2;
            if (params.wishId) {
                step2 = await wishesDaoV1.findWishById(params.wishId);
                if (!step2) return Promise.reject(MESSAGES.ERROR.WISH_NOT_FOUND);
            }
            await blessingDaoV1.findFormBelssings(params, tokenData);
            // if (step3) return Promise.reject(MESSAGES.ERROR.BLESSING_ALREADY_PERFORM);
            params.userId = tokenData.userId;
            if (params.wishId) params["wishDetail.wishNumber"] = step2.wishNumber ? step2.wishNumber : "";
            let wellLogs;
            let data

            // if (!params.location) {
            //     let userLocationData = await this.getLocation(tokenData.userId);
            //     params["location"] = userLocationData;
            // }
            let userLocationData = await this.getLocation(tokenData.userId,params);
              params["location"] = userLocationData;
            data = await blessingDaoV1.saveFormBelssings(params);
            //add location in location table for most compassionate
			await commonControllerV1.saveLocation(params,GEO_LOCATION_TYPE.BLESSING,data._id,tokenData.userId)
            // manage streak count
            await blessingDaoV1.saveStreakCount(params, tokenData);
            let wishData = await wishesDaoV1.findWishById(params.wishId);
            //well section log maintain for this
            if (data) wellLogs = await this.wellLogs(data, params, step2);
            //at signup time manage pinned data after blessing
            if(params.atSignup && params.wishId){
            let checkPin = await baseDao.find("pinned_wishes",{userId:tokenData.userId,status:STATUS.ACTIVE},{});
            if(checkPin && checkPin.length < 8){
                //save data in pinned wishes
                let checkPinData = await baseDao.findOne("pinned_wishes",{userId:tokenData.userId,status:STATUS.ACTIVE,subjectId:params.wishId});
                let savePinned = {
                    userId:tokenData.userId,
                    subjectId:params.wishId
                };
                if(!checkPinData) await baseDao.save("pinned_wishes",savePinned,{});

            }
            }

            //push send user data
            let ids = [];
                if(tokenData.userId==wishData.hostId){
                    ids.push(wishData.userId);
                }
                else if(tokenData.userId==wishData.userId){
                    ids.push(wishData.hostId);
                }
                else{
                ids.push(wishData.hostId);
                ids.push(wishData.userId);
                }
            let pushParams = {"message":MESSAGES_NOTIFICATION.BLESSING_RECEIVED, "title":TITLE_NOTIFICATION.BLESSING_RECEIVED, "type":TYPE_NOTIFICATION.BLESSING_RECEIVED}
            await notificationManager.CommonublishNotification(ids, params.wishId, pushParams);
            if (wellLogs) return MESSAGES.SUCCESS.BLESSSING_SUBMITTED;
            else
                return Promise.reject(MESSAGES.ERROR.SOMETHING_WENT_WRONG);

        } catch (error) {
            throw error;
        }
    }
    /**
         * @function blessingDetails
         * @description admin guided blessing detail
         *
         */
    async guidedBlessingDetail(params: BlessingRequest.GuidedBlessingDetail) {
        try {
            const step1 = await blessingDaoV1.guidedBlessingDetail(params.id);
            if (!step1) {
                return Promise.reject(MESSAGES.ERROR.INVALID_GUIDED_BLESSING);
            }
            return MESSAGES.SUCCESS.GUIDED_BLESSING_DETAIL(step1);
        } catch (error) {
            throw error;
        }
    }
    /**
      * @function editGuidedBlessing
      * @description edit guided blessing detail
      *
      */
    async editGuidedBlessing(params: BlessingRequest.EditGuidedBlessing) {
        try {
            const step1 = await blessingDaoV1.findGuidedBlessingById(params.id);
            if (!step1) {
                return Promise.reject(MESSAGES.ERROR.INVALID_GUIDED_BLESSING);
            }
            if (step1.status !== STATUS.UN_PUBLISHED) {
                return Promise.reject(MESSAGES.ERROR.GUIDED_BLESSING_UPDATE_INVALID);
            }
            await blessingDaoV1.editGuidedBlessing(params);
            return MESSAGES.SUCCESS.GUIDED_BLESSING_UPDATE;
        } catch (error) {
            throw error;
        }
    }
    /**
       * @function wishesBlessings
       * @description user can see the all blessings on wish
       *
       */
    async wishesBlessings(params: BlessingRequest.WishBlessings, tokenData) {
        try {
            const step1 = await userDaoV1.findUserById(tokenData.userId);
            if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
            const step2 = await wishesDaoV1.findWishById(params.wishId);
            if (!step2) return Promise.reject(MESSAGES.ERROR.WISH_NOT_FOUND);
            let step3 = await reportDaoV1.reportedWishes(REPORT_TYPE.BLESSING, tokenData.userId)
            let step4 = await reportDaoV1.reportedWishes(REPORT_TYPE.GRATITUDE, tokenData.userId)
            let data = await blessingDaoV1.wishesBlessing(params, step3, step4)
            return MESSAGES.SUCCESS.LIST(data);

        } catch (error) {
            throw error;
        }
    }
    /**
      * @function editGuidedBlessingStatus
      * @description update guided blessing status
      *
      */
    async editGuidedBlessingStatus(params: BlessingRequest.EditGuidedBlessing) {
        try {
            const step1 = await blessingDaoV1.findGuidedBlessingById(params.id);
            if (!step1) {
                return Promise.reject(MESSAGES.ERROR.INVALID_GUIDED_BLESSING);
            }
            if (params.status == STATUS.DELETED) {
                if (step1.status == STATUS.UN_PUBLISHED) {
                    const step2 = await blessingDaoV1.editGuidedBlessing(params);
                    return MESSAGES.SUCCESS.GUIDED_BLESSING_DELETE;
                } else {
                    return Promise.reject(MESSAGES.ERROR.GUIDED_BLESSING_DELETE_INVALID);
                }
            } else {
                const step2 = await blessingDaoV1.editGuidedBlessing(params);
                if (step1.status !== STATUS.UN_PUBLISHED) {
                    return (params.status == STATUS.ACTIVE) ? MESSAGES.SUCCESS.GUIDED_BLESSING_ACTIVATE : MESSAGES.SUCCESS.GUIDED_BLESSING_DEACTIVATE;
                } else {
                    return MESSAGES.SUCCESS.GUIDED_BLESSING_PUBLISH;
                }
            }
        } catch (error) {
            throw error;
        }
    }
    /**
      * @function flaggedBlessings
      * @description flagged blessing listing
      *
      */
    async flaggedBlessings(params: BlessingRequest.FlaggedBlessing) {
        try {
            const step1 = await blessingDaoV1.flaggedBlessings(params);
            return MESSAGES.SUCCESS.FLAGGED_BLESSING_LIST(step1);
        } catch (error) {
            throw error;
        }
    }

    /**
        * @function performBlessingDetail
        * @description perform blessing detail
        *
        */
    async performBlessingDetail(params: BlessingRequest.GuidedBlessingDetail) {
        try {
            const step1 = await blessingDaoV1.findPerformBlessingById(params.id);
            if (!step1) {
                return Promise.reject(MESSAGES.ERROR.INVALID_PERFORM_BLESSING);
            }
            const step2 = await blessingDaoV1.performBlessingDetail(params);
            return MESSAGES.SUCCESS.FLAGGED_PERFORM_BLESSING_DETAIL(step2);
        } catch (error) {
            throw error;
        }
    }

    /**
         * @function flaggedBlessingReports
         * @description flagged blessing reports
         *
         */
    async flaggedBlessingReports(params: BlessingRequest.FlaggedBlessing) {
        try {
            const step1 = await blessingDaoV1.findPerformBlessingById(params.blessingId);
            if (!step1) {
                return Promise.reject(MESSAGES.ERROR.INVALID_PERFORM_BLESSING);
            }
            const step2 = await blessingDaoV1.flaggedBlessingReports(params);
            return MESSAGES.SUCCESS.FLAGGED_BLESSING_REPORT(step2);
        } catch (error) {
            throw error;
        }
    }

    /**
        * @function wellLogs
        * @description logs maintain for well section
        *
        */
    async wellLogs(data, tokenData, wishDetail) {
        try {
           let wellLogSave={};
           wellLogSave['senderId'] = tokenData.userId;
        //    if(wishDetail)wellLogSave['receiverId'] = wishDetail.userId;
           if(wishDetail)wellLogSave['receiverId'] = wishDetail.hostId? wishDetail.hostId: wishDetail.userId;
           if(wishDetail && wishDetail.hostId)wellLogSave['hostId'] = wishDetail.hostId;
           wellLogSave['wishId'] = data.wishId?data.wishId:"";
           wellLogSave['subjectId'] = data._id; //perform blessId
           wellLogSave['subjectDetail.audio'] = data.audio?data.audio:"";
           wellLogSave['subjectDetail.notes'] = data.notes?data.notes:"";
           wellLogSave['subjectDetail.type'] = data.type?data.type:"";
           wellLogSave['subjectDetail.listenDuration'] = data.listenDuration?data.listenDuration:"";
           wellLogSave['subjectDetail.audioLength'] = data.audioLength?data.audioLength:0 // audio length is length of audio user has recorded manually from the screen
           wellLogSave['type'] = NOTIFICATION_TYPE.PERFORM_BLESS;
           if(wishDetail.image) wellLogSave["subjectDetail.wishImage"] = wishDetail.image;
           let saveData = await baseDao.save("well_logs",wellLogSave);
           return saveData;
            
        } catch (error) {
            throw error;
        }
    }

       /**
         * @function unflagDeletePerformBlessing
         * @description unflag/delete flagged perform blessing
         *
         */
    async unflagDeletePerformBlessing(params: BlessingRequest.UnflagDeletePerformBlessing, tokenData: TokenData) {
        try {
            const step1 = await blessingDaoV1.findPerformBlessingById(params.id);
            if (!step1) {
                return Promise.reject(MESSAGES.ERROR.INVALID_PERFORM_BLESSING);
            }
            const step2 = await blessingDaoV1.unflagDeletePerformBlessing(params, tokenData);
            if (params.status == FLAG_TYPE.UN_FLAGGED) {
                // await baseDao.updateMany("reports", { subjectId: toObjectId(params.id) }, { '$set': { status: STATUS.ACTIVE } }, {});
                return MESSAGES.SUCCESS.UNFLAG_PERFORM_BLESSING;
            } else {
                const step3 = await baseDao.findOne("wishes", { _id: step1.wishId }, { totalBlessings: 1 });
                if(step3 && step3.totalBlessings > 0) {
                    await baseDao.updateOne("wishes", { _id: step1.wishId }, { $inc: { totalBlessings: -1 } }, {});
                }
                const step4 = await baseDao.findOne("users", { _id: step1.userId }, { blessingCount: 1 });
                if(step4 && step4.blessingCount > 0) {
                    await baseDao.updateOne("users", { _id: step1.userId }, { $inc: { blessingCount: -1 } }, {});
                }
                // await commonControllerV1.saveAdminWellLogs(tokenData.userId, [step1.userId], step1, params.deleteReason, ADMIN_NOTIFICATION_TYPE.DELETE_BLESSING_ADMIN.TYPE);
                await notificationManager.PublishAdminNotifications([step1.userId], ADMIN_NOTIFICATION_TYPE.DELETE_BLESSING_ADMIN.TYPE, step1._id, params.deleteReason);
                return MESSAGES.SUCCESS.DELETE_PERFORM_BLESSING;
            }
        } catch (error) {
            throw error;
        }
    }
      /**
        * @function wellBlessLogs
        * @description well bless log list
        *
        */
    async wellBlessLogs(params: BlessingRequest.WellLog, tokenData) {
        try {
            const step1 = await userDaoV1.findUserById(tokenData.userId);
            if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
             let data = await blessingDaoV1.wellBlessLogs(params, tokenData)
            return MESSAGES.SUCCESS.LIST(data);

        } catch (error) {
            throw error;
        }
    }    

	async getLocation(userId,params) {
		try {
		  let address;
		  let city;
		  let state;
		  let country;
		  //get the location from user
		   if(params.location){

			if (params.location.city) { address = params.location.city; }
			if (params.location.state)
			address = address + "," + params.location.state;
			city = params.location.city;
			state = params.location.state;
			country = params.location.country;

     		}else{
				const userProfileAddress = await userDaoV1.findOne("users", { _id: toObjectId(userId) }, {} );
				if (userProfileAddress.city) { 
					address = userProfileAddress.city; 
					city = userProfileAddress.city;
				}else{
					return  Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
				}
				if (userProfileAddress.state) {
					address = address + "," + userProfileAddress.state;
					state = userProfileAddress.state;
					country = userProfileAddress.country;
				}
				
		    }
	    	
		 const apiUrl = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(address)}&key=${process.env.GOOGLE_API_KEY}`;
		  return new Promise((resolve, reject) => {
			// Make the API request
			fetch(apiUrl)
			  .then((response) => response.json())
			  .then((data) => {
				// Extract latitude and longitude from the response
				
				if (data.results.length > 0) {
					if(!data.results[0].geometry.location){
						reject(MESSAGES.ERROR.LOCATION_NOT_FOUND);
						//return  Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
						
                    }
					const location = data.results[0].geometry.location;
					const latitude = location.lat;
					const longitude = location.lng;
					let retData = {
							address: city,
							coordinates: [longitude, latitude],
							city:city,
							state: state,
							country:country
					};
					resolve(retData);
			
				} else {
				 console.error("Location not found.222");
				  reject(MESSAGES.ERROR.LOCATION_NOT_FOUND);
				  //return  Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
				  
				}
			  })
			  .catch((error) => {
				console.error("Error:", error);
				reject(MESSAGES.ERROR.LOCATION_NOT_FOUND);
				
			  });
		  });
		} catch (err) {
		    return  Promise.reject(MESSAGES.ERROR.SOMETHING_WENT_WRONG);
		}
	}

}

export const blessingController = new BlessingController();