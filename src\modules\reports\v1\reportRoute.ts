"use strict";
import { Request, ResponseToolkit } from "@hapi/hapi";
import * as Jo<PERSON> from "joi";
import { failActionFunction } from "@utils/appUtils";
import { reportControllerV1 } from "@modules/reports/index";
import {
	SWAGGER_DEFAULT_RESPONSE_MESSAGES,
	SERVER,
	REGEX,
	REPORT_TYPE_CRITERIA,
	REPORT_TYPE,
	REPORTED_POST_SORT_CRITERIA
} from "@config/index";
import { responseHandler } from "@utils/ResponseHandler";
import { authorizationHeaderObj } from "@utils/validator";
export const reportRoute = [
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/report/report-wish`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const payload: ReportRequest.ReportWish = request.payload;
				const result = await  reportControllerV1.reportWish(payload,tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "report"],
			description: "Report a wish",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
					subjectId: Joi.string().trim().regex(REGEX.MONGO_ID).required(),
            		reportType:Joi.string().valid(REPORT_TYPE_CRITERIA.FALSE_INFORMATION,REPORT_TYPE_CRITERIA.HATE_SPEECH_OR_SYMBOL,REPORT_TYPE_CRITERIA.SPAM,REPORT_TYPE_CRITERIA.SUSPICIOUS).required(),
					description: Joi.string().required()
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/report/report-community`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const payload: ReportRequest.ReportCommunity = request.payload;
				const result = await  reportControllerV1.reportCommunity(payload,tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "report"],
			description: "Report a community",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
					subjectId: Joi.string().trim().regex(REGEX.MONGO_ID).required(),
				//	type: Joi.string().valid(REPORT_TYPE.COMMUNITY,REPORT_TYPE.BLESSING,REPORT_TYPE.WISH).required(),
            		reportType:Joi.string().valid(REPORT_TYPE_CRITERIA.FALSE_INFORMATION,REPORT_TYPE_CRITERIA.HATE_SPEECH_OR_SYMBOL,REPORT_TYPE_CRITERIA.SPAM,REPORT_TYPE_CRITERIA.SUSPICIOUS).required(),
					description: Joi.string().required()
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/report/report`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const payload: ReportRequest.Report = request.payload;
				const result = await  reportControllerV1.report(payload,tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "report"],
			description: "Report data",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
					subjectId: Joi.string().trim().regex(REGEX.MONGO_ID).required(),
            		reportType:Joi.string().valid(REPORT_TYPE_CRITERIA.FALSE_INFORMATION,REPORT_TYPE_CRITERIA.HATE_SPEECH_OR_SYMBOL,REPORT_TYPE_CRITERIA.SPAM,REPORT_TYPE_CRITERIA.SUSPICIOUS).required(),
					description: Joi.string().required(),
					type: Joi.string().valid(REPORT_TYPE.COMMUNITY,REPORT_TYPE.BLESSING,REPORT_TYPE.WISH,REPORT_TYPE.GRATITUDE).required(),

				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/admin/reported-posts/{id}`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const query: ReportRequest.ReportedPosts = request.query;
				const params: ReportRequest.ReportedPosts = request.params;
				const result = await reportControllerV1.reportedPosts({ ...query, ...params });
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin user"],
			description: "Reported posts listing",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				query: Joi.object({
					pageNo: Joi.number().required(),
					limit: Joi.number().required(),
					sortCriteria: Joi.string().trim().valid(REPORTED_POST_SORT_CRITERIA.POST_NUMBER, REPORTED_POST_SORT_CRITERIA.TYPE, REPORTED_POST_SORT_CRITERIA.DATE, REPORTED_POST_SORT_CRITERIA.STATUS).optional().default(REPORTED_POST_SORT_CRITERIA.DATE),
					sortBy: Joi.number().valid(1, 0).optional(),
					searchKey: Joi.string().optional(),
				}),
				params: Joi.object({
					id: Joi.string().required()
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
];