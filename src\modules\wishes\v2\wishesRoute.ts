"use strict";
import { Request, ResponseToolkit } from "@hapi/hapi";
import * as <PERSON><PERSON> from "joi";
import { failActionFunction } from "@utils/appUtils";
import { wishesControllerV2 } from "@modules/wishes/index";
import {
	MESSAGES,
	SWAGGER_DEFAULT_RESPONSE_MESSAGES,
	SERVER,
	WISH_INTESION,
	REGEX,
	WISH_TYPE,
	ACTION_TYPE,
	STATUS
} from "@config/index";
import { responseHandler } from "@utils/ResponseHandler";
import { authorizationHeaderObj } from "@utils/validator";
export const wishesRoute = [
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v2/wishes`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const payload: WishesRequest.Add = request.payload;
				const result = await wishesControllerV2.addWishes(payload, tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "wishes v2"],
			description: "Add Wishes",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
					title: Joi.string().trim().optional(),
					description: Joi.string().trim().required(),
					wishType: Joi.string().trim().required().valid(WISH_TYPE.MYSELF, WISH_TYPE.OTHER),
					hostId: Joi.string().trim().regex(REGEX.MONGO_ID).optional(),
					hostDetail: Joi.object({
						userId: Joi.string().trim().regex(REGEX.MONGO_ID).optional(),
						countryCode: Joi.string().trim().optional(),
						phoneNumber: Joi.string().trim().optional(),
						isAppUser: Joi.boolean().required(),
						contactId: Joi.string().trim().regex(REGEX.MONGO_ID).optional(),
						name: Joi.string().trim().optional()
					}).optional(),
					isGlobalWish: Joi.boolean().default(false).required(),
					visibility: Joi.string().valid("PUBLIC", "PRIVATE").default("PUBLIC").optional(),
					intension: Joi.string().trim().required().valid(WISH_INTESION.HEALTH, WISH_INTESION.LOVE, WISH_INTESION.PEACE, WISH_INTESION.ABUNDANCE),
					image: Joi.string().trim().allow("").optional(),
					communityId: Joi.array().items(Joi.string().trim().regex(REGEX.MONGO_ID).optional()).optional(),
					tagContacts: Joi.array().items(
						Joi.object({
							userId: Joi.string().trim().regex(REGEX.MONGO_ID).optional(),
							contactId: Joi.string().trim().regex(REGEX.MONGO_ID).optional(),
							countryCode: Joi.string().trim().optional(),
							phoneNumber: Joi.string().trim().optional(),
							name: Joi.string().trim().optional(),
							isAppUser: Joi.boolean().required(),
						}).optional(),).optional(),
					cohosts: Joi.array().items(Joi.string()).optional(),
					location: Joi.object({
						address: Joi.string().trim().allow("").optional(),
						coordinates: Joi.array().required(),
						city: Joi.string().required(),
						country: Joi.string().required(),
						state: Joi.string().required()
					}).optional(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "PUT",
		path: `${SERVER.API_BASE_URL}/v2/wishes/edit-wish`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const payload: WishesRequest.EditWish = request.payload;
				const result = await wishesControllerV2.editWish(payload, tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "wishes v2"],
			description: "Edit/Modify wish",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
					wishId: Joi.string().trim().regex(REGEX.MONGO_ID).required(),
					title: Joi.string().trim().optional(),
					description: Joi.string().trim().optional(),
					intension: Joi.string().trim().optional().valid(WISH_INTESION.HEALTH, WISH_INTESION.LOVE, WISH_INTESION.PEACE, WISH_INTESION.ABUNDANCE),
					image: Joi.string().trim().allow("").optional(),
					addCommunityId: Joi.array().items(Joi.string().trim().regex(REGEX.MONGO_ID).optional()).optional(),
					removeCommunityId: Joi.array().items(Joi.string().trim().regex(REGEX.MONGO_ID).optional()).optional(),
					addTagContacts: Joi.array().items(
						Joi.object({
							userId: Joi.string().trim().regex(REGEX.MONGO_ID).optional(),
							contactId: Joi.string().trim().regex(REGEX.MONGO_ID).optional(),
							countryCode: Joi.string().trim().optional(),
							phoneNumber: Joi.string().trim().optional(),
							isAppUser: Joi.boolean().required(),
						}).optional(),).optional(),
					removeTagContacts: Joi.array().items(
						Joi.object({
							userId: Joi.string().trim().regex(REGEX.MONGO_ID).optional(),
							contactId: Joi.string().trim().regex(REGEX.MONGO_ID).optional(),
							countryCode: Joi.string().trim().optional(),
							phoneNumber: Joi.string().trim().optional(),
							isAppUser: Joi.boolean().required(),
						}).optional(),).optional(),
					addCohosts: Joi.array().items(Joi.string()).optional(),
					removeCohosts: Joi.array().items(Joi.string()).optional(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v2/wishes/public`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const query = request.query;
	
				const result = await wishesControllerV2.getPublicWishes(query, tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "wishes v2"],
			description: "Get All Public Wishes",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				query: Joi.object({
					pageNo: Joi.number().optional().description("Page number for pagination"),
					limit: Joi.number().optional().description("Number of wishes per page")
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v2/wishes/public-wish-detail`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const params: WishesRequest.WishDetail = request.query;
				const result = await wishesControllerV2.publicWishDetail(params);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "wishes v2"],
			description: "Get Public Wish Details (No Authentication Required)",
			auth: false, // 🔥 NO AUTHENTICATION REQUIRED 🔥
			validate: {
				query: Joi.object({
					wishId: Joi.string().trim().regex(REGEX.MONGO_ID).required()
				}),
				failAction: failActionFunction,
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	}
];