"use strict";

const mongoose = require("mongoose");
import { Document, model, Model, Schema } from "mongoose";
import { users } from "@modules/models";
import { DB_MODEL_REF, STATUS, WISH_CREATED_BY, FLAG_TYPE } from "@config/constant";
export interface Communities extends Document {
    communityNumber: number;
    name: string;
    purpose: string;
    userId:string;
    type:string;
    status: string;
    image: string;
    createdBy:string
    created: number;
}
const unflagHistorySchema = new Schema({
    adminId: { type: Schema.Types.ObjectId, required: true },
    _id: false
}, {
    versionKey: false,
    timestamps: true
});
const communitiesSchema: Schema = new mongoose.Schema({
    _id: { type: Schema.Types.ObjectId, required: true, auto: true },
    communityNumber: { type: Number, required: false },
    name: { type: String, required: false },
    purpose: { type: String, required: false},
    userId: { type: Schema.Types.ObjectId, required: true },
    image: { type: String, required: false, default: "" },
    visibility: { type: String, enum: ["PUBLIC", "PRIVATE"], default: "PUBLIC" },
    isFlagged: { type: Boolean, required: false, default: false }, // for admin (if admin unflags the community from admin panel)
    flagStatus: {
        type: String,
        required: false,
        enum: [FLAG_TYPE.FLAGGED, FLAG_TYPE.UN_FLAGGED, STATUS.ACTIVE, STATUS.DELETED],
        default: STATUS.ACTIVE
    }, // for admin (status to show in admin flagged communities module)
    unflagHistory: [unflagHistorySchema],
    reportCount: { type: Number, required: true, default: 0 },
    createdBy: {
        type: String,
        enum: [WISH_CREATED_BY.USER, WISH_CREATED_BY.ADMIN],
        default: WISH_CREATED_BY.USER
    },
    status: {
        type: String,
        enum: [STATUS.ACTIVE, STATUS.IN_ACTIVE, STATUS.UN_PUBLISHED,STATUS.DELETED, STATUS.PENDING],
        default: STATUS.ACTIVE
    },
    deleteReason: { type: String, required: false },
    deletedAt: { type: Date, required: false },
    created: { type: Number, default: Date.now }
}, {
    versionKey: false,
    timestamps: true
});

communitiesSchema.index({ userId: 1 });
communitiesSchema.index({ status: 1 });
communitiesSchema.index({ created: -1 });
communitiesSchema.pre<Communities>('save', async function (next) {
    if (this.isNew) {
        const lastEntry = await communities.findOne({}, {}, { sort: { createdAt: -1 } });
        const lastKeyValue = lastEntry ? (lastEntry.communityNumber == undefined) ? 0 : lastEntry.communityNumber : 0;
        this.communityNumber = lastKeyValue + 1;
    }
    next();
});

communitiesSchema.post<Communities>('save', async function (doc) {
    const updateCommunityCount = await users.updateOne({ _id: doc.userId }, { $inc: { communityCount: 1 } }, {});
});

// Export communities
export const communities: Model<Communities> = model<Communities>(DB_MODEL_REF.COMMUNITIES, communitiesSchema);