"use strict";
import { SERVER, SQS_TYPES } from "@config/index";
import { Producer } from 'sqs-producer';
import { SQSClient } from '@aws-sdk/client-sqs';
import { Consumer } from 'sqs-consumer';
import * as AWS from "aws-sdk";
import { mailManager } from "@lib/MailManager";
import { smsManager } from "./SMSManager";
const SQS = new AWS.SQS({apiVersion: '2012-11-05'});

let producerSqsConn = null;
let consSqsConn = null;

export class AwsSqs {
	init() {
		try {
			const producer = Producer.create({
				queueUrl: SERVER.AWS_SQS.QUEUE_URL,
				region: SERVER.AWS_IAM_USER.REGION,
				sqs: new SQSClient({
					region: SERVER.AWS_IAM_USER.REGION,
					credentials: {
						accessKeyId: SERVER.AWS_IAM_USER.ACCESS_KEY_ID,
						secretAccessKey: SERVER.AWS_IAM_USER.SECRET_ACCESS_KEY
					}
				})
			});
			producerSqsConn = producer;
			console.log("[SQS] connectiong with SQS" + producer.queueUrl);
			// this.queueList();
		} catch (error) {
			console.log("[SQS] something not right", error);
		}
	}

	queueList() {
		SQS.listQueues({}, function(err, data) {
			if (err) {
			  	console.log("Error", err);
			} else {
			  	console.log("QUEUE LIST", data);
			}
		});
	}

	// deleteQueue() {
	// 	const params = {
	// 		QueueUrl: SERVER.AWS_SQS.QUEUE_URL
	// 	};
	// 	SQS.deleteQueue(params, function(err, data) {
	// 		if (err) {
	// 		  	console.log("Error", err);
	// 		} else {
	// 		  	console.log("QUEUE DELETE", data);
	// 		}
	// 	});
	// }


  checkQueueexist(){
    const params = {
      QueueName: SERVER.AWS_SQS.QUEUE_NAME
    };
    SQS.getQueueUrl(params, function (err, data) {
      if (err){
        console.log("errrrr============>", err.message); // an error occurred
        let createParams = {
          QueueName: SERVER.AWS_SQS.QUEUE_NAME,
          Attributes: {
              'FifoQueue': 'true',
              'DeduplicationScope': 'messageGroup',
              'SqsManagedSseEnabled': 'false',
          }
        };
        SQS.createQueue(createParams, function (err, data) {
          if (err) console.log("errrrr======*****======>", err.message);  // an error occurred
          else console.log("// successful response ",data);            // successful response
      });

      } 
      else console.log("Existed Queue",data);           // successful response
    });
  }

	signupMagicLinkProducer(data: any) {
		const message = {
			id: new Date().getTime().toString(),
			// body: data.message,
			body: JSON.stringify(data),
			groupId: new Date().getTime().toString(),
			deduplicationId: new Date().getTime().toString()
			// messageAttributes:[{type:"SMS"}]
		};
		producerSqsConn.send([message]);
		this.signupMagicLinkConsumer();
	}

	signupMagicLinkConsumer() {
		let consumer = Consumer.create({
			queueUrl: SERVER.AWS_SQS.QUEUE_URL,
			region: SERVER.AWS_IAM_USER.REGION,
			handleMessage: async (message: any) => {
				console.log(JSON.parse(message.Body), 'consumer message')
				const messageBody = JSON.parse(message.Body);
				switch(messageBody.type) {
					case SQS_TYPES.MAGIC_LINK:
						console.log(messageBody.type,'===========================message.Body.type...SQS_TYPES.MAGIC_LINK')
						let res = await mailManager.magicLinkSignUp({
							"email": messageBody.email,
							"deeplink": messageBody.deeplink,
						});
						if(res == false){
							let data = {
							message: "Magic Link Signup",
							email: messageBody.email,
							deeplink: messageBody.deeplink,
							type: SQS_TYPES.MAGIC_LINK
							}
							this.signupMagicLinkProducer(data)
						}
					break;

					case SQS_TYPES.VERIFY_USER_EMAIL:
						let verifyUserEmail = await mailManager.verifyUserEmail({
							"email": messageBody.email,
							"deeplink": messageBody.deeplink,
						});
						if(verifyUserEmail == false){
							let data = {
							message: "Verify User Email",
							email: messageBody.email,
							deeplink: messageBody.deeplink,
							type: SQS_TYPES.VERIFY_USER_EMAIL
							}
							this.signupMagicLinkProducer(data)
						}
					break;

					case SQS_TYPES.WISH_CREATE_SMS:
						console.log(messageBody.type,'===========================message.Body.type')
						console.log(messageBody.countryCode,messageBody.phoneNumber,messageBody.SMS_CONTENT,'===========================message.Body.type')
						await smsManager.sendMessageViaAWS(messageBody.countryCode, messageBody.phoneNumber, messageBody.SMS_CONTENT);
					break;

					case SQS_TYPES.COMMUNITY_CREATE_SMS:
						console.log(messageBody.type,'===========================message.Body.type')
						await smsManager.sendMessageViaAWS(messageBody.countryCode, messageBody.phoneNumber, messageBody.SMS_CONTENT);
					break;
						
					case SQS_TYPES.COMMUNITY_EDIT_SMS:
						console.log(messageBody.type,'===========================message.Body.type')
						await smsManager.sendMessageViaAWS(messageBody.countryCode, messageBody.phoneNumber, messageBody.SMS_CONTENT);
					break;

					case SQS_TYPES.EDIT_USER:
						console.log(messageBody.type,'===========================message.Body.type')
						await smsManager.sendMessageViaAWS(messageBody.countryCode, messageBody.phoneNumber, messageBody.SMS_CONTENT);
					break;

					case SQS_TYPES.FORGOT_PASSWORD:
						console.log(messageBody.type,'===========================message.Body.type')
						await mailManager.forgotPasswordMail({"email": messageBody.email, "name": messageBody.name, "url": messageBody.url});
					break;

					case SQS_TYPES.INVESTOR_CREATED:
						console.log(messageBody.type,'===========================message.Body.type')
						mailManager.investorInvitationMail({ "name": messageBody.name, "email": messageBody.email, "randomString": messageBody.randomString });
					break;

					case SQS_TYPES.REPORT_INCIDENT:
						console.log(messageBody.type,'===========================message.Body.type')
						mailManager.incidenReportdMail({ type:messageBody.reportType, subjectId:messageBody.subjectId});
					break;

					case SQS_TYPES.WISHWELL_THANKS:
						console.log(messageBody.type,'===========================message.Body.type')
						mailManager.thanksWishwellMail(messageBody.step1,messageBody.params);

					break;

				}
			},
			sqs: new SQSClient({
				region: SERVER.AWS_IAM_USER.REGION,
				credentials: {
					accessKeyId: SERVER.AWS_IAM_USER.ACCESS_KEY_ID,
					secretAccessKey: SERVER.AWS_IAM_USER.SECRET_ACCESS_KEY
				}
			})
		});
		consSqsConn = consumer;
		consSqsConn.on('error', (err) => {
			console.error(err.message, '2');
		});
		consSqsConn.on('processing_error', (err) => {
			console.error(err.message, '3');
		});
		consSqsConn.start();
	}

  

  // Function to receive and process messages
 async receiveAndProcessMessages () {
    try {
      while (true) {
        const params = {
          QueueUrl: SERVER.AWS_SQS.QUEUE_URL,
          MaxNumberOfMessages: 10,  // Adjust the number of messages to retrieve
        };

        const response = await SQS.receiveMessage(params).promise();
        // Check if any messages were received
        if (response.Messages && response.Messages.length > 0) {
          for (const message of response.Messages) {
            console.log(`Message Body*************************: ${message.Body}`);
            
            // Process the message as needed

            // Delete the message from the queue
            const receiptHandle = message.ReceiptHandle;
            await SQS.deleteMessage({ QueueUrl: SERVER.AWS_SQS.QUEUE_URL, ReceiptHandle: receiptHandle }).promise();
          }
        } else {
          console.log("// No more messages in the queue //")
          break;  // No more messages in the queue
        }
      }
    } catch (error) {
      console.error('Error:', error);
    }
  };




	// startPublisher() {
	// 	const message = {
	// 		id: new Date().getTime().toString(),
	// 		body: JSON.stringify([{ "name": "neha", "type": "f" }]),
	// 		groupId: new Date().getTime().toString(),
	// 		// groupId: 'your-group-iddf',// Set your desired message group ID
	// 		deduplicationId: new Date().getTime().toString()
	// 		// messageAttributes:[{type:"SMS"}]
	// 	};
	// 	producerSqsConn.send([message]);
	// 	this.startConsumer();
	// }

	// startConsumer() {
	// 	let consumer = Consumer.create(
	// 		{
	// 			queueUrl: SERVER.AWS_SQS.QUEUE_URL,
	// 			region: SERVER.AWS_IAM_USER.REGION,
	// 			handleMessage: async (message) => {
	// 				console.log(JSON.parse(message.Body), 'mesg here')

	// 			},
	// 			sqs: new SQSClient({
	// 				region: SERVER.AWS_IAM_USER.REGION,
	// 				credentials: {
	// 					accessKeyId: SERVER.AWS_IAM_USER.ACCESS_KEY_ID,
	// 					secretAccessKey: SERVER.AWS_IAM_USER.SECRET_ACCESS_KEY
	// 				}
	// 			})
	// 		});
	// 	consSqsConn = consumer;
	// 	consSqsConn.on('error', (err) => {
	// 		console.error(err.message, '2');
	// 	});

	// 	consSqsConn.on('processing_error', (err) => {
	// 		console.error(err.message, '3');
	// 	});
	// 	consSqsConn.start();

	// }

}

export const awsSQS = new AwsSqs();