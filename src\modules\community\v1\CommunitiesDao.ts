"use strict";
import { BaseDao,baseDao } from "@modules/baseDao/BaseDao";
import { STATUS, USER_TYPE,WISH_TAG_TYPE, COMMUNITY_SORT_CRITERIA, DB_MODEL_REF, REPORT_TYPE, FLAG_TYPE, COMMUNITY_LISTING_TYPE } from "@config/constant";
import { escapeSpecial<PERSON>haracter } from "@utils/appUtils";
import { toObjectId } from "@utils/appUtils";


export class CommunitiesDao extends BaseDao {
    /**    
	 * @function findCommunityById
	 */
	async findCommunityById(communityId: string, project = {}) {
		try {
			const query: any = {};
			query._id = toObjectId(communityId);
			query.status ={ '$nin': [STATUS.DELETED,STATUS.IN_ACTIVE,STATUS.UN_PUBLISHED]};
			const projection = (Object.values(project).length) ? project : { createdAt: 0, updatedAt: 0 };
			return await this.findOne("communities", query, projection);
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function addCommunities
	 */
	async addCommunities(params: CommunitiesRequest.Add) {
		try {
			return await this.save("communities", params);
		} catch (error) {
			throw error;
		}
	}
    /**
	 * @function addMember
	 */
	async addMember(params) {
		try {
			
			return await this.insertMany("members", params,{});
		} catch (error) {
			throw error;
		}
	}
    /**
	 * @function getCreatorCommunities
	 */
	async getCreatorCommunities(params: CommunitiesRequest.CommunityList,reportList?) {
		try {
			const aggPipe = [];
			const match: any = {};
			match['userId'] = toObjectId(params.userId)
			match['status'] = { '$nin': [STATUS.DELETED,STATUS.IN_ACTIVE,STATUS.UN_PUBLISHED]}
			if(reportList && reportList.length) match['_id'] ={ '$nin': reportList}
			if (params.searchKey) {
				params.searchKey = escapeSpecialCharacter(params.searchKey);
				match["$or"] = [
					{ purpose: { "$regex": params.searchKey, "$options": "i" } },
                    { name: { "$regex": params.searchKey, "$options": "i" } },
				];
			}
			aggPipe.push({ "$match": match });
			aggPipe.push({
				"$project": {
					_id:1,userId:1,name:1,purpose:1,image:1,created:1,status:1,
				}
			});
			aggPipe.push({ "$sort": {created: -1} });
			const options = {};
			// let counData = await this.aggregate("communities", aggPipe);
            // let total = counData.length;
			let response = await this.paginate("communities", aggPipe, params.limit, params.pageNo, options, true);
			// response.total = total;
			return response;
		} catch (error) {
			throw error;
		}
	}
     /**
	 * @function getCommunities
	 */
	async getCommunities(params: CommunitiesRequest.CommunityList,reportList?) {
		try {
			const aggPipe = [];
			const match: any = {};
			match['userId'] = toObjectId(params.userId)
			match['status'] = { '$nin': [STATUS.DELETED,STATUS.IN_ACTIVE,STATUS.REJECTED,STATUS.PENDING]}
			if(reportList && reportList.length) match['communityId'] ={ '$nin': reportList}
			if(params.type){
				match['status'] = { '$eq': STATUS.PENDING}
				match['type'] = { '$in': [WISH_TAG_TYPE.INVITED,WISH_TAG_TYPE.CONTACTS]}
			}
            aggPipe.push({
				"$lookup": {
					"from": "communities",
					"let": { "communityId": "$communityId" },
					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$_id", "$$communityId"]
										},
										{
											"$eq": ["$status", STATUS.ACTIVE]
										}
									]
								}

						 },
						},
						{ "$project": {_id:1,name:1,purpose:1,image:1,userId:1,createdAt:1} }
					],
					"as": "communityDetail"
				}
			});
			aggPipe.push({
				'$unwind': {
					"path": "$communityDetail",
					"preserveNullAndEmptyArrays": false
				}
			});
			aggPipe.push({
				"$lookup": {
					"from": "members",
					"let": { "communityId": "$communityId" },
					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$communityId", "$$communityId"]
										},
										{
											"$eq": ["$status", STATUS.ACTIVE]
										},
										{
											"$ne": ["$type", WISH_TAG_TYPE.INVITED]
										}
									]
								}

						 },
						},
						{ "$project": {_id:1,userId:1,type:1,status:1} }
					],
					"as": "members"
				}
			});
			if (params.searchKey) {
				params.searchKey = escapeSpecialCharacter(params.searchKey);
				match["$or"] = [
                    { 'communityDetail.name': { "$regex": params.searchKey, "$options": "i" } },
				];
			}
			aggPipe.push({ "$match": match });
			aggPipe.push({
				"$project": {
					_id:1, communityDetail:1,members:1,status:1
				}
			});
			aggPipe.push({ "$sort": {"communityDetail.createdAt": -1} });
			const options = {};
			// let counData = await this.aggregate("members", aggPipe);
            // let total = counData.length;
			let response = await this.paginate("members", aggPipe, params.limit, params.pageNo, options, true);
			// response.total = total;
			return response;
		} catch (error) {
			throw error;
		}
	}
    /**
	 * @function communityDetail
	 */
	async communityDetail(params) {
		try {
			const aggPipe = [];
			const match: any = {};
			match['_id'] = toObjectId(params.communityId)
			aggPipe.push({
				"$lookup": {
					"from": "users",
					"let": { "userId": "$userId" },

					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$_id", "$$userId"]
										}
									]
								}
						 },
						},
						{ "$project": {_id:0,  "profilePicture": 1,"firstName": 1,"lastName": 1
						} }
					],
					"as": "userDetail"
				}
			});
			aggPipe.push({
				'$unwind': {
					"path": "$userDetail",
					"preserveNullAndEmptyArrays": true
				}
			});
			aggPipe.push({ "$match": match });
			aggPipe.push({ "$sort": { created: -1} });
			aggPipe.push({
				"$project": {
					_id:1,name:1, purpose:1,image:1,userId:1,type:1,userDetail:1,created:1,
				}
			});
			const options = {};
			let response = await this.aggregate("communities", aggPipe, options);
			return response;
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function findMembers
	 */
	async findMembers(params) {
		try {
			const aggPipe = [];
			const match: any = {};
			match['communityId'] = toObjectId(params.communityId);
			match['type'] = { '$in': [WISH_TAG_TYPE.CONTACTS, WISH_TAG_TYPE.CREATOR, WISH_TAG_TYPE.INVITED] };
			match['status'] = { '$in': [STATUS.ACTIVE, STATUS.PENDING]};
			aggPipe.push({
				"$lookup": {
					"from": "users",
					"let": { "userId": "$userId" },
					"pipeline": [
						{
							"$match": {

								"$expr":
								{
									$and: [
										{
											"$eq": ["$_id", "$$userId"]
										}
									]
								}

						 },
						},
						{ "$project": {_id:1, firstName:1, lastName:1,"profilePicture": 1} }
					],
					"as": "userDetail"
				}
			});
			aggPipe.push({
				'$unwind': {
					"path": "$userDetail",
					"preserveNullAndEmptyArrays": true
				}
			})
			aggPipe.push({ "$match": match });
			aggPipe.push({ "$sort": { created: -1} });
			aggPipe.push({
				"$project": {
					_id:1, userId:1,userDetail:1,status:1,countryCode:1,phoneNumber:1,type:1,contactId:1
				
				}
			});
			const options = {};
			let response = await this.aggregate("members", aggPipe, options);
			return response;
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function communityListing
	 */
	async communityListing(params: CommunitiesRequest.CommunityListing) {
		try {
			let match: any = {};
			let aggPipe: any = [];
			let sort: any = {};

			// match["status"] = STATUS.ACTIVE;

			if(params.type == COMMUNITY_LISTING_TYPE.FLAGGED) {
				match["flagStatus"] = { $in: [FLAG_TYPE.FLAGGED, FLAG_TYPE.UN_FLAGGED, FLAG_TYPE.DELETED] }
			}

			if(params.type == COMMUNITY_LISTING_TYPE.USER) {
				match["userId"] = toObjectId(params.userId);
			}

			const userLookup = {
				from: DB_MODEL_REF.USER,
				localField: "userId",
				foreignField: "_id",
				as: "userDetails"
			};

			if(params.searchKey) {
				params.searchKey = escapeSpecialCharacter(params.searchKey);
				match = {
					...match,
					$or: [
						{
							$expr: {
								$regexMatch: {
									input: { $concat: ['$userDetails.firstName', ' ', '$userDetails.lastName'] },
									regex: params.searchKey,
									options: 'i'
								}
							}
						},
						{ "name": { "$regex": params.searchKey, "$options": "i" } },
						{ "flagStatus": { "$regex": params.searchKey, "$options": "i" } }
					]
				}
			}

			if(params.type != COMMUNITY_LISTING_TYPE.USER) {
				aggPipe.push({ "$lookup": userLookup });
				aggPipe.push({ "$unwind": "$userDetails" });
			}
			aggPipe.push({ "$match": match });

			const project = {
				_id: 1,
				name: 1,
				status: 1,
				flagStatus: 1,
				reportCount: 1,
				createdAt: 1,
				deletedAt: 1,
				deleteReason: 1,
				"userDetails._id": 1,
				"userDetails.firstName": 1,
				"userDetails.lastName": 1,
				"userDetails.profilePicture": 1
			};

			aggPipe.push({ "$project": project });

			aggPipe.push({
				$addFields: {
					flagStatus: {
						$cond: [
							{ $eq: ["$status", STATUS.DELETED] },  // Check if status is "DELETED"
							"$status",                             // If true, set flagStatus to the value of status
							"$flagStatus"                          // Otherwise, keep flagStatus as is
						]
					}
				}
			});

			if (params.sortBy == 1) {
				params.sortBy = 1;
			} else {
				params.sortBy = -1;
			}

			if(params.sortCriteria == COMMUNITY_SORT_CRITERIA.NAME) {
				sort = { "name": params.sortBy }
			} else if (params.sortCriteria == COMMUNITY_SORT_CRITERIA.CREATED_BY) {
                sort = { "userDetails.firstName": params.sortBy, "userDetails.lastName": params.sortBy }
			} else if (params.sortCriteria == COMMUNITY_SORT_CRITERIA.STATUS) {
				sort = { "flagStatus": params.sortBy }
			} else if (params.sortCriteria == COMMUNITY_SORT_CRITERIA.CREATED_AT) {
                sort = { "createdAt": params.sortBy }
			}

			aggPipe.push({ "$sort": sort });

			const options = { collation: true };
			return this.paginate("communities", aggPipe, params.limit, params.pageNo, options, true);
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function communityDetailAdmin
	 */
	async communityDetailAdmin(params: CommunitiesRequest.CommunityId) {
		try {
			let match: any = {};
			let aggPipe: any = [];

			match["_id"] = toObjectId(params.id);	

			const project = {
				_id: 1,
				name: 1,
				purpose: 1,
				createdAt: 1,
				flagStatus: 1,
				reportCount: 1,
				unflagHistory: 1,
				deleteReason: 1,
				deletedAt: 1,
				image: 1,
				memberCount: { $size: "$memberCount" },
				"userDetails._id": 1,
				"userDetails.firstName": 1,
				"userDetails.lastName": 1,
				"userDetails.profilePicture": 1,
				wishDetail: 1,
				wishCount: { $size: "$wishDetail" }
			}
			
			aggPipe.push({ "$match": match });

			const userLookup = {
				from: DB_MODEL_REF.USER,
				localField: "userId",
				foreignField: "_id",
				as: "userDetails"
			}

			const memberLookup = {
				from: DB_MODEL_REF.MEMBERS,
				let: { communityId: "$_id" },
				pipeline: [
					{
						$match: {
							$expr: {
								$and: [
									{
										$eq: [ "$$communityId", "$communityId" ]
									},
									{
										$eq: [ "$status", STATUS.ACTIVE ]
									}
								]
							}
						}
					},
					{
						$project: { _id: 1 }
					}
				],
				as: "memberCount"
			}

			const wishLookup = {
				from: DB_MODEL_REF.TAGWISHES,
				localField: "_id",
				foreignField: "communityId",
				pipeline: [
					{
						$project: { _id: 0, status: 1, wishId: 1 }
					},
					{
						$match: { status: { $ne: STATUS.DELETED } }
					},
					{
						$group: {
							_id: "$wishId"
						}
					}
				],
				as: "wishDetail"
			}
			
			aggPipe.push({ "$lookup": userLookup });
			aggPipe.push({ "$lookup": memberLookup });
			aggPipe.push({ "$lookup": wishLookup });
			aggPipe.push({ "$unwind": "$userDetails" });
			aggPipe.push({ "$project": project });

			const response = await this.aggregate("communities", aggPipe);

			const wishIds = [];
			response[0].wishDetail.map(ids => wishIds.push(ids._id));

			let listenDuration = 0;

			const blessingDetail = await this.aggregate("perform_blessings", [
				{
					$project: { _id: 1, wishId: 1, listenDuration: 1 }
				},
				{
					$match: {
						wishId: {
							$in: wishIds
						},
						status: {
							$ne: STATUS.DELETED
						}
					}
				}
			]);

			blessingDetail.map(duration => listenDuration = listenDuration + duration.listenDuration);

			response[0].blessingTime = listenDuration;
			response[0].blessingCount = blessingDetail.length;
			return response[0];
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function flaggedCommunityReports
	 */
	async flaggedCommunityReports(params: CommunitiesRequest.CommunityListing) {
		try {
			let match: any = {};
			let aggPipe: any = [];
			let sort: any = {};

            match["subjectId"] = toObjectId(params.communityId);
			match["type"] = REPORT_TYPE.COMMUNITY;

            const userLookup = {
				from: DB_MODEL_REF.USER,
				localField: "userId",
				foreignField: "_id",
				as: "userDetails"
			};

			aggPipe.push({ "$match": match });
			aggPipe.push({ "$lookup": userLookup });

            const project = {
				createdAt: 1, description: 1, reportType: 1, userId: 1, "userDetails.firstName": 1, "userDetails.lastName": 1, "userDetails.profilePicture": 1
			};

            if (params.sortBy == 1) {
				params.sortBy = 1;
			} else {
				params.sortBy = -1;
			}

			sort = { "createdAt": params.sortBy }

			aggPipe.push({ "$sort": sort });
			aggPipe.push({ "$project": project });

			const options = { collation: true };
            return this.paginate("reports", aggPipe, params.limit, params.pageNo, options, true);
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function unflagDeleteCommunity
	 */
	async unflagDeleteCommunity(params: CommunitiesRequest.UnflagDeleteCommunity, tokenData: TokenData) {
		try {
			const query: any = {};
			const update: any = {};

            query["_id"] = params.id;
            
            if(params.status == FLAG_TYPE.UN_FLAGGED) {
                update["$set"] = {
                    isFlagged: false,
                    flagStatus: FLAG_TYPE.UN_FLAGGED
                }
                update["$push"] = {
                    unflagHistory: {
                        adminId: tokenData.userId
                    }
                }
            } else {
                update["$set"] = {
                    status: STATUS.DELETED,
                    flagStatus: STATUS.DELETED,
                    deleteReason: params.deleteReason,
                    deletedAt: Date.now()
                }
            }

            return this.updateOne("communities", query, update, {});
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function unflagMembers
	 */
	async unflagMembers(params: CommunitiesRequest.UnflagDeleteCommunity) {
		try {
			const query: any = {};
			const update: any = {};

			query["communityId"] = params.id;
			update["$set"] = {
				isFlagged: false
			}

			return this.updateMany("members", query, update, {});
		} catch (error) {
			throw error;
		}
	}
}

export const communitiesDao = new CommunitiesDao();