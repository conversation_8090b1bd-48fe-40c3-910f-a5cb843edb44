"use strict";
import * as _ from "lodash";
import {
	NOTIFICATION_DATA,
	STATUS,
	SERVER,
	DEVICE_TYPE,
	ADMIN_NOTIFICATION_TYPE
} from "@config/index";
import {
	MESSAGES_NOTIFICATION,
	TITLE_NOTIFICATION,
	TYPE_NOTIFICATION
} from "@config/index";
import { baseDao } from "@modules/baseDao/index";

const admin = require('firebase-admin');
import * as serviceAccount from '../config/wishwell-firebase.json';
// const firebaseConfigAbsolutePath = path.resolve(__dirname, process.env.FIREBASE_CONFIG_PATH);

// const serviceAccount = require(firebaseConfigAbsolutePath);

admin.initializeApp({
	credential: admin.credential.cert(serviceAccount)
})
export class NotificationManager {

	// FCM -> AWS SNS -> FCM

	async subscribeDeviceTokenViaAWS(data) {
		try {
			// AWS.config.update({ region: SERVER.AWS_IAM_USER.REGION });
			// const snsClient = new AWS.SNS();
			const token = data.token;
			const UserId = data.userId
			// const platformApplicationArn = SERVER.SNS_ARN_ENDPOINT;
			// console.log(data,'==================================data')
			let step1 = await baseDao.findOne("login_histories", { 'deviceToken': token, 'isLogin': true }, {});
			// console.log(step1,'==================================step1')
			// let fetchedARN;
			// if(step1) {
			// 	// console.log("=========================INSIDE STEP1========================================")
			// 	// fetchedARN = await this.fetchARNbyToken(platformApplicationArn, step1.deviceToken)
			// 	fetchedARN = await this.fetchAllARNByToken({
			// 		PlatformApplicationArn: platformApplicationArn
			// 	}, step1.deviceToken);
			// }
			// console.log(fetchedARN,'==================================fetch arn')
			// if(step1){
				// console.log(fetchedARN,'==================================fetch arn if')
				// await baseDao.updateMany("login_histories", { 'deviceToken': token, 'isLogin': true }, { }, {});
				// await this.updateDevTokenByArn(fetchedARN, token, UserId);
				// console.log("device token updated successfully")
				// return JSON.stringify({"success":"device token updated successfully"});
			// }
			if(!step1){
				console.log("=============================ELSE==============================")
				// const endpointParams = {
				// 	Token: token,
				// 	PlatformApplicationArn: platformApplicationArn,
				// 	Attributes: {
				// 		UserId: UserId.toString()
				// 	}
				// };
				// const response = await snsClient.createPlatformEndpoint(endpointParams).promise();
				// console.log(response,'==============================================================response')
				// await baseDao.updateOne("login_histories", { 'deviceToken': token,  'isLogin': true }, { arn: response.EndpointArn }, {});
				await baseDao.updateOne("login_histories", { 'userId._id': UserId,  'isLogin': true }, { deviceToken: token }, {});
				// console.log("device token saved successfully")
				return JSON.stringify({"success":"device token saved successfully"})
			}
		} catch (e) {
			console.error(e.message);
			return JSON.stringify({"error====>":e.message})
		}
	}

	// async updateDevTokenByArn(EndpointArn, deviceToken, UserId) {   
	// 	// try {
	// 	//     AWS.config.update({ region: SERVER.AWS_IAM_USER.REGION });
	// 	//     const snsClient = new AWS.SNS();
	// 	// 	const endpointArn = EndpointArn;

	// 	// 	const params = {
	// 	// 		Attributes: {
	// 	// 		  Enabled: 'true',
	// 	// 		  UserId: UserId.toString(),
	// 	// 		  Token: deviceToken
	// 	// 		},
	// 	// 		EndpointArn: endpointArn,
	// 	// 	  };
			  
	// 	// 	  snsClient.setEndpointAttributes(params, function(err, data) {
	// 	// 		if (err) {
	// 	// 		  console.error('Error disabling endpoint:', err);
	// 	// 		} else {
	// 	// 		  console.log('Endpoint disabled:', data);
	// 	// 		}
	// 	// 	  });
	//     // } catch (e) {
	// 	// console.error(e.message);
	// 	// return JSON.stringify({"error====>":e.message})
	//     // }
    // }

	async PublishNotification(userData, wishId, pushParams) {
		try {
			let IdArr = []
			await userData.map(obj=>{IdArr.push(obj.userId)});

			let query: any = {
				isLogin: true,
				'userId._id': { "$in": IdArr  },
			};

			if(pushParams.type == TYPE_NOTIFICATION.WISH_CREATE){
				query = {
					...query,
					'userId.pushNotificationStatus': true,
					'userId.wishesNotificationStatus': true
				}	
			}
			else if(pushParams.type == TYPE_NOTIFICATION.GRATITUDE_RECEIVED_PERSONALISED || pushParams.type == TYPE_NOTIFICATION.GRATITUDE_RECEIVED_GLOBAL ){
				query = {
					...query,
					'userId.pushNotificationStatus': true,
					'userId.gratitudeNotificationStatus': true
				}
			}
			else if(pushParams.type == TYPE_NOTIFICATION.COMMUNITY_INVITE){
				query = {
					...query,
					'userId.pushNotificationStatus': true,
					'userId.communityNotificationStatus': true
				}
			}
			else if(pushParams.type == TYPE_NOTIFICATION.NOTIFICATION_REMINDER) {
				query = {
					...query,
					'userId.pushNotificationStatus': true,
				}
			}
		
			const loginHistories = await baseDao.find("login_histories", query, {});

			if(loginHistories) {
				for(const obj of loginHistories) {
					if(obj.deviceToken){
						const message = {
							notification: {
								title: pushParams.title ? `${pushParams.title}` : "",
								body: pushParams.message ? `${pushParams.message}` : ""
							},
							data: {
								type: pushParams.type ? `${pushParams.type}` : "NO_VALUE",
								subjectId: wishId ? `${wishId}` : "NO_VALUE",
								imessageNumber: pushParams.imessageNumber ? `${pushParams.imessageNumber}` : "NO_VALUE",
								imessageText: pushParams.imessageText ? `${pushParams.imessageText}` : "NO_VALUE"
							},
							tokens: [obj.deviceToken] 
						}
			
						// console.log(`Notification Message == `, message);
			
						admin
							.messaging()
							.sendEachForMulticast(message)
							.then((response: any) => {
								console.log(`Successfully sent notification ${response}`);
								return 'Successfully sent notification';
							})
							.catch((error: any) => {
								console.error(`Error sending notification: ${error}`);
							});
					}
					else{
						continue;
					}
				} 
			} 
			} catch (e) {
				console.error(e.message);
				return JSON.stringify({"error====>":e.message})
			}
	}

	async PublishAdminNotifications(userIds: Array<object>, type: string, subjectId, deleteReason) {
		try {
			const loginHistories = await baseDao.find("login_histories", { 'userId._id': { $in: userIds }, 'isLogin': true }, {});
				
			if(loginHistories.length > 0) {
				for(let data of loginHistories) {				
					if(data.deviceToken) {				
						let body_prefix, body_suffix,  title;
						if(type == ADMIN_NOTIFICATION_TYPE.DELETE_WISH_ADMIN.TYPE) {
							body_prefix = ADMIN_NOTIFICATION_TYPE.DELETE_WISH_ADMIN.BODY_PREFIX;
							body_suffix = ADMIN_NOTIFICATION_TYPE.DELETE_WISH_ADMIN.BODY_SUFFIX;
							title = ADMIN_NOTIFICATION_TYPE.DELETE_WISH_ADMIN.TITLE;
						} else if (type == ADMIN_NOTIFICATION_TYPE.DELETE_BLESSING_ADMIN.TYPE) {
							body_prefix = ADMIN_NOTIFICATION_TYPE.DELETE_BLESSING_ADMIN.BODY_PREFIX;
							body_suffix = ADMIN_NOTIFICATION_TYPE.DELETE_BLESSING_ADMIN.BODY_SUFFIX;
							title = ADMIN_NOTIFICATION_TYPE.DELETE_BLESSING_ADMIN.TITLE;
						} else if (type == ADMIN_NOTIFICATION_TYPE.DELETE_GRATITUDE_ADMIN.TYPE) {
							body_prefix = ADMIN_NOTIFICATION_TYPE.DELETE_GRATITUDE_ADMIN.BODY_PREFIX;
							body_suffix = ADMIN_NOTIFICATION_TYPE.DELETE_GRATITUDE_ADMIN.BODY_SUFFIX;
							title = ADMIN_NOTIFICATION_TYPE.DELETE_GRATITUDE_ADMIN.TITLE;
						} else {
							body_prefix = ADMIN_NOTIFICATION_TYPE.DELETE_COMMUNITY_ADMIN.BODY_PREFIX;
							body_suffix = ADMIN_NOTIFICATION_TYPE.DELETE_COMMUNITY_ADMIN.BODY_SUFFIX;
							title = ADMIN_NOTIFICATION_TYPE.DELETE_COMMUNITY_ADMIN.TITLE;
						}

						const message = {
							notification: {
								title: `${title}`,
								body: `${body_prefix} ${deleteReason} ${body_suffix}`
							},
							data: {
								type: `${type}`,
								subjectId: `${subjectId}`,
							},
							tokens: [data.deviceToken]
						}
			
						console.log(`Notification Message == ${message}`);
			
						admin
							.messaging()
							.sendEachForMulticast(message)
							.then((response: any) => {
								console.log(`Successfully sent notification ${response}`);
								return 'Successfully sent notification';
							})
							.catch((error: any) => {
								console.error(`Error sending notification: ${error}`)
							});
					}
				}
			}
		} catch (error) {
			throw error;
		}
	}

	async PublishContactNotifications(contacts, name) {
		try {
			const userIds = [];
			contacts.map(id => userIds.push(id._id));
			const loginHistoryData = await baseDao.find("login_histories", { 'userId._id': { $in: userIds }, 'isLogin': true }, {});
			if(loginHistoryData.length > 0) {
				for(let data of loginHistoryData) {
					if(data.deviceToken) {
						const messageTitle = TITLE_NOTIFICATION.CONTACT_JOINED_APP.replace('[NAME]', `${name}`);
						const message = {
							notification: {
								title: messageTitle ? `${messageTitle}` : `${TITLE_NOTIFICATION.CONTACT_JOINED_APP}`,
								body: `${MESSAGES_NOTIFICATION.CONTACT_JOINED_APP}`
							},
							data: {
								type: `${TYPE_NOTIFICATION.CONTACT_JOINED_APP}`,
								subjectId: '',
							},
							tokens: [data.deviceToken]
						}
			
						console.log(`Notification Message == ${message}`);
			
						admin
							.messaging()
							.sendEachForMulticast(message)
							.then((response: any) => {
								console.log(`Successfully sent notification ${response}`);
								return 'Successfully sent notification';
							})
							.catch((error: any) => {
								console.error(`Error sending notification: ${error}`)
							});
					}
				}
			}
		} catch (error) {
			throw error;
		}
	}
	
	async PublishNotificationToHost(hostId, wishId) {
		try {
			let loginHistories = await baseDao.find("login_histories", { 'userId._id': hostId, 'isLogin': true }, {});
			for(let obj of loginHistories){
				if(obj.deviceToken) {
					const message = {
						notification: {
							title: `${TITLE_NOTIFICATION.WISH_CREATED}`,
							body: `${MESSAGES_NOTIFICATION.WISH_CREATED}`
						},
						data: {
							type: `${TYPE_NOTIFICATION.WISH_CREATE_HOST}`,
							subjectId: `${wishId}`,
						},
						tokens: [obj.deviceToken]
					}
		
					console.log(`Notification Message == ${message}`);
		
					admin
						.messaging()
						.sendEachForMulticast(message)
						.then((response: any) => {
							console.log(`Successfully sent notification ${response}`);
							return 'Successfully sent notification';
						})
						.catch((error: any) => {
							console.error(`Error sending notification: ${error}`)
						});
				}
			}
			} catch (e) {
			console.error(e.message);
			return JSON.stringify({"error====>":e.message})
		}

}

	// async fetchARNbyToken(applicationArn, deviceToken){
	// 	// const listParams = {
	// 	// 	PlatformApplicationArn: applicationArn
	// 	// };
	// 	// let matchingEndpoint;
	// 	// const snsClient = new AWS.SNS();
	// 	// let data = await snsClient.listEndpointsByPlatformApplication(listParams).promise();
	// 	// //   console.log("datadatadatadata",JSON.stringify(data, undefined, 2))
	// 	// await data.Endpoints.map(obj=>{
	// 	// 	if(obj.Attributes.Token === deviceToken ){ matchingEndpoint = obj.EndpointArn; }
	// 	// });
	// 	// return matchingEndpoint;
	// }

	// async fetchAllARNByToken(params, deviceToken, allEndpoints = []) {
	// 	// let matchingEndpoint;
	// 	// const snsClient = new AWS.SNS();
	// 	// const data = await snsClient.listEndpointsByPlatformApplication(params).promise();
	// 	// allEndpoints = allEndpoints.concat(data.Endpoints);

	// 	// // Check if there are more results to retrieve
	// 	// if(data.NextToken) {
	// 	// 	params.NextToken = data.NextToken;
	// 	// 	return await this.fetchAllARNByToken(params, deviceToken, allEndpoints);
	// 	// } else {
	// 	// 	matchingEndpoint = allEndpoints.find(obj => obj.Attributes.Token === deviceToken)?.EndpointArn;
	// 	// 	return matchingEndpoint;
	// 	// }
	// }

	//CommonublishNotification notifiction function update for userIds
	async CommonublishNotification(userIds, wishId, pushParams) {
		try {		
			if(userIds && userIds.length > 0){
				let query: any = {
					"userId._id": { "$in": userIds  },
					isLogin: true
				};

				if(pushParams.type==TYPE_NOTIFICATION.WISH_CREATE_HOST || pushParams.type==TYPE_NOTIFICATION.WISH_REQUEST_ACCEPTED || pushParams.type==TYPE_NOTIFICATION.WISH_REQUEST_DECLINED){
					query = {
						...query,
						'userId.pushNotificationStatus': true,
						'userId.wishesNotificationStatus': true
					};
				}
				else if(pushParams.type==TYPE_NOTIFICATION.BLESSING_RECEIVED){
					query = {
						...query,
						'userId.pushNotificationStatus': true
					};
				}

				const loginHistories = await baseDao.find("login_histories", query, {});

				for(let obj of loginHistories){
					if(obj.deviceToken) {
							const message = {
								notification: {
									title: `${pushParams.title}`,
									body: `${pushParams.message}`
								},
								data: {
									type: pushParams.type ? `${pushParams.type}` : "NO_VALUE",
									subjectId: wishId ? `${wishId}` : "NO_VALUE",
								},
								tokens: [obj.deviceToken] 
							}
				
							console.log(`Notification Message == ${message}`);
				
							admin
								.messaging()
								.sendEachForMulticast(message)
								.then((response: any) => {
									console.log(`Successfully sent notification ${response}`);
									return 'Successfully sent notification';
								})
								.catch((error: any) => {
									console.error(`Error sending notification: ${error}`);
								});
					}
				}
		    }
		} catch (e) {
			console.error(e.message);
			return JSON.stringify({"error====>":e.message})
		}

}


}

export const notificationManager = new NotificationManager();	