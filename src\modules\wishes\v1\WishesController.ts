"use strict";
import { MESSAGES, STATUS, NOTIFICATION_TYPE, WISH_TAG_TYPE, WISH_TYPE, ACTION_TYPE, GLOBAL_WISH_PIN_COUNT,REPORT_TYPE, JOB_SCHEDULER_TYPE, SMS_CONTENT, MESSAGES_NOTIFICATION, TITLE_NOTIFICATION, TYPE_NOTIFICATION, SQS_TYPES,GEO_LOCATION_TYPE, COMPASSION_MAP } from "@config/constant";
import { SERVER, DEEPLINK_TYPE } from "@config/index";
import { wishesDaoV1 } from "@modules/wishes/index";
import { userDaoV1 } from "@modules/user/index"
import { toObjectId } from "@utils/appUtils";
import * as mongoose from "mongoose";
import { baseDao } from "@modules/baseDao/index";
import { reportDaoV1 } from "@modules/reports/index";
import { redisClient } from "@lib/redis/RedisClient";
import { notificationManager }  from "../../../utils/NotificationManager"
import { awsSQS } from "@lib/AwsSqs";
import { blessingDaoV1 } from "@modules/blessings/index";
import { gratitudesDaoV1 } from "@modules/gratitude/index";
import moment = require("moment");
import { commonControllerV1 } from "@modules/common/index";
import { commonDao } from "@modules/common/v1/CommonDao";
const fetch = require('node-fetch');
const buffer = require('buffer');

export class WishesController {
	/**
	 * @function addWishes
	 * @description here are adding the wishes.
	 */
	async addWishes(params: WishesRequest.Add, tokenData: TokenData) {
		const session = await mongoose.startSession();
		session.startTransaction();
		try {
			const step1 = await userDaoV1.findUserById(tokenData.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
		    if(!step1.isProfileComplete) return Promise.reject(MESSAGES.ERROR.REGISTRATION_PENDING);
			// console.log(params.tagContacts)
			// console.log(params.communityId)
			if(params.tagContacts && params.tagContacts.length == 0 && params.communityId && params.communityId.length == 0) return Promise.reject(MESSAGES.ERROR.ADD_WISH_ATLEAST_ONE_CONTACT_OR_COMMUNITY);
			if(!params.tagContacts && params.communityId && params.communityId.length == 0) return Promise.reject(MESSAGES.ERROR.ADD_WISH_ATLEAST_ONE_COMMUNITY);
			if(!params.communityId && params.tagContacts && params.tagContacts.length == 0) return Promise.reject(MESSAGES.ERROR.ADD_WISH_ATLEAST_ONE_CONTACT);
			// if (params.location) {
			// //	await baseDao.updateOne("users", { '_id': tokenData.userId }, { 'location': params.location }, {});
			// }
			step1.name = step1.name?step1.name:"wishwell";
			if (params.wishType == WISH_TYPE.OTHER) {
				params.status = STATUS.PENDING;
			}
			params.userId = tokenData.userId
			//wish-created
			let obj = {};
			obj['firstName'] = step1.firstName
			obj['lastName'] = step1.lastName
			obj['profilePicture'] = step1.profilePicture
			params.userDetail = obj;
			let registeredUser = [], nonRegisteredUser = [];
			// if (!params.location) {
			// 	let userLocationData = await this.getLocation(tokenData.userId);
			// 	params["location"] = userLocationData;
			// }
			let userLocationData = await this.getLocation(tokenData.userId,params);
				params["location"] = userLocationData;
				// console.log(params["location"],'===============================================params["location"]')

			let step2 = await wishesDaoV1.addWishes(params);
			// console.log(step2,'=============================================add wishes')
			//add location in location table for most compassionate
			if(params.location){
				await commonControllerV1.saveLocation(params,GEO_LOCATION_TYPE.WISH,step2._id,tokenData.userId)
			}
			// members of communities added in tagged-users for wish
			let communityUsers,duplicateData=[];
			if(params.communityId){
				communityUsers = await baseDao.find("members", {'communityId': {'$in':params.communityId}, status:{'$nin':[STATUS.DELETED,STATUS.REJECTED]}, userId:{$exists: true } }, {});
				// console.log(communityUsers,'=============================================communityUsers')
				// console.log(communityUsers.length,'=============================================communityUsers length')
				if(communityUsers){
					const uniqueArray = [];
					let i =0;
					communityUsers = await this.removeDuplicateV2(communityUsers);
					// console.log(communityUsers,'==============================================communityUsers 2')
					// console.log(communityUsers.length,'==============================================communityUsers 2 length')
					// return
					if(tokenData.userId){
						
						let foundObject = communityUsers.find(obj => obj.userId.toString() === tokenData.userId.toString());
						// console.log(foundObject,'==========================================foundObject')
						let index = communityUsers.findIndex(obj => obj.userId.toString() === tokenData.userId.toString());
						// console.log(index,'========================================index')
						if (foundObject) {
							let removedValue = communityUsers.splice(index, 1); // Removes 1 element at the specified index - user from community which is host 
						}
						// console.log(communityUsers,'==============================================communityUsers 3')
						// console.log(communityUsers.length,'==============================================communityUsers 3 length')
					}
					if(params.hostId){
						let foundObject = communityUsers.find(obj => obj.userId.toString() === params.hostId.toString());
						let index = communityUsers.findIndex(obj => obj.userId.toString() === params.hostId.toString());
						if (foundObject) {
							let removedValue = communityUsers.splice(index, 1); // Removes 1 element at the specified index - user from community which is host 
						}
					}
					if(params.tagContacts){
                        for (const obj1 of params.tagContacts) {
                           for (const obj2 of communityUsers) {
							if(obj1['isAppUser']){
                               if (obj1['userId'].toString() === obj2['userId'].toString()) {
								obj1['isDuplicate'] = false;  
								obj1['isDuplicateExist']=true;
								obj2['isDuplicate'] = true;
								obj2['isDuplicateExist']=true;                              
							    }
                           }
						  }
                       }
					}
					if(params.cohosts){
                        for (const obj1 of params.cohosts) {
                           for (const obj2 of communityUsers) {
                               if (obj1.toString() === obj2['userId'].toString()) {
								//obj1['isDuplicate'] = true;
								obj2['isDuplicate'] = true;
								obj2['isDuplicateExist']=true;
								duplicateData.push(obj1);
                               }
                           }
                       }
					}
				
			    }

			}
			// return
			if(!params.tagContacts){
				params.tagContacts = []
			}
			if(communityUsers && communityUsers.length !== 0){
				for (let i = 0; communityUsers.length > i; i++) {
					let object={};
					if(communityUsers[i].type == WISH_TAG_TYPE.CONTACTS || communityUsers[i].type == WISH_TAG_TYPE.CREATOR){
						object['userId']=communityUsers[i].userId.toString();
						object['communityId']=communityUsers[i].communityId;
						//object['contactId']=communityUsers[i].contactId;
						object['isAppUser']=true;
						object['status']=communityUsers[i].status;
						object['created']=Date.now;
						object['createdAt']=new Date(Date.now());
						object['communityIdList'] = communityUsers[i].communityIdList;
						if(communityUsers[i].isDuplicateExist){
							object['isDuplicate']=communityUsers[i].isDuplicate;
							object['isDuplicateExist']=communityUsers[i].isDuplicateExist;
						}else{
							object['isDuplicate']=false;
							object['isDuplicateExist']=false;
						}
						params.tagContacts.push(object);
					}else{
						object['phoneNumber']=communityUsers[i].phoneNumber;
						object['countryCode']=communityUsers[i].countryCode;
						object['communityId']=communityUsers[i].communityId;
						if(communityUsers[i].contactId)object['contactId']=communityUsers[i].contactId;
						object['isAppUser']=false;
						object['status']=communityUsers[i].status;
						object['created']=Date.now;
						object['createdAt']=new Date(Date.now());
						object['communityIdList'] = communityUsers[i].communityIdList;
						params.tagContacts.push(object);
					}
				}
				// console.log(params.tagContacts,'=============================================params.tagContacts')
				// console.log(params.tagContacts.length,'======================================params.tagContacts.length')
			}
			//for contact section logic
			if(params.tagContacts){
				for (let i = 0; params.tagContacts.length > i; i++) {
					if (params.tagContacts[i]['isAppUser']) {
						registeredUser.push(params.tagContacts[i])
					} else {
						nonRegisteredUser.push(params.tagContacts[i])
					}
				}

			}
			const deeplink = `${SERVER.APP_URL}${SERVER.API_BASE_URL}/v1/common/deeplink?type=${DEEPLINK_TYPE.SMS_INVITE}`;  // deeplinking
			let contactArray = [];
			let pushArray = [];
			let hostIdArr = [];
			obj = {};
			//host-added to tagged-wishes model
			if (params.hostDetail) {
				if (params.hostDetail['userId']) {
					obj['userId'] = toObjectId(params.hostDetail['userId'])
					obj['wishId'] = step2._id
					obj['type'] = WISH_TAG_TYPE.HOST;
					obj['status'] = STATUS.ACTIVE;
					obj['wishType'] = params.wishType
					obj['created']=Date.now;
					obj['createdAt']=new Date(Date.now());
				} else {
					obj['countryCode'] = params.hostDetail['countryCode'];
					obj['phoneNumber'] = params.hostDetail['phoneNumber'];
					obj['wishId'] = step2._id
					obj['type'] = WISH_TAG_TYPE.INVITED_HOST;
					obj['status'] = STATUS.ACTIVE;
					obj['wishType'] = params.wishType
					obj['created']=Date.now;
					obj['createdAt']=new Date(Date.now());
					let userName;
					params.hostDetail["name"] ? userName = params.hostDetail["name"] : userName = "user";
					await awsSQS.signupMagicLinkProducer({"countryCode":params.hostDetail['countryCode'], "phoneNumber":params.hostDetail['phoneNumber'], "SMS_CONTENT":SMS_CONTENT.DEAR_USER+params.hostDetail["name"]+SMS_CONTENT.HOST_WISH_REQUEST+ SMS_CONTENT.WITH_GRAT+ step1.name +`\n`+deeplink,"type":SQS_TYPES.WISH_CREATE_SMS});
					// 5th msg
					const payload1 = {
						jobName:JOB_SCHEDULER_TYPE.WISHES_REQUEST_HOST,
						time: new Date(Date.now()).getTime() + 24 * 60 * 60 * 1000,
						//time: new Date(Date.now()).getTime() +    2 * 60 * 1000,
						//time: new Date(Date.now()).getTime() + 5 * 60 * 1000,
						// time: new Date(Date.now()).getTime() + 5 * 60 * 1000,
						//time: new Date(Date.now()).getTime() +  (20 * 1000),
						data: { 
							"wishId": step2._id.toString(),
							"countryCode":params.hostDetail['countryCode'],
							"phoneNumber":params.hostDetail['phoneNumber'],
							"deeplink":"deeplink",
							"userName":userName,
							"sender":step1.name,
							"number":"1"

						}
						};
						redisClient.createJobs(payload1);
						const payload2 = {
							jobName:JOB_SCHEDULER_TYPE.WISHES_REQUEST_HOST,
							time: new Date(Date.now()).getTime() + 72 * 60 * 60 * 1000,
							//time: new Date(Date.now()).getTime() + 3 * 60 * 1000,
							// time: new Date(Date.now()).getTime() + 10 * 60 * 1000,
							//time: new Date(Date.now()).getTime() +  (20 * 1000),
							data: { 
								"wishId": step2._id.toString(),
								"countryCode":params.hostDetail['countryCode'],
								"phoneNumber":params.hostDetail['phoneNumber'],
								"deeplink":"deeplink",
								"userName":userName,
								"sender":step1.name,
								"number":"2"
								

							}
							};
							redisClient.createJobs(payload2);	
						const payload3 = {
							jobName:JOB_SCHEDULER_TYPE.WISHES_REQUEST_HOST,
							time: new Date(Date.now()).getTime() + 5 * 24 * 60 * 60 * 1000,
							//time: new Date(Date.now()).getTime() +  4 * 60 * 1000,
							// time: new Date(Date.now()).getTime() + 15 * 60 * 1000,
							//time: new Date(Date.now()).getTime() + (60 * 1000),
							data: { 
								"wishId": step2._id.toString(),
								"countryCode":params.hostDetail['countryCode'],
								"phoneNumber":params.hostDetail['phoneNumber'],
								"deeplink":"deeplink",
								"userName":userName,
								"sender":step1.name,
								"number":"3"

							}
							};
							redisClient.createJobs(payload3);	

				}
				contactArray.push(obj);
				// pushArray.push(obj);
				hostIdArr.push(obj)
			}
			//registered and non-registered users logic
			if (registeredUser && registeredUser.length !== 0) {
				for (let i = 0; registeredUser.length > i; i++) {
					let obj = {};
					obj['userId'] = toObjectId(registeredUser[i]['userId']);
					obj['wishId'] = step2._id;
					obj['type'] = WISH_TAG_TYPE.CONTACTS;
					if(registeredUser[i].status){
					    obj['status'] = registeredUser[i].status;
					}else{
					    obj['status'] = STATUS.ACTIVE;
					}
					obj['wishType'] = params.wishType;
					obj['created']=Date.now;
					obj['createdAt']=new Date(Date.now());
					if(registeredUser[i].communityIdList) obj['communityIdList'] = registeredUser[i].communityIdList;
					if(registeredUser[i].isDuplicateExist){
						obj['isDuplicate']=registeredUser[i].isDuplicate;
						obj['isDuplicateExist']=registeredUser[i].isDuplicateExist;
					}else{
						obj['isDuplicate']=false;
						obj['isDuplicateExist']=false;
					}
					if(registeredUser[i]['communityId']) obj['communityId'] = toObjectId(registeredUser[i]['communityId']);
					contactArray.push(obj);
					if(registeredUser[i].communityId) {
						if(registeredUser[i].status && registeredUser[i].status !== STATUS.PENDING) {
							if(params.wishType == WISH_TYPE.MYSELF) {
								pushArray.push(obj);
							}
						}
					} else {
						if(params.wishType == WISH_TYPE.MYSELF) {
							pushArray.push(obj);
						}
					}

				}
			}
			if (nonRegisteredUser && nonRegisteredUser.length !== 0) {
				
				for (let i = 0; nonRegisteredUser.length > i; i++) {
					let obj = {};
					obj['wishId'] = step2._id;
					obj['type'] = WISH_TAG_TYPE.INVITED;
					obj['countryCode'] = nonRegisteredUser[i].countryCode;
					obj['phoneNumber'] = nonRegisteredUser[i].phoneNumber;
					if(nonRegisteredUser[i].status){
					    obj['status'] = registeredUser[i].status;
					}else{
					    obj['status'] = STATUS.ACTIVE;
					}
					obj['wishType'] = params.wishType;
					obj['created']=Date.now;
					obj['createdAt']=new Date(Date.now());

					if(nonRegisteredUser[i].isDuplicateExist){
						obj['isDuplicate']=nonRegisteredUser[i].isDuplicate;
						obj['isDuplicateExist']=nonRegisteredUser[i].isDuplicateExist;
					}else{
						obj['isDuplicate']=false;
						obj['isDuplicateExist']=false;
					}
					//obj['contactId'] =  toObjectId(nonRegisteredUser[i].contactId);
					if(nonRegisteredUser[i].communityId)obj['communityId']=nonRegisteredUser[i].communityId;
					if(nonRegisteredUser[i].contactId)obj['contactId'] =  toObjectId(nonRegisteredUser[i].contactId);					
					contactArray.push(obj);
				//	const deeplink = `${SERVER.APP_URL}${SERVER.API_BASE_URL}/v1/common/deeplink?type=${DEEPLINK_TYPE.SMS_INVITE}`;  // deeplinking
					let host;
					if(step2.wishType == WISH_TYPE.MYSELF){
						
						// contact name
						let userName
						nonRegisteredUser[i].name ? userName = nonRegisteredUser[i].name : userName = "user";
						await awsSQS.signupMagicLinkProducer({"countryCode":nonRegisteredUser[i].countryCode, "phoneNumber":nonRegisteredUser[i].phoneNumber, "SMS_CONTENT":SMS_CONTENT.DEAR_USER+userName+`.\n`+SMS_CONTENT.TAG_CONTACT_ON_WISH_FOR_SELF1+`\n`+SMS_CONTENT.WITH_GRAT+step1.name+`\n`+deeplink,"type":SQS_TYPES.WISH_CREATE_SMS});
						const payload1 = {
							jobName:JOB_SCHEDULER_TYPE.WISHES_REQUEST_MESSAGE,
							time: new Date(Date.now()).getTime() + 24 * 60 * 60 * 1000,
							//time: new Date(Date.now()).getTime() +   1 * 60 * 1000,
							// time: new Date(Date.now()).getTime() +   5 * 60 * 1000,
							data: { 
								"wishId": step2._id.toString(),
								"countryCode":nonRegisteredUser[i].countryCode,
								"phoneNumber":nonRegisteredUser[i].phoneNumber,
								"deeplink":"deeplink",
								"wishType":step2.wishType,
								"number": "4",
								"userName":userName,
								"sender":step1.name
							}
							};
							redisClient.createJobs(payload1);	
							const payload2 = {
								jobName:JOB_SCHEDULER_TYPE.WISHES_REQUEST_MESSAGE,
								time: new Date(Date.now()).getTime() + 72 * 60 * 60 * 1000,
								//time: new Date(Date.now()).getTime() + 2 * 60 * 60 * 1000,
								// time: new Date(Date.now()).getTime() + 10 * 60 * 1000,
								data: { 
									"wishId": step2._id.toString(),
									"countryCode":nonRegisteredUser[i].countryCode,
									"phoneNumber":nonRegisteredUser[i].phoneNumber,
									"deeplink":"deeplink",
									"wishType":step2.wishType,
									"number": "5",
									"userName":userName,
									"sender":step1.name
								}
								};
								redisClient.createJobs(payload2);	
							const payload3 = {
								jobName:JOB_SCHEDULER_TYPE.WISHES_REQUEST_MESSAGE,
								time: new Date(Date.now()).getTime() + 5 * 24 * 60 * 60 * 1000,
								//time: new Date(Date.now()).getTime() +  3 * 60 * 60 * 1000,
								// time: new Date(Date.now()).getTime() + 15 * 60 * 1000,
								data: { 
									"wishId": step2._id.toString(),
									"countryCode":nonRegisteredUser[i].countryCode,
									"phoneNumber":nonRegisteredUser[i].phoneNumber,
									"deeplink":"deeplink",
									"wishType":step2.wishType,
									"number": "6",
									"userName":userName,
									"sender":step1.name
								}
								};
								redisClient.createJobs(payload3);	
					}else{
						host = await baseDao.findOne("users", { _id: params.hostId, status: { '$ne': STATUS.DELETED } });
						
						if(host){
							// await smsManager.sendMessageViaAWS(nonRegisteredUser[i].countryCode, nonRegisteredUser[i].phoneNumber, SMS_CONTENT.TAG_CONTACT_ON_WISH_FOR_OTHERS1+host.name+SMS_CONTENT.TAG_CONTACT_ON_WISH_FOR_OTHERS2+deeplink)
							let userName,hostName;
							nonRegisteredUser[i].name ? userName = nonRegisteredUser[i].name : userName = "user";
							params.hostDetail["name"] ? hostName = params.hostDetail["name"] : hostName = "Host"
							await awsSQS.signupMagicLinkProducer({"countryCode":nonRegisteredUser[i].countryCode, "phoneNumber":nonRegisteredUser[i].phoneNumber, "SMS_CONTENT":SMS_CONTENT.DEAR_USER+ userName+`,\n`+SMS_CONTENT.TAG_CONTACT_ON_WISH_FOR_OTHERS1+hostName+SMS_CONTENT.TAG_CONTACT_ON_WISH_FOR_OTHERS2+hostName + `\n`+SMS_CONTENT.WITH_GRAT+ step1.name +`\n`+deeplink, "type":SQS_TYPES.WISH_CREATE_SMS});
							const payload1 = {
								jobName:JOB_SCHEDULER_TYPE.WISHES_REQUEST_MESSAGE,
								time: new Date(Date.now()).getTime() + 24 * 60 * 60 * 1000,
								//time: new Date(Date.now()).getTime() + 1 * 60 * 1000,
								// time: new Date(Date.now()).getTime() + 5 * 60 * 1000,
								data: { 
									"wishId": step2._id.toString(),
									"countryCode":nonRegisteredUser[i].countryCode,
									"phoneNumber":nonRegisteredUser[i].phoneNumber,
									"deeplink":"deeplink",
									"wishType":step2.wishType,
									"hostName":hostName,
									"userName":userName,
									"sender":step1.name,
									"number": "7"}
								};
								redisClient.createJobs(payload1);	
							const payload2 = {
								jobName:JOB_SCHEDULER_TYPE.WISHES_REQUEST_MESSAGE,
								time: new Date(Date.now()).getTime() + 72 * 60 * 60 * 1000,
								//time: new Date(Date.now()).getTime() + 2 * 60 * 1000,
								// time: new Date(Date.now()).getTime() + 10 * 60 * 1000,
								data: { 
									"wishId": step2._id.toString(),
									"countryCode":nonRegisteredUser[i].countryCode,
									"phoneNumber":nonRegisteredUser[i].phoneNumber,
									"deeplink":"deeplink",
									"wishType":step2.wishType,
									"hostName":hostName,
									"userName":userName,
									"sender":step1.name,
									"number": "8"}
								};
								redisClient.createJobs(payload2);	
							const payload3 = {
								jobName:JOB_SCHEDULER_TYPE.WISHES_REQUEST_MESSAGE,
								time: new Date(Date.now()).getTime() + 5 * 24 * 60 * 60 * 1000,
								//time: new Date(Date.now()).getTime() + 3 * 60 * 1000,
								// time: new Date(Date.now()).getTime() + 15 * 60 * 1000,
								data: { 
									"wishId": step2._id.toString(),
									"countryCode":nonRegisteredUser[i].countryCode,
									"phoneNumber":nonRegisteredUser[i].phoneNumber,
									"deeplink":"deeplink",
									"wishType":step2.wishType,
									"hostName":hostName,
									"userName":userName,
									"sender":step1.name,
									"number": "9"}
								};
								redisClient.createJobs(payload3);	
						}else{
							// host = userDaoV1.findOne("contacts",params.hostDetail["contactId"]);
							// await smsManager.sendMessageViaAWS(nonRegisteredUser[i].countryCode, nonRegisteredUser[i].phoneNumber, SMS_CONTENT.TAG_CONTACT_ON_WISH_FOR_OTHERS1+step1.name+SMS_CONTENT.TAG_CONTACT_ON_WISH_FOR_OTHERS2+deeplink)
							let userName, hostName;
							nonRegisteredUser[i].name ? userName = nonRegisteredUser[i].name : userName = "user";
							params.hostDetail["name"] ? hostName = params.hostDetail["name"] : hostName = "Host"
							await awsSQS.signupMagicLinkProducer({"countryCode":nonRegisteredUser[i].countryCode, "phoneNumber":nonRegisteredUser[i].phoneNumber, "SMS_CONTENT":SMS_CONTENT.DEAR_USER+ userName +`,\n`+SMS_CONTENT.TAG_CONTACT_ON_WISH_FOR_OTHERS1+hostName+SMS_CONTENT.TAG_CONTACT_ON_WISH_FOR_OTHERS2+ hostName + `\n`+SMS_CONTENT.WITH_GRAT+ step1.name +`\n`+deeplink, "type":SQS_TYPES.WISH_CREATE_SMS})
							const payload1 = {
								jobName:JOB_SCHEDULER_TYPE.WISHES_REQUEST_MESSAGE,
								time: new Date(Date.now()).getTime() + 24 * 60 * 60 * 1000,
								//time: new Date(Date.now()).getTime() + 1 * 60 * 1000,
								// time: new Date(Date.now()).getTime() + 5 * 60 * 1000,
								data: { 
									"wishId": step2._id.toString(),
									"countryCode":nonRegisteredUser[i].countryCode,
									"phoneNumber":nonRegisteredUser[i].phoneNumber,
									"deeplink":"deeplink",
									"wishType":step2.wishType,
									"hostName":hostName,
									"userName":userName,
									"sender":step1.name,
									"number": "10"
								     }
								};
								redisClient.createJobs(payload1);	
							const payload2 = {
								jobName:JOB_SCHEDULER_TYPE.WISHES_REQUEST_MESSAGE,
								time: new Date(Date.now()).getTime() + 72 * 60 * 60 * 1000,
							   // time: new Date(Date.now()).getTime() + 2 * 60 * 1000,
								// time: new Date(Date.now()).getTime() + 10 * 60 * 1000,
								data: { 
									"wishId": step2._id.toString(),
									"countryCode":nonRegisteredUser[i].countryCode,
									"phoneNumber":nonRegisteredUser[i].phoneNumber,
									"deeplink":"deeplink",
									"wishType":step2.wishType,
									"hostName":hostName,
									"userName":userName,
									"sender":step1.name,
									"number": "11"}
								};
								redisClient.createJobs(payload2);	
							const payload3 = {
								jobName:JOB_SCHEDULER_TYPE.WISHES_REQUEST_MESSAGE,
								time: new Date(Date.now()).getTime() + 5 * 24 * 60 * 60 * 1000,
								//time: new Date(Date.now()).getTime() + 3 * 60 * 1000,
								// time: new Date(Date.now()).getTime() + 15 * 60 * 1000,
								data: { 
									"wishId": step2._id.toString(),
									"countryCode":nonRegisteredUser[i].countryCode,
									"phoneNumber":nonRegisteredUser[i].phoneNumber,
									"deeplink":"deeplink",
									"wishType":step2.wishType,
									"hostName":hostName,
									"userName":userName,
									"sender":step1.name,
									"number": "12"}
								};
								redisClient.createJobs(payload3);	
						}
					}
				}
			}
			if (params.cohosts && params.cohosts.length !== 0) {
				let cohostsDetails = await wishesDaoV1.getCohosts(params.cohosts);
				if(duplicateData && duplicateData.length !== 0){
					for (const obj1 of cohostsDetails) {
						for (const obj2 of duplicateData) {
							if (obj1['_id'].toString() === obj2.toString()) {
							 obj1['isDuplicate'] = false;  // original data            
							 obj1['isDuplicateExist'] = true; // duplicate of this data exist  

							}
						}
					}
				}
				for (let i = 0; cohostsDetails.length > i; i++) {
					let obj = {};
					obj['userId'] = toObjectId(cohostsDetails[i]._id);
					obj['wishId'] = step2._id
					obj['type'] = WISH_TAG_TYPE.COHOST;
					obj['status'] = STATUS.ACTIVE;
					obj['wishType'] = params.wishType;
					obj['created']=Date.now;
					obj['createdAt']=new Date(Date.now());
					if(cohostsDetails[i].isDuplicateExist){
						obj['isDuplicate']=cohostsDetails[i].isDuplicate;
						obj['isDuplicateExist']=cohostsDetails[i].isDuplicateExist;
					}else{
						obj['isDuplicate']=false;
						obj['isDuplicateExist']=false;
					}
					contactArray.push(obj);
					pushArray.push(obj);
				}
			}
			// console.log(contactArray)
			await wishesDaoV1.addTagWishes(contactArray);
			await this.wellLogs(step2,tokenData,contactArray,params,false);
			await session.commitTransaction();
			session.endSession();
			// let pushParamsContact = {"message":MESSAGES_NOTIFICATION.WISH_CREATED, "title":TITLE_NOTIFICATION.WISH_CREATED, "type":TYPE_NOTIFICATION.WISH_CREATE}
			// await notificationManager.PublishNotification(pushArray, step2._id, pushParamsContact);

			if(params.hostId){
			    let pushParamsHost = {"message":MESSAGES_NOTIFICATION.WISH_CREATED, "title":TITLE_NOTIFICATION.WISH_HOST_CREATED, "type":TYPE_NOTIFICATION.WISH_CREATE_HOST}
			    await notificationManager.CommonublishNotification([params.hostId], step2._id, pushParamsHost);
			}
			params.intension = params.intension.toLowerCase(); // to lower case intension text in notification message
			if(step2.wishType == WISH_TYPE.MYSELF){
				// console.log(MESSAGES_NOTIFICATION.WISH_CREATED1+'me'+MESSAGES_NOTIFICATION.WISH_CREATED2+params.intension+MESSAGES_NOTIFICATION.WISH_CREATED3,"kkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkk");
				let pushParamsContact = {"message":MESSAGES_NOTIFICATION.WISH_CREATED1+step1.name+MESSAGES_NOTIFICATION.WISH_CREATED2+params.intension+MESSAGES_NOTIFICATION.WISH_CREATED3, "title":TITLE_NOTIFICATION.WISH_CREATED, "type":TYPE_NOTIFICATION.WISH_CREATE}
				await notificationManager.PublishNotification(pushArray, step2._id, pushParamsContact);
			}else if(params.hostId){

				let host = await baseDao.findOne("users", { _id: params.hostId, status: { '$ne': STATUS.DELETED } });
			    let pushParamsContact = {"message":MESSAGES_NOTIFICATION.WISH_CREATED1+host.name+MESSAGES_NOTIFICATION.WISH_CREATED2+params.intension+MESSAGES_NOTIFICATION.WISH_CREATED3, "title":TITLE_NOTIFICATION.WISH_CREATED, "type":TYPE_NOTIFICATION.WISH_CREATE}
				if(pushArray.length) {
					await notificationManager.PublishNotification(pushArray, step2._id, pushParamsContact);
				}
				// console.log(MESSAGES_NOTIFICATION.WISH_CREATED1+host.name+MESSAGES_NOTIFICATION.WISH_CREATED2+params.intension+MESSAGES_NOTIFICATION.WISH_CREATED3,"kkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkk");

			}else{
				let pushParamsContact = {"message":MESSAGES_NOTIFICATION.WISH_CREATED1+step1.name+MESSAGES_NOTIFICATION.WISH_CREATED2+params.intension+MESSAGES_NOTIFICATION.WISH_CREATED3, "title":TITLE_NOTIFICATION.WISH_CREATED, "type":TYPE_NOTIFICATION.WISH_CREATE}
				if(pushArray.length) {
					await notificationManager.PublishNotification(pushArray, step2._id, pushParamsContact);
				}
				// console.log(MESSAGES_NOTIFICATION.WISH_CREATED1+step1.name+MESSAGES_NOTIFICATION.WISH_CREATED2+params.intension+MESSAGES_NOTIFICATION.WISH_CREATED3,"kkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkk");


			}
			return MESSAGES.SUCCESS.WISH_CREATED;
		} catch (error) {
			await session.abortTransaction();
			session.endSession();
			throw error;
		}
	}
	/**
	 * @function getWishes
	 * @description here are getting the requested-wishes.
	 *
	 */
	async getWishes(params: WishesRequest.WishList, tokenData: TokenData) {
		try {
			let step1 = await userDaoV1.findUserById(tokenData.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			let step2 = await reportDaoV1.reportedWishes(REPORT_TYPE.WISH,params.userId)
			let step3 = await wishesDaoV1.getWishes(params,step2);
			await step3.data.map(obj=>{
				let encodedWishId = Buffer.from((obj.wishDetail._id).toString()).toString('base64');
				let encodedUserId = Buffer.from((tokenData.userId).toString()).toString('base64');
				obj["wishDetail"]["shareLink"] =  `${SERVER.APP_URL}${SERVER.API_BASE_URL}/v1/common/deeplink?type=${DEEPLINK_TYPE.WISH_SHARE}&wid=${encodedWishId}&uid=${encodedUserId}`
			})
			return MESSAGES.SUCCESS.WISH_LIST(step3);
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function receivedWishList
	 * @description here are getting the received-wishes.
	 */
	async receivedWishList(params: WishesRequest.WishList, tokenData: TokenData) {
		try {
			let step1 = await userDaoV1.findUserById(params.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			let step2 = await reportDaoV1.reportedWishes(REPORT_TYPE.WISH,params.userId)
			let step3 = await wishesDaoV1.getReceivedWishes(params,step2);
			await step3.data.map(obj=>{
				let encodedWishId = Buffer.from((obj.wishDetail._id).toString()).toString('base64');
				// let encodedHostId = Buffer.from((obj.wishDetail.hostId).toString()).toString('base64');
				let encodedUserId = Buffer.from((tokenData.userId).toString()).toString('base64');
				obj["wishDetail"]["shareLink"] =  `${SERVER.APP_URL}${SERVER.API_BASE_URL}/v1/common/deeplink?type=${DEEPLINK_TYPE.WISH_SHARE}&wid=${encodedWishId}&uid=${encodedUserId}`
			})
			return MESSAGES.SUCCESS.WISH_LIST(step3);
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function wishDetail
	 * @description here are getting the wish-detail.
	 */
	async wishDetail(params: WishesRequest.WishDetail, tokenData: TokenData) {
		try {
			let step1 = await userDaoV1.findUserById(tokenData.userId);		
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			const step2 = await wishesDaoV1.findWishById(params.wishId);
            if (!step2) return Promise.reject(MESSAGES.ERROR.WISH_NOT_FOUND);
			let step3 = await wishesDaoV1.wishDetail(params, tokenData.userId,step2);	
			if(step3){
			let step4 = await wishesDaoV1.findTaggedUser(params);
			step3[0].taggedUsers = step4;
			//last bless time
			  if(step3[0].isGlobalWish==true){
				let blessTime = await baseDao.findOne("perform_blessings",{wishId:params.wishId,userId:tokenData.userId},{},{_id:1,createdAt:1},{ createdAt: -1 }); 
				if(blessTime){
				step3[0].lastBlessTime = blessTime.createdAt;
				
			  }
			  }
			}
			// let encodedId = Buffer.from((step3[0]._id).toString()).toString('base64');
			// step3[0]["shareLink"] =  `${SERVER.APP_URL}${SERVER.API_BASE_URL}/v1/common/deeplink?type=${DEEPLINK_TYPE.WISH_SHARE}&id=${encodedId}`

			let encodedWishId  = Buffer.from((step3[0]._id).toString()).toString('base64');
			let encodedUserId = Buffer.from((tokenData.userId).toString()).toString('base64');

			step3[0]["shareLink"] =  `${SERVER.APP_URL}${SERVER.API_BASE_URL}/v1/common/deeplink?type=${DEEPLINK_TYPE.WISH_SHARE}&wid=${encodedWishId}&uid=${encodedUserId}`
			
			//get all list of user who performed bless on this wish
			let blessuserIdsList = await baseDao.distinct("perform_blessings","userId",{wishId:params.wishId,isGratitude:false,status : "ACTIVE"});
			step3[0]['blessuserIdsList'] = blessuserIdsList;
			// let step4 = await wishesDaoV1.blessingDetail(params)
			// step2[0].blessingData = step4
			return MESSAGES.SUCCESS.WISH_DETAIL(step3[0]);
		} catch (error) {
			throw error;
		}
	}
	/**   
	 * @function pinnedWishes
	 * @description here are pinning the wishes.
	 */
	async pinnedWishes(params: WishesRequest.PinnedWishes, tokenData: TokenData) {
		try {
			let step1 = await userDaoV1.findUserById(tokenData.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			let step2 = await wishesDaoV1.findWishById(params.subjectId);
			if (!step2) return Promise.reject(MESSAGES.ERROR.WISH_NOT_FOUND);
			params.userId = tokenData.userId
			let step4;
			if (params.type == ACTION_TYPE.ADD) {
				let step3 = await baseDao.findOne("pinned_wishes", { userId: params.userId, subjectId: params.subjectId, status: { '$ne': STATUS.DELETED } });
				if (step3) return Promise.reject(MESSAGES.ERROR.WISH_ALREADY_ADDED);
				const myReportedWishes = await reportDaoV1.reportedWishes(REPORT_TYPE.WISH,tokenData.userId);
				const pinnedCount = await wishesDaoV1.pinnedWishCount(params, myReportedWishes);
				// let count = await baseDao.countDocuments("pinned_wishes", { userId: params.userId, status: { '$ne': STATUS.DELETED } });
				if (pinnedCount >= 8) return Promise.reject(MESSAGES.ERROR.LIMIT_EXCEEDED);
				step4 = await wishesDaoV1.addPinnedWishes(params); // add-pinned-wishes			
			} else {
				step4 = await wishesDaoV1.removePinnedWishes(params); //remove-pinned-wishes
				if (!step4) return Promise.reject(MESSAGES.ERROR.WISH_DOES_NOT_EXIST);
			}
			return MESSAGES.SUCCESS.WISH_LIST(step4);
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function getPinnedWishes
	 * @description here are getting the pinned-wishes.
	 */
	async getPinnedWishes(params: ListingRequest, tokenData: TokenData) {
		try {
			let step1 = await userDaoV1.findUserById(tokenData.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			let step2 = await reportDaoV1.reportedWishes(REPORT_TYPE.WISH,tokenData.userId)
			let step3 = await wishesDaoV1.getPinnedWishes(tokenData.userId, step2);
			return MESSAGES.SUCCESS.DETAILS(step3);
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function deleteWish
	 * @description here are deleting the wishes.
	 */
	async deleteWish(params: WishesRequest.DeleteWish, tokenData: TokenData) {
		try {
			let step1 = await userDaoV1.findUserById(tokenData.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			let step2 = await wishesDaoV1.findWishById(params.wishId);
			if (!step2) {
				return Promise.reject(MESSAGES.ERROR.WISH_NOT_FOUND);
			} else if ((step2.userId == tokenData.userId) || (step2.hostId == tokenData.userId)) {
				let userLocationData = await this.getLocation(tokenData.userId, params);
				params["location"] = userLocationData;
				if (params.status) {
					await baseDao.updateOne("wishes", { _id: toObjectId(params.wishId) }, { '$set': { status: STATUS.CLOSED, closeDate: new Date() } }, {});
					await baseDao.updateMany("pinned_wishes", { subjectId: toObjectId(params.wishId) }, { '$set': { status: STATUS.CLOSED } }, {});
					if (params.location) {
						await commonControllerV1.deleteLocation(params, GEO_LOCATION_TYPE.WISH, step2._id, tokenData.userId);
					}
					return MESSAGES.SUCCESS.CLOSE_WISH;
				} else {
					await baseDao.updateOne("wishes", { _id: toObjectId(params.wishId) }, { '$set': { status: STATUS.DELETED } }, {});
					await baseDao.updateMany("tagwishes", { wishId: toObjectId(params.wishId) }, { '$set': { status: STATUS.DELETED } }, {});
					await baseDao.updateMany("pinned_wishes", { subjectId: toObjectId(params.wishId) }, { '$set': { status: STATUS.DELETED } }, {});
					await baseDao.updateMany("perform_blessings", { wishId: toObjectId(params.wishId) }, { '$set': { status: STATUS.DELETED } }, {});
					await baseDao.updateMany("gratitudes", { wishId: toObjectId(params.wishId) }, { '$set': { status: STATUS.DELETED } }, {});
					if(step1.wishCount > 0) {
						await baseDao.updateOne("users", { _id: step2.userId }, { $inc: { wishCount: -1 } }, {});
					}
					if (params.location) {
						await commonControllerV1.deleteLocation(params, GEO_LOCATION_TYPE.WISH, step2._id, tokenData.userId);
					}
					return MESSAGES.SUCCESS.DELETE_WISH;
				}
			} else {
				return Promise.reject(MESSAGES.ERROR.SOMETHING_WENT_WRONG);
			}
		} catch (error) {
			throw error;
		}

	}
	/**
	 * @function editWish
	 * @description here are editing the wishes.
	 */
	async editWish(params: WishesRequest.EditWish, tokenData: TokenData) {
		try {
			let step1 = await userDaoV1.findUserById(tokenData.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			let step2 = await wishesDaoV1.findWishById(params.wishId);
			if (!step2) {
				return Promise.reject(MESSAGES.ERROR.WISH_NOT_FOUND);
			} 
			let step3 = await baseDao.findOne("tagwishes", { userId: toObjectId(tokenData.userId), wishId:toObjectId(params.wishId), type: WISH_TAG_TYPE.COHOST });
			if ((step2.userId == tokenData.userId) || (step2.hostId == tokenData.userId) || (step3)) {
				//remove tagged users
				let obj = {}
				if (params.description) obj['description'] = params.description
				if (params.image) obj['image'] = params.image
				if (params.intension) obj['intension'] = params.intension
				if(params.removeCommunityId){
					await baseDao.updateOne("wishes", { _id: toObjectId(params.wishId) }, { "$pull":{communityId:{'$in':params.removeCommunityId}}, '$set': obj }, {});
				}else{
					await baseDao.updateOne("wishes", { _id: toObjectId(params.wishId) }, { '$set': obj }, {});
				}
				//await baseDao.updateOne("wishes", { _id: toObjectId(params.wishId) }, { '$set': obj }, {});
				let removeCommunityUsers;
				// if(params.removeCommunityId){
				//     removeCommunityUsers = await baseDao.find("tagwishes", {'wishId':toObjectId(params.wishId),'communityId': {'$in':params.removeCommunityId}, status:{'$nin':[STATUS.DELETED,STATUS.REJECTED]}  }, {_id:1});
				// 	console.log(removeCommunityUsers,'============================================removeCommunityUsers')
				// 	console.log(removeCommunityUsers.length,'================================================removeCommunityUsers.length')
				// 	return


					
				// 	let communityUsers = await baseDao.find("tagwishes", {'wishId':toObjectId(params.wishId),'communityId': {'$in':params.removeCommunityId}, status:{'$nin':[STATUS.DELETED,STATUS.REJECTED]}  }, {_id:1,wishId:1,userId:1, isDuplicate:1,isDuplicateExist:1});
				// 	console.log(communityUsers,'===================================================communityUsers')
				// 	console.log(communityUsers.length,'===================================================communityUsers.length')
				// 	for (const obj1 of communityUsers) {
				// 		if(obj1['isDuplicateExist']){
				// 			let data = await baseDao.find("tagwishes", {userId:toObjectId(obj1['userId']),'wishId':toObjectId(obj1['wishId']), status:{'$nin':[STATUS.DELETED,STATUS.REJECTED]}  }, {_id:1});
				// 			console.log(data,'=============================================data')
				// 			console.log(data.length,'=============================================data.length')
				// 			let updatedData = await baseDao.updateMany("tagwishes", { _id:{'$in':data}}, {'$set': { isDuplicateExist:false,isDuplicate:false } }, {});
				// 		}
				// 	}
			    // }
				if(params.removeCommunityId){
					const removeCommunityId = params.removeCommunityId.map(id => toObjectId(id));
				    removeCommunityUsers = await baseDao.find("tagwishes", {'wishId':toObjectId(params.wishId),'communityIdList': {'$all':removeCommunityId}, status:{'$nin':[STATUS.DELETED,STATUS.REJECTED]}  }, {_id:1});
					// console.log(removeCommunityUsers,'============================================removeCommunityUsers')
					// console.log(removeCommunityUsers.length,'================================================removeCommunityUsers.length')
					for(let id of removeCommunityUsers) {
						const pullCommunity = await baseDao.findOneAndUpdate("tagwishes", { _id: id._id }, { $pull: { communityIdList: { $in : removeCommunityId } } }, { returnOriginal: false });
						// console.log(pullCommunity,'=====================================pullCommunity')
						if(pullCommunity && pullCommunity.communityIdList.length == 0) {
							// await baseDao.deleteOne("tagwishes", { _id: pullCommunity._id });
							await baseDao.updateMany("tagwishes", { _id: pullCommunity._id }, { status: STATUS.DELETED }, {});
						}
					}
					// await baseDao.updateMany("tagwishes", { wishId: toObjectId(params.wishId), communityIdList: { $all: removeCommunityId }, status: { $nin: [STATUS.DELETED,STATUS.REJECTED] } }, { $pull: { communityIdList: { $in : removeCommunityId } } }, {});
					
					// return

					let communityUsers = await baseDao.find("tagwishes", {'wishId':toObjectId(params.wishId),'communityIdList': {'$all':removeCommunityId}, status:{'$nin':[STATUS.DELETED,STATUS.REJECTED]}  }, {_id:1,wishId:1,userId:1, isDuplicate:1,isDuplicateExist:1});
					// console.log(communityUsers,'===================================================communityUsers')
					// console.log(communityUsers.length,'===================================================communityUsers.length')
					// return
					for (const obj1 of communityUsers) {
						if(obj1['isDuplicateExist']){
							let data = await baseDao.find("tagwishes", {userId:toObjectId(obj1['userId']),'wishId':toObjectId(obj1['wishId']), status:{'$nin':[STATUS.DELETED,STATUS.REJECTED]}  }, {_id:1});
							// console.log(data,'=============================================data')
							// console.log(data.length,'=============================================data.length')
							let updatedData = await baseDao.updateMany("tagwishes", { _id:{'$in':data}}, {'$set': { isDuplicateExist:false,isDuplicate:false } }, {});
						}
					}
			    }
				// return
				// if(removeCommunityUsers){
				// 	await baseDao.updateMany("tagwishes", { _id: { '$in': removeCommunityUsers } }, { '$set': { status: STATUS.DELETED } }, {});
				// }
				let registeredUser = [], nonRegisteredUser = [];
				if (params.removeTagContacts && params.removeTagContacts.length !== 0) {
					for (const obj1 of params.removeTagContacts) {
						if(obj1['userId']){
							let mainData = await baseDao.findOne("tagwishes", {userId:toObjectId(obj1['userId']),'wishId':toObjectId(params.wishId),"type":WISH_TAG_TYPE.CONTACTS, status:{'$nin':[STATUS.DELETED,STATUS.REJECTED]},communityId:{'$exists':false}  }, {});
						    if(mainData['isDuplicateExist']){
							    let data = await baseDao.find("tagwishes", {userId:toObjectId(mainData['userId']),'wishId':toObjectId(params.wishId), status:{'$nin':[STATUS.DELETED,STATUS.REJECTED]}  }, {_id:1});
							    let updatedData = await baseDao.updateMany("tagwishes", { _id:{'$in':data}}, {'$set': { isDuplicateExist:false,isDuplicate:false } }, {});
						    }
					    }
					}
					params.removeTagContacts.forEach(element => {
						if (element['isAppUser']) {
							registeredUser.push(element['userId'])
						} else {
							nonRegisteredUser.push(element['phoneNumber'])
						}
					});
				}
				let users1 = await baseDao.find("tagwishes", { wishId: params.wishId, userId: { '$in': registeredUser }, type: WISH_TAG_TYPE.CONTACTS }, { _id: 1 });
				let users2 = await baseDao.find("tagwishes", { wishId: params.wishId, phoneNumber: { '$in': nonRegisteredUser }, type: WISH_TAG_TYPE.INVITED }, { _id: 1 });
				let users3 = await baseDao.find("tagwishes", { wishId: params.wishId, userId: { '$in': params.removeCohosts }, type: WISH_TAG_TYPE.COHOST }, { _id: 1 });
				if(params.removeCohosts && params.removeCohosts.length !== 0){
					for (let obj of params.removeCohosts) {
						let mainData = await baseDao.findOne("tagwishes", {userId:toObjectId(obj.toString()),'wishId':toObjectId(params.wishId),"type":WISH_TAG_TYPE.COHOST, status:{'$nin':[STATUS.DELETED,STATUS.REJECTED]} }, {}); 
					    if(mainData['isDuplicateExist']){
						    let data = await baseDao.find("tagwishes", {userId:toObjectId(mainData['userId']),'wishId':toObjectId(params.wishId), status:{'$nin':[STATUS.DELETED,STATUS.REJECTED]}  }, {_id:1});
						    let updatedData = await baseDao.updateMany("tagwishes", { _id:{'$in':data}}, {'$set': { isDuplicateExist:false,isDuplicate:false } }, {});
					    }
					}
				}
				let resultRepo = users1.concat(users2, users3)
				await baseDao.updateMany("tagwishes", { _id: { '$in': resultRepo },communityId:{'$exists':false} }, { '$set': { status: STATUS.DELETED } }, {});
				// members of communities added in tagged-users for wish
			    let communityUsers,duplicateData=[];
			    if(params.addCommunityId){
					await baseDao.updateOne("wishes", { _id: toObjectId(params.wishId) }, { "$push":{communityId:params.addCommunityId} }, {});  // add communityId in wishes table
				    communityUsers = await baseDao.find("members", {'communityId': {'$in':params.addCommunityId}, status:{'$in':[STATUS.ACTIVE,STATUS.PENDING]}, userId: { $exists: true }  }, {});
					// console.log(communityUsers,'==============================================communityUsers')
					// console.log(communityUsers.length,'==============================================communityUsers.length')
					if(communityUsers){
						let tagCommunityContact = await baseDao.find("tagwishes", {'wishId':toObjectId(params.wishId),status:{'$nin':[STATUS.DELETED,STATUS.REJECTED]}, communityId:{'$exists':true}  }, {}); 
						// console.log(tagCommunityContact,'===================================tagCommunityContact')
						// console.log(tagCommunityContact.length,'======================================tagCommunityContact.length')
						// return
						let i =0;
						let updatedArray=[],noDuplicates=[];
						while(i < communityUsers.length){
							let currentItem = communityUsers[i];
							let isDuplicate = false;
							for (const obj2 of tagCommunityContact) {
								if(obj2['userId']) { // 8 NOV UPDATE USER ID CHECK LAST TIME OF BUILD DEPLOY
									if (currentItem['userId'].toString() === obj2['userId'].toString()) {
										isDuplicate = true;
										let obj={};
										obj2.communityIdList.push(currentItem['communityId']);
										obj['_id']=obj2['_id'];
										obj['communityIdList']= obj2['communityIdList'];
										updatedArray.push(obj);
										break;
									}
								}
								
							}
							if(!isDuplicate) noDuplicates.push(currentItem);
							i++;
						}
						// console.log(updatedArray,'==================================================updatedArray');
						// console.log(updatedArray.length,'==================================================updatedArray.length');
						// console.log(noDuplicates,'====================================================noDuplicates');
						// console.log(noDuplicates.length,'====================================================noDuplicates.length');
						// return
						if(updatedArray){
							for (let i = 0; i < updatedArray.length; i++) {
								// console.log("============================FOR LOOP=================================")
								await baseDao.updateOne("tagwishes", { _id: toObjectId(updatedArray[i]['_id']) }, { '$set': { communityIdList: updatedArray[i]['communityIdList'] }  }, {});  
							}
						}
						communityUsers = noDuplicates;
						if(communityUsers.length > 0){
							const uniqueArray = [];
					        i =0;
							// communityUsers = await this.removeDuplicate(communityUsers);
							communityUsers = await this.removeDuplicateV2(communityUsers);
							// console.log(communityUsers,'=============================================communityUsers REMOVE')
							// console.log(communityUsers.length,'=============================================communityUsers.length REMOVE')
							if(step2.userId){
								let foundObject = communityUsers.find(obj => obj.userId.toString() === step2.userId.toString());
								let index = communityUsers.findIndex(obj => obj.userId.toString() === step2.userId.toString());
								if (foundObject) {
									let removedValue = communityUsers.splice(index, 1); // Removes 1 element at the specified index - user from community which is host 
								}
							}
							// console.log(communityUsers,'=============================================communityUsers REMOVE AFTER')
							// console.log(communityUsers.length,'=============================================communityUsers.length REMOVE AFTER')
							// return
							if(step2.hostId){
								let foundObject = communityUsers.find(obj => obj.userId.toString() === step2.hostId.toString());
								let index = communityUsers.findIndex(obj => obj.userId.toString() === step2.hostId.toString());
								if (foundObject) {
									let removedValue = communityUsers.splice(index, 1); // Removes 1 element at the specified index - user from community which is host 
								}
							}
							if(params.addTagContacts){
								for (const obj1 of params.addTagContacts) {
								   for (const obj2 of communityUsers) {
									if(obj1['isAppUser']){
									   if (obj1['userId'].toString() === obj2['userId'].toString()) {
										obj1['isDuplicate'] = false;  
										obj1['isDuplicateExist']=true;
										obj2['isDuplicate'] = true;
										obj2['isDuplicateExist']=true;                              
										}
									}
								   }
							   }
							}
						 
						}
						// return
						// users which are already tagged in wish
						let tagContactExist = await baseDao.find("tagwishes", {'wishId':toObjectId(params.wishId),status:{'$nin':[STATUS.DELETED,STATUS.REJECTED]} }, {}); 
						// console.log(tagContactExist,'============================tagContactExist')
						// return
					    if(tagContactExist){
							for (const obj1 of tagContactExist) {
							   for (const obj2 of communityUsers) {
									if(obj1['userId']) { // 8 NOV UPDATE USER ID CHECK LAST TIME OF BUILD DEPLOY
										if (obj1['userId'].toString() === obj2['userId'].toString()) {
											// obj1['isDuplicate'] = false;  
											// obj1['isDuplicateExist']=true;
											obj2['isDuplicate'] = true;
											obj2['isDuplicateExist']=true;                              
										}
									}
								   
							   }
						   }
		
						}
						if(params.addCohosts){
							for (const obj1 of params.addCohosts) {
							   for (const obj2 of communityUsers) {
								   if (obj1.toString() === obj2['userId'].toString()) {
									//obj1['isDuplicate'] = true;
									obj2['isDuplicate'] = true;
									obj2['isDuplicateExist']=true;
									duplicateData.push(obj1);
								   }
							   }
						   }
					
				    	}
					}
			    }
				// return

				if(params.addTagContacts){
					  // community already exists
					  let data = await baseDao.find("tagwishes", {'wishId':toObjectId(params.wishId), status:{'$nin':[STATUS.DELETED,STATUS.REJECTED]},communityId:{'$exists':true}}, {});
					  let communityData=[];
					  if(data){
					   for (const obj1 of params.addTagContacts) {
						   for (const obj2 of data) {
								if(obj2['userId'] && obj1['isAppUser']) { // 8 NOV UPDATE USER ID CHECK LAST TIME OF BUILD DEPLOY
									if (obj1['userId'].toString() === obj2['userId'].toString()) {
										obj1['isDuplicate'] = false;  
										obj1['isDuplicateExist']=true;
									   	communityData.push(obj2['_id'])                          
									}
								}
							   
						   }
						  }
					   if(communityData) await baseDao.updateMany("tagwishes", { _id: { '$in': communityData }}, { '$set': { isDuplicate: true, isDuplicateExist: true } }, {});
					 }
				}
				if(params.addCohosts){
					 // community already exists
					 let data = await baseDao.find("tagwishes", {'wishId':toObjectId(params.wishId), status:{'$nin':[STATUS.DELETED,STATUS.REJECTED]},communityId:{'$exists':true}}, {});
					 let communityData=[];
					 if(data){
					   for (const obj1 of params.addCohosts) {
						   for (const obj2 of data) {
								if(obj2['userId']) { // 8 NOV UPDATE USER ID CHECK LAST TIME OF BUILD DEPLOY
									if (obj1.toString() === obj2['userId'].toString()) {
										duplicateData.push(obj1);
										communityData.push(obj2['_id'])                          
									   }
								}
							   
						   }
						  }
						 if(communityData) await baseDao.updateMany("tagwishes", { _id: { '$in': communityData }}, { '$set': { isDuplicate: true, isDuplicateExist: true } }, {});
					   
					 }

				}


				if(!params.addTagContacts){
					params.addTagContacts = []
				}
				//add tagged users
				let contactArray = [];
				let pushArray = [];
			    if(communityUsers && communityUsers.length !== 0){
				    for (let i = 0; communityUsers.length > i; i++) {
					    let object={};
					   // if(communityUsers[i].type == WISH_TAG_TYPE.CONTACTS){
							if(communityUsers[i].type == WISH_TAG_TYPE.CONTACTS || communityUsers[i].type == WISH_TAG_TYPE.CREATOR){
					
					    	object['userId']=communityUsers[i].userId.toString();
		    				object['communityId']=communityUsers[i].communityId;
						//	object['contactId']=communityUsers[i].contactId;
			    			object['isAppUser']=true;
							object['status']=communityUsers[i].status;
							object['created']=Date.now;
					        object['createdAt']=new Date(Date.now());
							object['communityIdList'] = communityUsers[i].communityIdList;
							if(communityUsers[i].isDuplicateExist){
								object['isDuplicate']=communityUsers[i].isDuplicate;
								object['isDuplicateExist']=communityUsers[i].isDuplicateExist;
							}else{
								object['isDuplicate']=false;
								object['isDuplicateExist']=false;
							}
				     		params.addTagContacts.push(object);
							// contactArray.push(object);
    					}else{
	    					object['phoneNumber']=communityUsers[i].phoneNumber;
		    				object['countryCode']=communityUsers[i].countryCode;
			    			object['communityId']=communityUsers[i].communityId;
							if(communityUsers[i].contactId) object['contactId']=communityUsers[i].contactId;
				    		object['isAppUser']=false;
							object['status']=communityUsers[i].status;
							object['created']=Date.now;
					        object['createdAt']=new Date(Date.now());
							object['communityIdList'] = communityUsers[i].communityIdList;
					    	params.addTagContacts.push(object);
							//contactArray.push(object);
				    	}	
				    }
			    }
				registeredUser = [], nonRegisteredUser = [];
				if (params.addTagContacts && params.addTagContacts.length !== 0) {
					params.addTagContacts.forEach(element => {
						if (element['isAppUser']) {
							//registeredUser.push(element['userId'])
							registeredUser.push(element);
						} else {
							//nonRegisteredUser.push({ phoneNumber: element['phoneNumber'], countryCode: element['countryCode'],contactId: element['contactId']  })
							nonRegisteredUser.push(element);
						}
					});
					if (registeredUser && registeredUser.length !== 0) {
						for (let i = 0; registeredUser.length > i; i++) {
							let obj = {};
							obj['userId'] = toObjectId(registeredUser[i].userId)
							obj['wishId'] = step2._id
							obj['type'] = WISH_TAG_TYPE.CONTACTS;
							obj['status'] = STATUS.ACTIVE;
							obj['created']=Date.now;
					        obj['createdAt']=new Date(Date.now());
							if(registeredUser[i].status){
								obj['status'] = registeredUser[i].status;
							}else{
								obj['status'] = STATUS.ACTIVE;
							}
							obj['created']=Date.now;
							if(registeredUser[i].communityIdList) obj['communityIdList'] = registeredUser[i].communityIdList;
							if(registeredUser[i].isDuplicateExist){
								obj['isDuplicate']=registeredUser[i].isDuplicate;
								obj['isDuplicateExist']=registeredUser[i].isDuplicateExist;
							}else{
								obj['isDuplicate']=false;
								obj['isDuplicateExist']=false;
							}
							if(registeredUser[i].communityId)obj['communityId']=registeredUser[i].communityId;
							contactArray.push(obj);
							if(registeredUser[i].communityId) {
								if(registeredUser[i].status && registeredUser[i].status !== STATUS.PENDING) {
									if(step2.wishType == WISH_TYPE.MYSELF) {
										pushArray.push(obj);
									} else if (step2.wishType == WISH_TYPE.OTHER && step2.status == STATUS.ACTIVE) {
										pushArray.push(obj);
									}
								}
							} else {
								if(step2.wishType == WISH_TYPE.MYSELF) {
									pushArray.push(obj);
								} else if (step2.wishType == WISH_TYPE.OTHER && step2.status == STATUS.ACTIVE) {
									pushArray.push(obj);
								}
							}
						}
					}
					if (nonRegisteredUser && nonRegisteredUser.length !== 0) {
						for (let i = 0; nonRegisteredUser.length > i; i++) {
							let obj = {};
							obj['wishId'] = step2._id;
							obj['type'] = WISH_TAG_TYPE.INVITED;
							obj['countryCode'] = nonRegisteredUser[i].countryCode;
							obj['phoneNumber'] = nonRegisteredUser[i].phoneNumber;
							obj['status'] = STATUS.ACTIVE;
							obj['created']=Date.now;
					        obj['createdAt']=new Date(Date.now());
							if(nonRegisteredUser[i].status){
								obj['status'] = registeredUser[i].status;
							}else{
								obj['status'] = STATUS.ACTIVE;
							}				
							if(nonRegisteredUser[i].isDuplicateExist){
								obj['isDuplicate']=nonRegisteredUser[i].isDuplicate;
								obj['isDuplicateExist']=nonRegisteredUser[i].isDuplicateExist;
							}else{
								obj['isDuplicate']=false;
								obj['isDuplicateExist']=false;
							}
							if(nonRegisteredUser[i].communityId)obj['communityId']=nonRegisteredUser[i].communityId;
							if(nonRegisteredUser[i].contactId)obj['contactId'] =  toObjectId(nonRegisteredUser[i].contactId);
							contactArray.push(obj);
							const deeplink = `${SERVER.APP_URL}${SERVER.API_BASE_URL}/v1/common/deeplink?type=${DEEPLINK_TYPE.SMS_INVITE}`;  // deeplinking
							let host;
							if(step2.wishType == WISH_TYPE.MYSELF){
								// await smsManager.sendMessageViaAWS(nonRegisteredUser[i].countryCode, nonRegisteredUser[i].phoneNumber, SMS_CONTENT.TAG_CONTACT_ON_WISH_FOR_SELF+deeplink)
								let userName 
								nonRegisteredUser[i].name ? userName = nonRegisteredUser[i].name : userName = 'user'
								await awsSQS.signupMagicLinkProducer({"countryCode":nonRegisteredUser[i].countryCode, "phoneNumber":nonRegisteredUser[i].phoneNumber, "SMS_CONTENT":SMS_CONTENT.DEAR_USER+userName+`\n`+SMS_CONTENT.TAG_CONTACT_ON_WISH_FOR_SELF1+`\n`+deeplink+SMS_CONTENT.WITH_GRAT+step1.name,"type":SQS_TYPES.WISH_CREATE_SMS})
								const payload1 = {
									jobName:JOB_SCHEDULER_TYPE.WISHES_REQUEST_MESSAGE,
									time: new Date(Date.now()).getTime() + 24 * 60 * 60 * 1000,
									//time: new Date(Date.now()).getTime() +   1 * 60 * 60 * 1000,
									// time: new Date(Date.now()).getTime() +   5 * 60 * 1000,
									data: { 
										"wishId": step2._id.toString(),
										"countryCode":nonRegisteredUser[i].countryCode,
										"phoneNumber":nonRegisteredUser[i].phoneNumber,
										"deeplink":"deeplink",
										"wishType":step2.wishType,
										"number": "1",
										"userName":userName,
										"sender":step1.name
									}
									};
									redisClient.createJobs(payload1);	
									const payload2 = {
										jobName:JOB_SCHEDULER_TYPE.WISHES_REQUEST_MESSAGE,
										time: new Date(Date.now()).getTime() + 72 * 60 * 60 * 1000,
										//time: new Date(Date.now()).getTime() + 2 * 60 * 60 * 1000,
										// time: new Date(Date.now()).getTime() + 10 * 60 * 1000,
										data: { 
											"wishId": step2._id.toString(),
											"countryCode":nonRegisteredUser[i].countryCode,
											"phoneNumber":nonRegisteredUser[i].phoneNumber,
											"deeplink":"deeplink",
											"wishType":step2.wishType,
											"number": "2",
											"userName":userName,
											"sender":step1.name
										}
										};
										redisClient.createJobs(payload2);	
									const payload3 = {
										jobName:JOB_SCHEDULER_TYPE.WISHES_REQUEST_MESSAGE,
										time: new Date(Date.now()).getTime() + 5 * 24 * 60 * 60 * 1000,
										//time: new Date(Date.now()).getTime() +  3 * 60 * 60 * 1000,
										// time: new Date(Date.now()).getTime() + 15 * 60 * 1000,
										data: { 
											"wishId": step2._id.toString(),
											"countryCode":nonRegisteredUser[i].countryCode,
											"phoneNumber":nonRegisteredUser[i].phoneNumber,
											"deeplink":"deeplink",
											"wishType":step2.wishType,
											"number": "3",
											"userName":userName,
											"sender":step1.name
										}
										};
										redisClient.createJobs(payload3);	
							}else{
								host = await baseDao.findOne("users", { _id: step2.hostId, status: { '$ne': STATUS.DELETED } });
								
								if(host){
									// await smsManager.sendMessageViaAWS(nonRegisteredUser[i].countryCode, nonRegisteredUser[i].phoneNumber, SMS_CONTENT.TAG_CONTACT_ON_WISH_FOR_OTHERS1+host.name+SMS_CONTENT.TAG_CONTACT_ON_WISH_FOR_OTHERS2+deeplink)
									let userName,hostName;
									nonRegisteredUser[i].name ? userName = nonRegisteredUser[i].name : userName = "user";
									host["name"] ? hostName = host["name"] : hostName = "Host"
									// await awsSQS.signupMagicLinkProducer({"countryCode":nonRegisteredUser[i].countryCode, "phoneNumber":nonRegisteredUser[i].phoneNumber, "SMS_CONTENT":SMS_CONTENT.TAG_CONTACT_ON_WISH_FOR_OTHERS1+host.name+SMS_CONTENT.TAG_CONTACT_ON_WISH_FOR_OTHERS2+deeplink,"type":SQS_TYPES.WISH_CREATE_SMS})
									await awsSQS.signupMagicLinkProducer({"countryCode":nonRegisteredUser[i].countryCode, "phoneNumber":nonRegisteredUser[i].phoneNumber, "SMS_CONTENT":SMS_CONTENT.DEAR_USER+ userName +`,\n`+SMS_CONTENT.TAG_CONTACT_ON_WISH_FOR_OTHERS1+hostName+SMS_CONTENT.TAG_CONTACT_ON_WISH_FOR_OTHERS2+ hostName + `\n`+SMS_CONTENT.WITH_GRAT+ step1.name +`\n`+deeplink, "type":SQS_TYPES.WISH_CREATE_SMS})
									const payload1 = {
										jobName:JOB_SCHEDULER_TYPE.WISHES_REQUEST_MESSAGE,
										time: new Date(Date.now()).getTime() + 24 * 60 * 60 * 1000,
										//time: new Date(Date.now()).getTime() + 1 * 60 * 60 * 1000,
										// time: new Date(Date.now()).getTime() + 5 * 60 * 1000,
										data: { 
											"wishId": step2._id.toString(),
											"countryCode":nonRegisteredUser[i].countryCode,
											"phoneNumber":nonRegisteredUser[i].phoneNumber,
											"deeplink":"deeplink",
											"wishType":step2.wishType,
											"hostName":hostName,
											"userName":userName,
											"sender":step1.name,
											"number": "1"
										}
										};
										redisClient.createJobs(payload1);	
									const payload2 = {
										jobName:JOB_SCHEDULER_TYPE.WISHES_REQUEST_MESSAGE,
										time: new Date(Date.now()).getTime() + 72 * 60 * 60 * 1000,
										//time: new Date(Date.now()).getTime() + 2 * 60 * 60 * 1000,
										// time: new Date(Date.now()).getTime() + 10 * 60 * 1000,
										data: { 
											"wishId": step2._id.toString(),
											"countryCode":nonRegisteredUser[i].countryCode,
											"phoneNumber":nonRegisteredUser[i].phoneNumber,
											"deeplink":"deeplink",
											"wishType":step2.wishType,
											"hostName":host.name?host.name: '',
											"number": "2"}
										};
										redisClient.createJobs(payload2);	
									const payload3 = {
										jobName:JOB_SCHEDULER_TYPE.WISHES_REQUEST_MESSAGE,
										time: new Date(Date.now()).getTime() + 5 * 24 * 60 * 60 * 1000,
										//time: new Date(Date.now()).getTime() + 5 * 24 * 60 * 60 * 1000,
										//time: new Date(Date.now()).getTime() + 3 * 60 * 60 * 1000,
										// time: new Date(Date.now()).getTime() + 15 * 60 * 1000,
										data: { 
											"wishId": step2._id.toString(),
											"countryCode":nonRegisteredUser[i].countryCode,
											"phoneNumber":nonRegisteredUser[i].phoneNumber,
											"deeplink":"deeplink",
											"wishType":step2.wishType,
											"hostName":host.name?host.name: '',
											"number": "3"}
										};
										redisClient.createJobs(payload3);	
								}else{
									// await smsManager.sendMessageViaAWS(nonRegisteredUser[i].countryCode, nonRegisteredUser[i].phoneNumber, SMS_CONTENT.TAG_CONTACT_ON_WISH_FOR_OTHERS1+step1.name+SMS_CONTENT.TAG_CONTACT_ON_WISH_FOR_OTHERS2+deeplink)
									let userName, hostName;
									nonRegisteredUser[i].name ? userName = nonRegisteredUser[i].name : userName = "user";
									host["name"] ? hostName = host["name"] : hostName = "Host"

									// await awsSQS.signupMagicLinkProducer({"countryCode":nonRegisteredUser[i].countryCode, "phoneNumber":nonRegisteredUser[i].phoneNumber, "SMS_CONTENT":SMS_CONTENT.TAG_CONTACT_ON_WISH_FOR_OTHERS1+step1.name+SMS_CONTENT.TAG_CONTACT_ON_WISH_FOR_OTHERS2+deeplink,"type":SQS_TYPES.WISH_CREATE_SMS})
									await awsSQS.signupMagicLinkProducer({"countryCode":nonRegisteredUser[i].countryCode, "phoneNumber":nonRegisteredUser[i].phoneNumber, "SMS_CONTENT":SMS_CONTENT.DEAR_USER+ userName +`,\n`+SMS_CONTENT.TAG_CONTACT_ON_WISH_FOR_OTHERS1+hostName+SMS_CONTENT.TAG_CONTACT_ON_WISH_FOR_OTHERS2+ hostName + `\n`+SMS_CONTENT.WITH_GRAT+ step1.name +`\n`+deeplink, "type":SQS_TYPES.WISH_CREATE_SMS})

									const payload1 = {
										jobName:JOB_SCHEDULER_TYPE.WISHES_REQUEST_MESSAGE,
										time: new Date(Date.now()).getTime() + 24 * 60 * 60 * 1000,
										//time: new Date(Date.now()).getTime() + 1 * 60 * 60 * 1000,
										// time: new Date(Date.now()).getTime() + 5 * 60 * 1000,
										data: { 
											"wishId": step2._id.toString(),
											"countryCode":nonRegisteredUser[i].countryCode,
											"phoneNumber":nonRegisteredUser[i].phoneNumber,
											"deeplink":"deeplink",
											"wishType":step2.wishType,
											"hostName":hostName,
											"userName":userName,
											"sender":step1.name,
											"number": "1"
										}
										};
										redisClient.createJobs(payload1);	
									const payload2 = {
										jobName:JOB_SCHEDULER_TYPE.WISHES_REQUEST_MESSAGE,
										time: new Date(Date.now()).getTime() + 72 * 60 * 60 * 1000,
									//	time: new Date(Date.now()).getTime() + 2 * 60 * 60 * 1000,
										// time: new Date(Date.now()).getTime() + 10 * 60 * 1000,
										data: { 
											"wishId": step2._id.toString(),
											"countryCode":nonRegisteredUser[i].countryCode,
											"phoneNumber":nonRegisteredUser[i].phoneNumber,
											"deeplink":"deeplink",
											"wishType":step2.wishType,
											"hostName":hostName,
											"userName":userName,
											"sender":step1.name,
											"number": "2"}
										};
										redisClient.createJobs(payload2);	
									const payload3 = {
										jobName:JOB_SCHEDULER_TYPE.WISHES_REQUEST_MESSAGE,
										time: new Date(Date.now()).getTime() + 5 * 24 * 60 * 60 * 1000,
										//time: new Date(Date.now()).getTime() + 3 * 60 * 60 * 1000,
										// time: new Date(Date.now()).getTime() + 15 * 60 * 1000,
										data: { 
											"wishId": step2._id.toString(),
											"countryCode":nonRegisteredUser[i].countryCode,
											"phoneNumber":nonRegisteredUser[i].phoneNumber,
											"deeplink":"deeplink",
											"wishType":step2.wishType,
											"hostName":hostName,
											"userName":userName,
											"sender":step1.name,
											"number": "3"
										}
										};
										redisClient.createJobs(payload3);	
								}
							}						}
					}
				}
				if (params.addCohosts && params.addCohosts.length !== 0) {
					let cohostsDetails = await wishesDaoV1.getCohosts(params.addCohosts);
					if(duplicateData && duplicateData.length !== 0){
						for (const obj1 of cohostsDetails) {
							for (const obj2 of duplicateData) {
								if (obj1['_id'].toString() === obj2.toString()) {
								 obj1['isDuplicate'] = false;  // original data            
								 obj1['isDuplicateExist'] = true; // duplicate of this data exist  
	
								}
							}
						}
					}
					for (let i = 0; cohostsDetails.length > i; i++) {
						let obj = {};
						obj['userId'] = toObjectId(cohostsDetails[i]._id);
						obj['wishId'] = step2._id;
						obj['type'] = WISH_TAG_TYPE.COHOST;
						obj['status'] = STATUS.ACTIVE;
						obj['created']=Date.now;
					    obj['createdAt']=new Date(Date.now());
						if(cohostsDetails[i].isDuplicateExist){
							obj['isDuplicate']=cohostsDetails[i].isDuplicate;
							obj['isDuplicateExist']=cohostsDetails[i].isDuplicateExist;
						}else{
							obj['isDuplicate']=false;
							obj['isDuplicateExist']=false;
						}
						contactArray.push(obj);
						pushArray.push(obj);
					}
				}
				if (contactArray) await wishesDaoV1.addTagWishes(contactArray);
				step2.description = params?.description ? params.description : step2.description;
				params.image = params?.image ? params.image : step2.image;
				await this.wellLogs(step2,tokenData,contactArray,params,true);
				if (pushArray.length) {
					const updatedIntension = params?.intension ? params.intension : step2.intension;
					let pushParamsContact = {"message":MESSAGES_NOTIFICATION.WISH_CREATED1+step1.name+MESSAGES_NOTIFICATION.WISH_CREATED2+updatedIntension+MESSAGES_NOTIFICATION.WISH_CREATED3, "title":TITLE_NOTIFICATION.WISH_CREATED, "type":TYPE_NOTIFICATION.WISH_CREATE}
					await notificationManager.PublishNotification(pushArray, step2._id, pushParamsContact);
				}
			} else {
				return Promise.reject(MESSAGES.ERROR.SOMETHING_WENT_WRONG);
			}
			return MESSAGES.SUCCESS.EDIT_WISH;
		} catch (error) {
			throw error;
		}

	}

	/**
	 * @function editWishDetail
	 * @description here are getting the wish-detail.
	 */
	async editWishDetail(params: WishesRequest.EditWishDetail, tokenData: TokenData) {
		try {
			let step1 = await userDaoV1.findUserById(tokenData.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			let step2 = await wishesDaoV1.editWishDetail(params);
			let step3 = await wishesDaoV1.findTaggedUserDetail(params, tokenData.userId);
			// let step4 = await wishesDaoV1.findTaggedWish(params);
			step2[0].users = step3;
			// step2[0].showOnlyRallySupport = false;
			// if(step4 == 0) step2[0].showOnlyRallySupport = true;
			return MESSAGES.SUCCESS.WISH_DETAIL(step2[0]);
		} catch (error) {
			throw error;
		}
	}
	/**
	 * @function acceptRejectWish
	 * @description for accept/reject the wishes by host.
	 */
	async acceptRejectWish(params: WishesRequest.AcceptReject, tokenData: TokenData) {
		try {
			let step1 = await userDaoV1.findUserById(tokenData.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			let step2 = await wishesDaoV1.findWishById(params.wishId);
			//if (!step2) return Promise.reject(MESSAGES.ERROR.WISH_NOT_FOUND);
			 if (!step2) {
				return Promise.reject(MESSAGES.ERROR.WISH_NOT_FOUND);
			} else if (step2.hostId == tokenData.userId) {
				if (params.type == ACTION_TYPE.REJECT) {
					await baseDao.updateOne("wishes", { _id: toObjectId(params.wishId) }, { '$set': { status: STATUS.REJECTED, rejectReason: params.rejectReason } }, {});
					let pushParams = {"message":MESSAGES_NOTIFICATION.WISH_REQUEST_DECLINED, "title":TITLE_NOTIFICATION.WISH_REQUEST_DECLINED, "type":TYPE_NOTIFICATION.WISH_REQUEST_DECLINED};
					await notificationManager.CommonublishNotification([step2.userId], params.wishId, pushParams);
				   
				} else {
					let updateData = {}
					updateData['status'] = STATUS.ACTIVE;
					//await baseDao.updateOne("wishes", { _id: toObjectId(params.wishId) }, { '$set': { status: STATUS.ACTIVE } }, {});
					await baseDao.updateOne("wishes", { _id: toObjectId(params.wishId) }, { '$set': updateData }, {});

					// let pushParams = {"message":MESSAGES_NOTIFICATION.WISH_REQUEST_ACCEPTED, "title":TITLE_NOTIFICATION.WISH_REQUEST_ACCEPTED, "type":TYPE_NOTIFICATION.WISH_REQUEST_ACCEPTED}
					// await notificationManager.CommonublishNotification([step2.userId], params.wishId, pushParams);
					
					// SEND NOTIFICATION TO TAG CONTACTS
					// const tagContacts = await baseDao.find("tagwishes", { wishId: toObjectId(params.wishId), type: WISH_TAG_TYPE.CONTACTS, status: STATUS.ACTIVE }, { userId: 1 });

					// let host = await baseDao.findOne("users", { _id: step2.hostId, status: { '$ne': STATUS.DELETED } });
					// let pushParamsContact = {"message":MESSAGES_NOTIFICATION.WISH_CREATED1+host.name+MESSAGES_NOTIFICATION.WISH_CREATED2+step2.intension+MESSAGES_NOTIFICATION.WISH_CREATED3, "title":TITLE_NOTIFICATION.WISH_CREATED, "type":TYPE_NOTIFICATION.WISH_CREATE}
					// if(tagContacts.length) {
					// 	await notificationManager.PublishNotification(tagContacts, step2._id, pushParamsContact);
					// }

					let pushParams = {"message":MESSAGES_NOTIFICATION.WISH_REQUEST_ACCEPTED, "title":TITLE_NOTIFICATION.WISH_REQUEST_ACCEPTED, "type":TYPE_NOTIFICATION.WISH_REQUEST_ACCEPTED};
					await notificationManager.CommonublishNotification([step2.userId], params.wishId, pushParams);
				}
			 } else {
				return Promise.reject(MESSAGES.ERROR.SOMETHING_WENT_WRONG);
			 }
			return MESSAGES.SUCCESS.WISH_UPDATED;
		} catch (error) {
			throw error;
		}

	}
	/**
	 * @function wishRequest
	 * @description here are getting the wish-request.
	 */
	async wishRequest(params: WishesRequest.WishList, tokenData: TokenData) {
		try {
			let step1 = await userDaoV1.findUserById(params.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			let step2 = await wishesDaoV1.wishRequestList(params);
			return MESSAGES.SUCCESS.WISH_LIST(step2);
		} catch (error) {
			throw error;
		}
	}
	/**
		 * @function removeWish
		 * @description here are remove the wishes.
		 */
	async removeWish(params: WishesRequest.RemoveWish, tokenData: TokenData) {
		try {
			let step1 = await userDaoV1.findUserById(tokenData.userId);
			if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
			let step2 = await wishesDaoV1.findWishById(params.wishId);
			if (!step2) {
				return Promise.reject(MESSAGES.ERROR.WISH_NOT_FOUND);
			} else {
				if (step2.isGlobalWish) {
					await baseDao.updateOne("wishes", { _id: toObjectId(params.wishId) }, { '$push': { userRemovedWish: toObjectId(tokenData.userId) } }, {});
				} else {
					await baseDao.updateOne("tagwishes", { wishId: toObjectId(params.wishId), userId: toObjectId(tokenData.userId) }, { '$set': { status: STATUS.DELETED } }, {});
				}
				await baseDao.updateOne("favourites", { wishId: toObjectId(params.wishId), userId: toObjectId(tokenData.userId) }, { status: STATUS.DELETED }, {});
				await baseDao.deleteMany("pinned_wishes", { subjectId: toObjectId(params.wishId), userId: toObjectId(tokenData.userId) });
			}
			return MESSAGES.SUCCESS.REMOVE_WISH;
		} catch (error) {
			throw error;
		}

	}
	/**
	 * @function updateGlobalWishStatus
	 * @description update global wish status in admin
	 */
	async updateGlobalWishStatus(params: WishesRequest.WishStatus) {
		try {
			const step1 = await wishesDaoV1.findGlobalWishById(params.id);
			if(!step1) {
				return Promise.reject(MESSAGES.ERROR.WISH_DOES_NOT_EXIST);
			}
			if(params.status == STATUS.ACTIVE) {
				if(step1.status == STATUS.UN_PUBLISHED && step1.isGlobalWish == true) {
					const step2 = await wishesDaoV1.updateGlobalWishStatus(params);
					const users = await userDaoV1.find('users', { status: 'UN_BLOCKED' }, { _id: 1 })
					const updatedUsersArray = users.map(user => {
						return {userId: user._id};
					});
					let pushParamsContact = { "message": MESSAGES_NOTIFICATION.GLOBAL_WISH_PUBLISHED, "title": TITLE_NOTIFICATION.GLOBAL_WISH_PUBLISHED, "type": TYPE_NOTIFICATION.WISH_CREATE }
					await notificationManager.PublishNotification(updatedUsersArray, params.id, pushParamsContact)
					return MESSAGES.SUCCESS.GLOBAL_WISH_PUBLISHED;
				} else {
					return Promise.reject(MESSAGES.ERROR.WISH_PUBLISH_INVALID);
				}
			} else {
				const step2 = await wishesDaoV1.updateGlobalWishStatus(params);
				await baseDao.updateOne("wishes", { _id: toObjectId(params.id) }, { isGlobalPinned: false }, {});
				// DELETE GLOBAL WISH FROM PINNED & FAVOURITE
				await baseDao.deleteMany("pinned_wishes", { subjectId: params.id });
				await baseDao.deleteMany("favourites", { wishId: params.id });
				return MESSAGES.SUCCESS.GLOBAL_WISH_CLOSED;
			}
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function pinGlobalWish
	 * @description 
	 */
	async pinGlobalWish(params: WishesRequest.WishStatus) {
		try {
			const step1 = await wishesDaoV1.findGlobalWishById(params.id);
			if(!step1) {
				return Promise.reject(MESSAGES.ERROR.WISH_DOES_NOT_EXIST);
			}
			if(step1.status !== STATUS.ACTIVE) {
				return Promise.reject(MESSAGES.ERROR.WISH_PIN_NOT_ACTIVE);
			}
			if(params.isGlobalPinned == true) {
				const pinnedCount = await baseDao.countDocuments("wishes", { isGlobalPinned: true });
				if(pinnedCount >= GLOBAL_WISH_PIN_COUNT) {
					return Promise.reject(MESSAGES.ERROR.WISH_PIN_COUNT_EXCEED);
				}
			}
			const step2 = await wishesDaoV1.updateGlobalWishStatus(params);
			return (params.isGlobalPinned == true) ? MESSAGES.SUCCESS.GLOBAL_WISH_PINNED : MESSAGES.SUCCESS.GLOBAL_WISH_UNPINNED;
		} catch (error) {
			throw error;
		}
	}

	/**
	 * @function wishLocation
	 * @description wish location list
	 */
		async wishLocation(tokenData: TokenData,params) {
			try {
				let step1 = await userDaoV1.findUserById(tokenData.userId);
				if (!step1) return Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
				let type;
				params.type ? type = params.type : type = 0
				//filter date calculation

				let dates = await this.dateCalculation(params);

				// let locationData = await baseDao.find("locations", {}, {});
				// let wishData = [], blessData = [], gratitudeData = [];

				// await locationData.map(obj=>{
				// 	if(obj.type == "WISH"){
				// 		wishData.push(obj);
				// 	}
				// 	if(obj.type == "BLESSING"){
				// 		blessData.push(obj);
				// 	}
				// 	if(obj.type == "GRATITUDE"){
				// 		gratitudeData.push(obj);
				// 	}
				// })

				let wishData = await wishesDaoV1.wishesLocation(dates,tokenData.userId,params.type, false);
				let blessData = await blessingDaoV1.blessLocation(dates,tokenData.userId, params.type, false);
				let gratitudeData = await gratitudesDaoV1.gratitudeLocation(dates,tokenData.userId,params.type, false);
				//total bless duration
				let totalListenDurationHome = await blessingDaoV1.totalBlessDuration(dates);
				let obj = {};
				obj["totalBlessDuration"] =0;
				obj['totalwish'] = 0;
				obj['totalGratitude'] = 0;

				if(totalListenDurationHome && totalListenDurationHome.length){
				obj["totalBlessDuration"] = totalListenDurationHome[0].totalListenBlessTime;
				}
				//total wish & gratitude
				let totalwish = await wishesDaoV1.wishesLocation(dates,tokenData.userId,type, true);
				let totalGratitude =await gratitudesDaoV1.gratitudeLocation(dates,tokenData.userId,type, true);
				//most used location
				let mostUsedLocation = await commonDao.mostUsedLocation(dates);
				obj['mostUsedLocation'] = mostUsedLocation.length?mostUsedLocation[0]:"";
				obj["wishData"] = wishData;
				obj["blessData"] = blessData;
				obj["gratitudeData"] = gratitudeData;
				obj['totalwish'] = totalwish.length;
				obj['totalGratitude'] = totalGratitude.length;
				obj["circleData"] = COMPASSION_MAP;
				obj['shareLink'] =  `${SERVER.APP_URL}${SERVER.API_BASE_URL}/v1/common/deeplink?type=${DEEPLINK_TYPE.COM_MAP}`;  // deeplinking
				
				return MESSAGES.SUCCESS.DETAILS(obj);
			} catch (error) {
				throw error;
			}
		}


	   /**
         * @function wellLogs
         * @description logs maintain for well section with wish creation
         *
         */
	  async wellLogs(data,tokenData,invitedUser,params,isEdit) {
        try {
			let wellLogSave = {};
			let ids = [];
			let saveArr = [];
			if(invitedUser && invitedUser.length) {
				invitedUser.filter(invite => {
					if(!invite.communityId){
						if(invite.userId)ids.push(invite.userId);
						if(invite.contactId)ids.push(invite.contactId);
					} 
				});
			}
		   //receiver individual data save
		    if(ids && ids.length>0) {
				//ids.forEach(element => {
				for await (let element of ids) {
					wellLogSave = {};	
					wellLogSave['senderId'] = element;  //sender id means user if who will get the list 
					wellLogSave['taggedUser'] =  [toObjectId(tokenData.userId)];
					if(params.hostId)wellLogSave['hostId'] =  toObjectId(params.hostId);
					wellLogSave['wishId'] = data._id?data._id:"";
					wellLogSave['subjectId'] = data._id; //wish create
					wellLogSave['subjectDetail'] = {
						'notes': data.description?data.description:"", //description of wish,
						'type': data.wishType?data.wishType:"", //wish type
						'wishImage':params.image?params.image:""
					}
					wellLogSave['type'] = NOTIFICATION_TYPE.TAG_WISHES;
					wellLogSave['created']=Date.now();
					wellLogSave['createdAt']=new Date(Date.now());
					saveArr.push(wellLogSave);
				}
			}
			//for community well log maintain
			if(isEdit == false) {
				if(params.communityId && params.communityId.length){
					params.communityId.filter(commiunity => {
						ids.push(toObjectId(commiunity));
					});
				}
				wellLogSave = {};
				//sender object data save
				wellLogSave['senderId'] = toObjectId(tokenData.userId); //sender id means user if who will get the list  
				// if(ids)wellLogSave['receiverId'] = ids;
				if(ids)wellLogSave['taggedUser'] = ids;
				if(params.hostId) wellLogSave['hostId'] =  toObjectId(params.hostId);
				wellLogSave['wishId'] = data._id?data._id:"";
				wellLogSave['subjectId'] = data._id; //wish create
				wellLogSave['subjectDetail'] = {
					'notes': data.description?data.description:"", //description of wish,
					'type': data.wishType?data.wishType:"",      //wish type
					'wishImage':params.image?params.image:""
				}
				wellLogSave['type'] = NOTIFICATION_TYPE.CREATE_WISHES;
				wellLogSave['created']=Date.now();
				wellLogSave['createdAt']=new Date(Date.now());
				saveArr.push(wellLogSave);
			}
			try{
				await wishesDaoV1.wellLogSave(saveArr);
			}catch(err){
				console.log(err);
			}
		   	return true;
        } catch (error) {
            throw error;
        }  
    }	

	async dateCalculation(params){
		
		let dateObj ={}
		let startDate;
		switch (params.type) {
			case 1:
			startDate = moment().subtract(12,"hours");
			dateObj['startDate'] = startDate.toDate();
			break;
			case 2:
			startDate = moment().subtract(24,"hours");
			dateObj['startDate'] = startDate.toDate();
			break;
			case 3:
			startDate = moment().subtract(7,"days");
			dateObj['startDate'] = startDate.toDate();
			break;
			case 4:  
			startDate = moment().subtract(30,"days");
			dateObj['startDate'] = startDate.toDate();
			break;
		  }
		  return dateObj;
		
	}

	async getLocation(userId,params) {
		try {
		  let address;
		  let city;
		  let state;
		  let country;
		  //get the location from user
		   if(params.location){

			if (params.location.city) { address = params.location.city; }
			if (params.location.state)
			address = address + "," + params.location.state;
			city = params.location.city;
			state = params.location.state;
			country = params.location.country;

     		}else{
				const userProfileAddress = await userDaoV1.findOne("users", { _id: toObjectId(userId) }, {} );
				if (userProfileAddress.city) { 
					address = userProfileAddress.city; 
					city = userProfileAddress.city;
				}else{
					return  Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
				}
				if (userProfileAddress.state) {
					address = address + "," + userProfileAddress.state;
					state = userProfileAddress.state;
					country = userProfileAddress.country;
				}
				
		    }
	    	
		 const apiUrl = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(address)}&key=${process.env.GOOGLE_API_KEY}`;
		  return new Promise((resolve, reject) => {
			// Make the API request
			fetch(apiUrl)
			  .then((response) => response.json())
			  .then((data) => {
				// Extract latitude and longitude from the response
				
				if (data.results.length > 0) {
					if(!data.results[0].geometry.location){
						reject(MESSAGES.ERROR.LOCATION_NOT_FOUND);
						//return  Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
						
                    }
					const location = data.results[0].geometry.location;
					const latitude = location.lat;
					const longitude = location.lng;
					let retData = {
							address: city,
							coordinates: [longitude, latitude],
							city:city,
							state: state,
							country:country
					};
					resolve(retData);
			
				} else {
				 console.error("Location not found.222");
				  reject(MESSAGES.ERROR.LOCATION_NOT_FOUND);
				  //return  Promise.reject(MESSAGES.ERROR.USER_NOT_FOUND);
				  
				}
			  })
			  .catch((error) => {
				console.error("Error:", error);
				reject(MESSAGES.ERROR.LOCATION_NOT_FOUND);
				
			  });
		  });
		} catch (err) {
		    return  Promise.reject(MESSAGES.ERROR.SOMETHING_WENT_WRONG);
		}
	}

	async removeDuplicate(communityUsers){
		
		const uniqueArray = [];
		let i =0;
		while(i < communityUsers.length){
			let currentItem = communityUsers[i];
			let isDuplicate = false;
			let index=[],arr=[];
			for (let j = i+1; j < communityUsers.length; j++) {
				let uniqueItem = communityUsers[j];
				
				if(currentItem['userId'].toString() === uniqueItem['userId'].toString() ){
					isDuplicate = true;
					arr.push(uniqueItem['communityId']);
					index.push(j);
				}
			}
			if (isDuplicate) {
				arr.push(currentItem['communityId']);
				communityUsers[i]['communityIdList'] = arr;
				for (const k of index) {
					communityUsers.splice(k, 1);
				}
			}else{
				arr.push(currentItem['communityId']);
				communityUsers[i]['communityIdList'] = arr;
			}
			i++;
		}
		return communityUsers;
		
	}

	async removeDuplicateV2(communityUsers) {
		const groupedData = {};

		communityUsers.sort((a, b) => {
			if (a.status > b.status) {
			  return -1;
			} else if (a.status < b.status) {
			  return 1;
			}
			return 0;
		});

		communityUsers.forEach((item) => {
			const { _id, userId, communityId, status, creatorId, type, createdAt } = item;
			
			if (!groupedData[userId]) {
			  groupedData[userId] = { communityIdList: [] };
			}
			
			groupedData[userId].communityIdList.push(communityId);
			groupedData[userId]._id = _id;
			groupedData[userId].communityId = communityId;
			groupedData[userId].status = status;
			groupedData[userId].creatorId = creatorId;
			groupedData[userId].type = type;
			groupedData[userId].createdAt = createdAt;
		});

		const result = Object.keys(groupedData).map((userId) => ({
			_id: groupedData[userId]._id,
			userId,
			communityId: groupedData[userId].communityId,
			status: groupedData[userId].status,
			creatorId: groupedData[userId].creatorId,
			type: groupedData[userId].type,
			createdAt: groupedData[userId].createdAt,
			communityIdList: groupedData[userId].communityIdList,
		}));
		
		return result;
	}
}
export const wishesController = new WishesController();