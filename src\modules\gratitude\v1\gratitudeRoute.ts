"use strict";

import { Request, ResponseToolkit } from "@hapi/hapi";
import * as <PERSON><PERSON> from "joi";
import { failActionFunction } from "@utils/appUtils";
import { gratitudesControllerV1 } from "@modules/gratitude/index";
import {
	MESSAGES,
	SWAGGER_DEFAULT_RESPONSE_MESSAGES,
	SERVER,
	REGEX,
	ACTION_TYPE,
	STATUS,
	BLESSING_PERFORM_TYPE,
	FLAGGED_GRATITUDE_SORT_CRITERIA,
	FLAG_TYPE,
	GRATITUDE_TYPE
} from "@config/index";
import { responseHandler } from "@utils/ResponseHandler";
import { authorizationHeaderObj } from "@utils/validator";
export const gratitudesRoute = [
	{
		method: "POST",
		path: `${SERVER.API_BASE_URL}/v1/gratitude`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const payload: GratitudesRequest.Add = request.payload;
				const result = await gratitudesControllerV1.gratitudes(payload, tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		options: {
			tags: ["api", "user/gratitude"],
			description: "User perform personalised/global-gratitude on blessing",
			auth: {
				strategies: ["UserAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				payload: Joi.object({
					wishId: Joi.string().trim().regex(REGEX.MONGO_ID).required(),
					blessingId: Joi.string().trim().regex(REGEX.MONGO_ID).optional(),
					audio: Joi.string().optional().description("user bless audio"),
					audioLength:Joi.number().optional().description('length of recorded audio'),
					notes: Joi.string().optional().description("user bless text"),
					type: Joi.string().valid(BLESSING_PERFORM_TYPE.AUDIO, BLESSING_PERFORM_TYPE.TEXT).required(),
					gratitudeType:Joi.string().valid(GRATITUDE_TYPE.GLOBAL, GRATITUDE_TYPE.PERSONALISED).required(),
					location:Joi.object({
						address: Joi.string().trim().allow("").optional(),
						coordinates: Joi.array().required(),
						city:Joi.string().required(),
						country:Joi.string().required(),
						state:Joi.string().required()
					}).optional(), 

				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/admin/flagged-gratitudes`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const query: GratitudesRequest.FlaggedGratitude = request.query;
				const result = await gratitudesControllerV1.flaggedGratitudes(query);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin flagged gratitudes"],
			description: "Flagged gratitudes",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				query: Joi.object({
					pageNo: Joi.number().required(),
					limit: Joi.number().required(),
					sortCriteria: Joi.string().trim().valid(
						FLAGGED_GRATITUDE_SORT_CRITERIA.GRATITUDE_NUMBER,
						FLAGGED_GRATITUDE_SORT_CRITERIA.WISH_NUMBER,
						FLAGGED_GRATITUDE_SORT_CRITERIA.GRATITUDE_BY,
						FLAGGED_GRATITUDE_SORT_CRITERIA.GRATITUDE_DATE,
						FLAGGED_GRATITUDE_SORT_CRITERIA.STATUS
					).optional().default(FLAGGED_GRATITUDE_SORT_CRITERIA.GRATITUDE_DATE),
					sortBy: Joi.number().valid(1, 0).optional(),
					searchKey: Joi.string().optional(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/admin/flagged-gratitudes/{id}`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const params: GratitudesRequest.GratitudeId = request.params;
				const result = await gratitudesControllerV1.flaggedGratitudeDetail(params);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin flagged gratitudes"],
			description: "Flagged gratitude detail",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				params: Joi.object({
					id: Joi.string().required(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "GET",
		path: `${SERVER.API_BASE_URL}/v1/admin/flagged-gratitude-reports`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const query: GratitudesRequest.FlaggedGratitude = request.query;
				const result = await gratitudesControllerV1.flaggedGratitudeReports(query);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin flagged gratitudes"],
			description: "Flagged gratitude reports",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				query: Joi.object({
					gratitudeId: Joi.string().required(),
					pageNo: Joi.number().required(),
					limit: Joi.number().required(),
					sortCriteria: Joi.string().trim().valid("createdAt").optional(),
					sortBy: Joi.number().valid(1, 0).optional(),
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	},
	{
		method: "PUT",
		path: `${SERVER.API_BASE_URL}/v1/admin/flagged-gratitudes/{id}`,
		handler: async (request: Request | any, h: ResponseToolkit) => {
			try {
				const tokenData: TokenData = request.auth && request.auth.credentials && request.auth.credentials.tokenData;
				const params: GratitudesRequest.UnflagDeleteGratitude = request.params;
				const payload: GratitudesRequest.UnflagDeleteGratitude = request.payload;
				const result = await gratitudesControllerV1.unflagDeleteGratitude({ ...params, ...payload }, tokenData);
				return responseHandler.sendSuccess(h, result);
			} catch (error) {
				return responseHandler.sendError(request, error);
			}
		},
		config: {
			tags: ["api", "admin flagged gratitudes"],
			description: "Unflag/Delete Gratitude",
			auth: {
				strategies: ["AdminAuth"]
			},
			validate: {
				headers: authorizationHeaderObj,
				params: Joi.object({
					id: Joi.string().required(),
				}),
				payload: Joi.object({
					status: Joi.string().required().valid(FLAG_TYPE.UN_FLAGGED, FLAG_TYPE.DELETED),
					deleteReason: Joi.string().optional().allow("")
				}),
				failAction: failActionFunction
			},
			plugins: {
				"hapi-swagger": {
					responseMessages: SWAGGER_DEFAULT_RESPONSE_MESSAGES
				}
			}
		}
	}
];