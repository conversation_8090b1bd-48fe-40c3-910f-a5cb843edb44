declare namespace ReportRequest {

    export interface ReportWish {
		reportType: string;
		description: string;
		subjectId: string;
        userId?:string;
        type?:string;
		status?:string;
		ownerId?: string;
	}
	export interface ReportCommunity {
		reportType: string;
		description: string;
		subjectId: string;
        userId?:string;
        type?:string;
		status?:string;
		ownerId?: string;
	}
	export interface Report {
		reportType: string;
		description: string;
		subjectId: string;
        userId?:string;
        type?:string;
		status?:string;
		ownerId?: string;
	}
	
	export interface ReportedPosts {
		id: string;
		pageNo: number;
		limit: number;
		sortBy?: number;
		sortCriteria?: string;
		searchKey?: string;
	}
}