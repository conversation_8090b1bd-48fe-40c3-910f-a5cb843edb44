"use strict";

const mongoose = require("mongoose");
import { Document, model, Model, Schema } from "mongoose";
import { DB_MODEL_REF } from "@config/constant";

export interface DisasterCategories extends Document {
    disasterCategory: number;
    name: string;
}

const disasterCategoriesSchema: Schema = new mongoose.Schema({
    _id: {
        type: Schema.Types.ObjectId,
        required: true,
        auto: true,
    },
    disasterCategory: {
        type: Number,
        required: true
    },
    name: {
        type: String,
        required: true,
    },
}, {
    versionKey: false,
    timestamps: true
});

// Export categories
export const disaster_categories: Model<DisasterCategories> = model<DisasterCategories>(DB_MODEL_REF.DISASTER_CATEGORIES, disasterCategoriesSchema);